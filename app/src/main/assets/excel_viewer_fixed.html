<!DOCTYPE html>
<html>
<head>
    <title>Excel Viewer</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif; 
            margin: 0; 
            padding: 8px; 
            font-size: 14px; 
            background-color: #f8f9fa; 
            display: flex;
            flex-direction: column;
            height: 100vh; 
            box-sizing: border-box; 
        }

        #sheet-nav {
             margin-bottom: 10px;
             padding-bottom: 10px;
             border-bottom: 1px solid #dee2e6; 
             white-space: nowrap; 
             overflow-x: auto;   
             flex-shrink: 0; 
        }
        .sheet-button {
            background-color: #f1f3f5; 
            border: 1px solid #ced4da; 
            padding: 6px 12px; 
            margin-right: 5px;
            cursor: pointer;
            border-radius: 4px;
            font-size: 0.9em;
            color: #495057;
        }
        .sheet-button.active {
            background-color: #e9ecef; /* Slightly darker active background */
            font-weight: bold;
            border-color: #adb5bd;
            color: #212529;
        }

        #output {
            overflow: auto; 
            border: 1px solid #dee2e6; 
            margin-top: 5px; 
            background-color: #fff; 
            flex-grow: 1;
            min-height: 150px;
        }

        table {
            border-collapse: collapse;
            width: 100%; 
            font-size: 0.9em; 
            table-layout: auto; 
        }

        th, td {
            border: 1px solid #e9ecef; 
            padding: 8px 10px; 
            text-align: left;
            vertical-align: top; 
            white-space: nowrap; 
        }

        th {
            background-color: #f8f9fa; 
            font-weight: bold;
            color: #495057;
            position: sticky;
            top: 0; 
            z-index: 1; 
            border-bottom: 2px solid #dee2e6;
        }

        tbody tr:nth-child(odd) {
            background-color: #fdfdfe; 
        }
        tbody tr:hover {
            background-color: #e9ecef;
        }

        #error {
            flex-shrink: 0; /* Prevent error div from shrinking */
            display: none; /* Hide the error div */
            color: red;
            font-weight: bold;
            margin-top: 10px;
            padding: 8px;
            background-color: #ffebee;
            border: 1px solid #ef9a9a;
            border-radius: 4px;
        }
    </style>
</head>
<body>
<div id="sheet-nav"></div>
<div id="output">Loading...</div>
<div id="error"></div>

<script>
    let currentWorkbook = null;

    function displayExcelData(base64String) {
        clearError();
        if (typeof XLSX === 'undefined') {
            showError("SheetJS library (XLSX) is not loaded or failed to load.");
            clearDisplay();
            return;
        }
        try {
            // 添加中文编码支持
            currentWorkbook = XLSX.read(base64String, { 
                type: 'base64',
                codepage: 936  // 添加GBK/GB2312编码支持
            });
            const sheetNames = currentWorkbook.SheetNames;
            const navContainer = document.getElementById('sheet-nav');
            navContainer.innerHTML = '';
            if (sheetNames.length === 0) {
                showError("The Excel file contains no sheets.");
                clearDisplay();
                return;
            }
            sheetNames.forEach((sheetName, index) => {
                const button = document.createElement('button');
                button.innerText = sheetName;
                button.className = 'sheet-button';
                button.setAttribute('data-sheetname', sheetName);
                button.onclick = function() {
                    displaySheet(this.getAttribute('data-sheetname'));
                    setActiveButton(this);
                };
                navContainer.appendChild(button);
                if (index === 0) {
                     button.classList.add('active');
                }
            });
            displaySheet(sheetNames[0]);
        } catch (error) {
            console.error("Error processing Excel data:", error);
            showError("Failed to parse or display Excel data: " + error.message);
            clearDisplay();
             currentWorkbook = null;
        }
    }

    function displaySheet(sheetName) {
         if (!currentWorkbook) {
            showError("Workbook data is not available.");
            clearDisplay();
            return;
         }
         try {
             const worksheet = currentWorkbook.Sheets[sheetName];
             if (!worksheet) {
                showError(`Sheet "${sheetName}" not found in the workbook.`);
                clearDisplay();
                return;
             }
             // Specify { "id": "excelTable" } so CSS targets the generated table if needed
             const htmlTable = XLSX.utils.sheet_to_html(worksheet, { id: "excelTable" });
             document.getElementById('output').innerHTML = htmlTable;
         } catch(error) {
             console.error(`Error displaying sheet "${sheetName}":`, error);
             showError(`Failed to display sheet "${sheetName}": ${error.message}`);
             clearDisplay();
         }
    }

    function setActiveButton(activeButton) {
        const buttons = document.querySelectorAll('#sheet-nav .sheet-button');
        buttons.forEach(button => button.classList.remove('active'));
        if(activeButton) activeButton.classList.add('active');
    }

    function clearDisplay() {
         document.getElementById('output').innerHTML = "";
         document.getElementById('sheet-nav').innerHTML = "";
    }

    function showError(message) {
        document.getElementById('error').innerText = message;
        console.error(message);
    }
    function clearError() {
        document.getElementById('error').innerText = "";
    }

    function libraryLoadError() {
         console.error("Failed to load SheetJS library from script tag.");
         showError("Error: Could not load the Excel viewing library (xlsx.full.min.js). Check file path or network connection if using CDN.");
         clearDisplay();
    }

    function initApp() {
        console.log("SheetJS library loaded successfully via script tag.");
        if (window.AndroidExcelInterface && typeof window.AndroidExcelInterface.getExcelDataAsBase64 === 'function') {
            console.log("Requesting Excel data from native interface...");
            window.AndroidExcelInterface.getExcelDataAsBase64();
        } else {
            showError("Native interface (AndroidExcelInterface) not available. Cannot load data.");
            clearDisplay();
        }
    }
</script>

<script src="xlsx.full.min.js" onload="initApp()" onerror="libraryLoadError()"></script>
</body>
</html>