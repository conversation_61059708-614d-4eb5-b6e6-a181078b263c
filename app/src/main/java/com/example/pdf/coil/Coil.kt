package com.example.pdf.coil

import androidx.compose.foundation.Image
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.DefaultAlpha
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import coil3.ImageLoader
import coil3.compose.rememberAsyncImagePainter
import coil3.gif.GifDecoder
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.example.pdf.appContext

private val defaultImageLoader: ImageLoader by lazy {
  ImageLoader.Builder(appContext)
    .components {
      add(GifDecoder.Factory())
    }
    .build()
}

/**
 * 封装Coil3的Image组件，支持GIF图片
 *
 * @param data 图片资源 (URL字符串, Uri, File, Drawable资源ID等)
 * @param modifier 修饰符
 * @param contentDescription 图片内容描述，用于无障碍服务
 * @param alignment 图片对齐方式
 * @param contentScale 图片缩放模式
 * @param alpha 透明度
 * @param colorFilter 颜色过滤器
 * @param enableCrossfade 是否启用渐变加载效果
 */
@Composable
fun CoilImage(
  data: Any,
  modifier: Modifier = Modifier,
  contentDescription: String? = null,
  imageLoader: ImageLoader = defaultImageLoader,
  alignment: Alignment = Alignment.Center,
  contentScale: ContentScale = ContentScale.Fit,
  alpha: Float = DefaultAlpha,
  colorFilter: ColorFilter? = null,
  enableCrossfade: Boolean = true,
  crossfadeDurationMillis: Int = 500,
) {
  val context = LocalContext.current

  // 创建图片请求
  val request = ImageRequest.Builder(context)
    .data(data)
    .apply {
      if (enableCrossfade) {
        crossfade(crossfadeDurationMillis)
      }
    }
    .build()

  // 创建图片绘制器并渲染图片
  val painter = rememberAsyncImagePainter(
    model = request,
    imageLoader = imageLoader
  )

  // 渲染图片
  Image(
    painter = painter,
    contentDescription = contentDescription,
    modifier = modifier,
    alignment = alignment,
    contentScale = contentScale,
    alpha = alpha,
    colorFilter = colorFilter
  )
}