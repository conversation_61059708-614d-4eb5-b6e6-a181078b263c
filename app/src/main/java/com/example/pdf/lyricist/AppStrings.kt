package com.example.pdf.lyricist

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.text.intl.Locale
import cafe.adriel.lyricist.LanguageTag
import cafe.adriel.lyricist.LocalStrings
import cafe.adriel.lyricist.Lyricist
import cafe.adriel.lyricist.Strings
import com.example.pdf.LocaleSupport
import com.example.pdf.lyricist.i18n.StringsEn
import kotlinx.coroutines.flow.MutableStateFlow

object Locales {
  const val EN = "en"
  const val DE = "de"
  const val FR = "fr"
  const val HK = "zh-HK"
  const val TW = "zh-TW"
  const val KO = "ko"
  const val PT = "pt"
  const val JA = "ja"
  const val ES = "es"
  const val IT = "it"
  const val ID = "id"
  const val MS = "ms"
  const val TH = "th"
  const val VI = "vi"
  const val AR = "ar"
  const val TR = "tr"
}

val runtimeLanguageTagFlow = MutableStateFlow(Locale.current.toLanguageTag())

@Composable
fun rememberStrings(
  defaultLanguageTag: LanguageTag = Locales.EN,
  currentLanguageTag: LanguageTag = Locale.current.toLanguageTag(),
): Lyricist<Strings> =
  cafe.adriel.lyricist.rememberStrings(Strings, defaultLanguageTag, currentLanguageTag)

@Composable
fun ProvideStrings(
  lyricist: Lyricist<Strings> = rememberStrings(),
  content: @Composable () -> Unit,
) {
  cafe.adriel.lyricist.ProvideStrings(lyricist, LocalStrings, content)
}

private var _globalStrings: Strings? = null

fun configureGlobalStrings(strings: Strings) {
  _globalStrings = strings
}

@Composable
fun ConfigureGlobalStringsEffect(strings: Strings) {
  LaunchedEffect(strings) {
    configureGlobalStrings(strings)
  }
}

val globalStrings get() = _globalStrings ?: StringsEn

fun matchStrings(languageTag: String): Strings {
  val compatibleLocale: java.util.Locale =
    LocaleSupport.compatibleLanguage(java.util.Locale.forLanguageTag(languageTag))
      ?: LocaleSupport.En

  return Strings[compatibleLocale.toLanguageTag()] ?: StringsEn
}

data class Strings(
  val ok: String,
  val go: String,
  val all: String,
  val home: String,
  val scan: String,
  val recent: String,
  val bookmarks: String,
  val tools: String,
  // Empty states
  val noFilesFound: String,
  val noFilesFoundWithType: String,
  val noBookmarks: String,
  val noBookmarksMessage: String,
  // Search
  val searchPlaceholder: String,
  // Settings
  val feedback: String,
  val privacyPolicy: String,
  val version: String,
  val settings: String,
  // Document actions
  val rename: String,
  val renamedSuccessfully: String,
  val cancel: String,
  val delete: String,
  val deleteConfirmSingle: String,
  val deleteConfirmMultiple: String,
  val confirmDeletion: String,
  val detail: String,
  val share: String,
  // PDF password
  val setPassword: String,
  val setPasswordDescription: String,
  val removePassword: String,
  val removePasswordDescription: String,
  val password: String,
  val enterPassword: String,
  val enterPasswordDescription: String,
  val incorrectPassword: String,
  val passwordCannotBeEmpty: String,
  val passwordSetSuccessfully: String,
  val failedToSetPassword: String,
  val passwordRemovedSuccessfully: String,
  val incorrectPasswordOrFailed: String,
  val unlock: String,
  // Success screen
  val convertedSuccessfully: String,
  val scannedSuccessfully: String,
  val lockedSuccessfully: String,
  val unlockedSuccessfully: String,
  val open: String,
  // Tools
  val import: String,
  val image: String,
  val item: String,
  val convert: String,
  val importFile: String,
  val imageToPdf: String,
  val wordToPdf: String,
  val scanToPdf: String,
  val convertToPdf: String,
  val lockPdf: String,
  val unlockPdf: String,
  val annotate: String,
  val signature: String,
  // Tips and dialogs
  val reorderTip: String,
  val dismissTips: String,
  val leaveNow: String,
  val leaveEditorWarning: String,
  val leave: String,
  val deletePageTitle: String,
  val deletePageWarning: String,
  val leaveEditModeTitle: String,
  val leaveEditModeWarning: String,
  // Document details
  val details: String,
  val fileName: String,
  val storagePath: String,
  val lastModified: String,
  val lastViewed: String,
  val noData: String,
  val fileSize: String,
  // Sort
  val sortBy: String,
  val selected: String,
  val selectAll: String,
  val sortNameAsc: String,
  val sortNameDesc: String,
  val sortDateModifiedAsc: String,
  val sortDateModifiedDesc: String,
  val sortSizeAsc: String,
  val sortSizeDesc: String,
  // Loading
  val loading: String,
  // Bottom sheet
  val confirm: String,
  // Word to PDF errors
  val failedToLoad: String,
  val savedToDocuments: String,
  val savedToInternal: String,
  val failedToSavePdf: String,
  val convertFailed: String,
  // Rating and feedback
  val feedbackGuideMessage: String,
  val skipForNow: String,
  val ratingGuideTitle: String,
  val ratingGuideSubtitle: String,
  // Permission
  val permissionRequired: String,
  val notice: String,
  // Notification permission
  val allowNotification: String,
  val notificationPermissionMessage: String,
  val grant: String,
  // Ad messages
  val adLoadingFailed: String,
  val adRewardWarning: String,
  val loadingAd: String,
  val adLabel: String,
  // Notification
  val tryNow: String,
  val notification: String,
  // Image cropper
  val rotateLeft: String,
  val rotateRight: String,
  val flipHorizontal: String,
  val flipVertical: String,
  // PDF picker
  val selectAFile: String,
  val search: String,
  val encrypted: String,
  val unencrypted: String,
  // Toast messages
  val failedToDelete: String,
  val failedToRename: String,
  val convertSuccessful: String,
  val selectedImages: String,
  val imageSelectionCancelled: String,
  val selectedImagesWithCustomPicker: String,
  val failedToLoadDocument: String,
  // Image picker
  val allImages: String,
  // Signature pad
  val color: String,
  val size: String,
  // PDF viewer
  val addSignature: String,
  val add: String,
  val highlight: String,
  val draw: String,
  val deleteSignature: String,
  // File access permission
  val fileAccessAllFilesPermissionMessage: String,
  val fileAccessStoragePermissionMessage: String,
  val fileAccessPermissionRequired: String,
  // Language
  val language: String,
  // Welcome pages
  val welcomeTitle1: String,
  val welcomeTitle2: String,
  val welcomeTitle3: String,
  val next: String,
)