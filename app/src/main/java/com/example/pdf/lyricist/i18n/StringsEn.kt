package com.example.pdf.lyricist.i18n

import cafe.adriel.lyricist.LyricistStrings
import com.example.pdf.lyricist.Locales
import com.example.pdf.lyricist.Strings

@LyricistStrings(languageTag = Locales.EN, default = true)
val StringsEn = Strings(
  ok = "OK",
  go = "Go",
  all = "All",
  home = "Home",
  scan = "Scan",
  recent = "Recent",
  bookmarks = "Bookmarks",
  tools = "Tools",
  // Empty states
  noFilesFound = "no files found",
  noFilesFoundWithType = "No %s files found",
  noBookmarks = "no bookmarks",
  noBookmarksMessage = "You haven't added any bookmarks yet.",
  // Search
  searchPlaceholder = "Search...",
  // Settings
  feedback = "Feedback",
  privacyPolicy = "Privacy Policy",
  version = "Version %s",
  settings = "Settings",
  // Document actions
  rename = "Rename",
  renamedSuccessfully = "Renamed successfully",
  cancel = "Cancel",
  delete = "Delete",
  deleteConfirmSingle = "Are you sure you want to delete this file?",
  deleteConfirmMultiple = "Are you sure you want to delete %d files?",
  confirmDeletion = "Confirm Deletion",
  detail = "Detail",
  share = "Share",
  // PDF password
  setPassword = "Set password",
  setPasswordDescription = "Create a password to protect your PDF.",
  removePassword = "Remove password",
  removePasswordDescription = "Password protection will be removed from the PDF.",
  password = "Password",
  enterPassword = "Enter Password",
  enterPasswordDescription = "This PDF is password protected. Please enter the password to open it.",
  incorrectPassword = "Incorrect password",
  passwordCannotBeEmpty = "Password cannot be empty",
  passwordSetSuccessfully = "Password set successfully",
  failedToSetPassword = "Failed to set password",
  passwordRemovedSuccessfully = "Password removed successfully",
  incorrectPasswordOrFailed = "Incorrect password or failed to remove password",
  unlock = "Unlock",
  // Success screen
  convertedSuccessfully = "Converted successfully",
  scannedSuccessfully = "Scanned successfully",
  lockedSuccessfully = "Locked successfully",
  unlockedSuccessfully = "Unlocked successfully",
  open = "Open",
  // Tools
  import = "Import",
  image = "Image",
  item = "Item",
  convert = "Convert",
  importFile = "Import File",
  imageToPdf = "Image to PDF",
  wordToPdf = "Word to PDF",
  scanToPdf = "Scan to PDF",
  convertToPdf = "Convert to PDF",
  lockPdf = "Lock PDF",
  unlockPdf = "Unlock PDF",
  annotate = "Annotate",
  signature = "Signature",
  // Tips and dialogs
  reorderTip = "Long press and drag to reorder",
  dismissTips = "dismiss tips",
  leaveNow = "Leave now?",
  leaveEditorWarning = "Your changes and images will not be saved. Are you sure you want to leave?",
  leave = "Leave",
  deletePageTitle = "Delete this page?",
  deletePageWarning = "The page deleted cannot be recovered. Are you sure you want to proceed?",
  leaveEditModeTitle = "Leave now?",
  leaveEditModeWarning = "Your changes haven't been saved yet. Do you want to save them now?",
  // Document details
  details = "Details",
  fileName = "File name",
  storagePath = "Storage path",
  lastModified = "Last modified",
  lastViewed = "Last viewed",
  noData = "--",
  fileSize = "File size",
  // Sort
  sortBy = "Sort By",
  selected = "Selected",
  selectAll = "Select All",
  sortNameAsc = "Name (A-Z)",
  sortNameDesc = "Name (Z-A)",
  sortDateModifiedAsc = "Date Modified (Oldest first)",
  sortDateModifiedDesc = "Date Modified (Newest first)",
  sortSizeAsc = "Size (Smallest first)",
  sortSizeDesc = "Size (Largest first)",
  // Loading
  loading = " Loading...",
  // Bottom sheet
  confirm = "Confirm",
  // Word to PDF errors
  failedToLoad = "Failed to load",
  savedToDocuments = "Saved to Documents folder: %s",
  savedToInternal = "Saved to internal storage: %s",
  failedToSavePdf = "Failed to save PDF: %s",
  convertFailed = "Convert failed",
  // Rating and feedback
  feedbackGuideMessage = "We are here to help and make things better for you.",
  skipForNow = "Skip for now",
  ratingGuideTitle = "Do you like using our app?",
  ratingGuideSubtitle = "Let us know with a quick rating!",
  // Permission
  permissionRequired = "Permission Required",
  notice = "Notice",
  // Notification permission
  allowNotification = "Allow Notification",
  notificationPermissionMessage = "To stay informed, please allow Notification permission.",
  grant = "Grant",
  // Ad messages
  adLoadingFailed = "Ad loading failed.",
  adRewardWarning = "If you leave before the ad end, you will not be rewarded.",
  loadingAd = "Loading ad...",
  adLabel = " AD ",
  // Notification
  tryNow = "Try now",
  notification = "Notification",
  // Image cropper
  rotateLeft = "Rotate Left",
  rotateRight = "Rotate Right",
  flipHorizontal = "Flip Horizontal",
  flipVertical = "Flip Vertical",
  // PDF picker
  selectAFile = "Select a file",
  search = "Search",
  encrypted = "Encrypted",
  unencrypted = "Unencrypted",
  // Toast messages
  failedToDelete = "Failed to delete file(s)",
  failedToRename = "Failed to rename file",
  convertSuccessful = "convert successful: %s",
  selectedImages = "Selected %d images.",
  imageSelectionCancelled = "Image selection cancelled.",
  selectedImagesWithCustomPicker = "Selected %d images with custom picker.",
  failedToLoadDocument = "Failed to load %s",
  // Image picker
  allImages = "All Images",
  // Signature pad
  color = "Color",
  size = "Size",
  // PDF viewer
  addSignature = "Add Signature",
  add = "Add",
  highlight = "Highlight",
  draw = "Draw",
  deleteSignature = "Delete Signature",
  // File access permission
  fileAccessAllFilesPermissionMessage = "To read and edit files, we need All Files Access permission. This will open system settings to allow permission.",
  fileAccessStoragePermissionMessage = "To read and edit files, we need storage access permission.",
  fileAccessPermissionRequired = "Permission is required to access all files",
  // Language
  language = "Language",
  // Welcome pages
  welcomeTitle1 = "All-in-One Document Reader",
  welcomeTitle2 = "Powerful PDF Editing Features",
  welcomeTitle3 = "Smart PDF Scanner",
  next = "Next"
)