package com.example.pdf.kermit

import co.touchlab.kermit.Logger
import com.example.pdf.BuildConfig

@Suppress("NOTHING_TO_INLINE")
inline fun debugLog(
  throwable: Throwable? = null,
  tag: String? = null,
  noinline message: () -> String
) {
  if (BuildConfig.DEBUG) {
    if (tag == null) {
      Logger.d(throwable = throwable, message = message)
    } else {
      Logger.d(throwable, tag, message)
    }
  }
}

@Suppress("NOTHING_TO_INLINE")
inline fun debugLog(
  message: String,
  throwable: Throwable? = null,
  tag: String? = null
) {
  if (BuildConfig.DEBUG) {
    if (tag == null) {
      Logger.d(throwable = throwable, message = { message })
    } else {
      Logger.d(throwable, tag) { message }
    }
  }
}