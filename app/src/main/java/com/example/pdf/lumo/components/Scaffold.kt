package com.example.pdf.lumo.components

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.calculateEndPadding
import androidx.compose.foundation.layout.calculateStartPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.SubcomposeLayout
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.offset
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.contentColorFor
import com.example.pdf.lumo.foundation.systemBarsForVisualComponents
import kotlin.jvm.JvmInline

@Composable
fun Scaffold(
    modifier: Modifier = Modifier,
    topBar: @Composable () -> Unit = {},
    bottomBar: @Composable () -> Unit = {},
    snackbarHost: @Composable () -> Unit = {},
    floatingActionButton: @Composable () -> Unit = {},
    floatingActionButtonPosition: FabPosition = FabPosition.End,
    containerColor: Color = AppTheme.colors.background,
    contentColor: Color = contentColorFor(containerColor),
    contentWindowInsets: WindowInsets = ScaffoldDefaults.contentWindowInsets,
    content: @Composable (PaddingValues) -> Unit,
) {
    Surface(modifier = modifier, color = containerColor, contentColor = contentColor) {
        ScaffoldLayout(
            fabPosition = floatingActionButtonPosition,
            topBar = topBar,
            bottomBar = bottomBar,
            content = content,
            snackbar = snackbarHost,
            contentWindowInsets = contentWindowInsets,
            fab = floatingActionButton,
        )
    }
}

@Composable
private fun ScaffoldLayout(
    fabPosition: FabPosition,
    topBar: @Composable () -> Unit,
    content: @Composable (PaddingValues) -> Unit,
    snackbar: @Composable () -> Unit,
    fab: @Composable () -> Unit,
    contentWindowInsets: WindowInsets,
    bottomBar: @Composable () -> Unit,
) {
    SubcomposeLayout { constraints ->
        val layoutWidth = constraints.maxWidth
        val layoutHeight = constraints.maxHeight

        val looseConstraints = constraints.copy(minWidth = 0, minHeight = 0)

        layout(layoutWidth, layoutHeight) {
            val topBarPlaceables =
                subcompose(ScaffoldLayoutContent.TopBar, topBar).map {
                    it.measure(looseConstraints)
                }

            val topBarHeight = topBarPlaceables.maxByOrNull { it.height }?.height ?: 0

            val snackbarPlaceables =
                subcompose(ScaffoldLayoutContent.Snackbar, snackbar).map {
                    // respect only bottom and horizontal for snackbar and fab
                    val leftInset =
                        contentWindowInsets
                            .getLeft(this@SubcomposeLayout, layoutDirection)
                    val rightInset =
                        contentWindowInsets
                            .getRight(this@SubcomposeLayout, layoutDirection)
                    val bottomInset = contentWindowInsets.getBottom(this@SubcomposeLayout)
                    // offset the snackbar constraints by the insets values
                    it.measure(
                        looseConstraints.offset(
                            -leftInset - rightInset,
                            -bottomInset,
                        ),
                    )
                }

            val snackbarHeight = snackbarPlaceables.maxByOrNull { it.height }?.height ?: 0
            val snackbarWidth = snackbarPlaceables.maxByOrNull { it.width }?.width ?: 0

            val fabPlaceables =
                subcompose(ScaffoldLayoutContent.Fab, fab).mapNotNull { measurable ->
                    // respect only bottom and horizontal for snackbar and fab
                    val leftInset =
                        contentWindowInsets.getLeft(
                            this@SubcomposeLayout,
                            layoutDirection,
                        )
                    val rightInset =
                        contentWindowInsets.getRight(
                            this@SubcomposeLayout,
                            layoutDirection,
                        )
                    val bottomInset = contentWindowInsets.getBottom(this@SubcomposeLayout)
                    measurable.measure(
                        looseConstraints.offset(
                            -leftInset - rightInset,
                            -bottomInset,
                        ),
                    )
                        .takeIf { it.height != 0 && it.width != 0 }
                }

            val fabPlacement =
                if (fabPlaceables.isNotEmpty()) {
                    val fabWidth = fabPlaceables.maxByOrNull { it.width }!!.width
                    val fabHeight = fabPlaceables.maxByOrNull { it.height }!!.height
                    // FAB distance from the left of the layout, taking into account LTR / RTL
                    val fabLeftOffset =
                        if (fabPosition == FabPosition.End) {
                            if (layoutDirection == LayoutDirection.Ltr) {
                                layoutWidth - FabSpacing.roundToPx() - fabWidth
                            } else {
                                FabSpacing.roundToPx()
                            }
                        } else {
                            (layoutWidth - fabWidth) / 2
                        }

                    FabPlacement(
                        left = fabLeftOffset,
                        width = fabWidth,
                        height = fabHeight,
                    )
                } else {
                    null
                }

            val bottomBarPlaceables =
                subcompose(ScaffoldLayoutContent.BottomBar) {
                    CompositionLocalProvider(
                        LocalFabPlacement provides fabPlacement,
                        content = bottomBar,
                    )
                }.map { it.measure(looseConstraints) }

            val bottomBarHeight = bottomBarPlaceables.maxByOrNull { it.height }?.height
            val fabOffsetFromBottom =
                fabPlacement?.let {
                    if (bottomBarHeight == null) {
                        it.height + FabSpacing.roundToPx() +
                            contentWindowInsets.getBottom(this@SubcomposeLayout)
                    } else {
                        // Total height is the bottom bar height + the FAB height + the padding
                        // between the FAB and bottom bar
                        bottomBarHeight + it.height + FabSpacing.roundToPx()
                    }
                }

            val snackbarOffsetFromBottom =
                if (snackbarHeight != 0) {
                    snackbarHeight +
                        (
                            fabOffsetFromBottom ?: bottomBarHeight
                                ?: contentWindowInsets.getBottom(this@SubcomposeLayout)
                        )
                } else {
                    0
                }

            val bodyContentPlaceables =
                subcompose(ScaffoldLayoutContent.MainContent) {
                    val insets = contentWindowInsets.asPaddingValues(this@SubcomposeLayout)
                    val innerPadding =
                        PaddingValues(
                            top =
                                if (topBarPlaceables.isEmpty()) {
                                    insets.calculateTopPadding()
                                } else {
                                    topBarHeight.toDp()
                                },
                            bottom =
                                if (bottomBarPlaceables.isEmpty() || bottomBarHeight == null) {
                                    insets.calculateBottomPadding()
                                } else {
                                    bottomBarHeight.toDp()
                                },
                            start = insets.calculateStartPadding((this@SubcomposeLayout).layoutDirection),
                            end = insets.calculateEndPadding((this@SubcomposeLayout).layoutDirection),
                        )
                    content(innerPadding)
                }.map { it.measure(looseConstraints) }

            // Placing to control drawing order to match default elevation of each placeable

            bodyContentPlaceables.forEach {
                it.place(0, 0)
            }
            topBarPlaceables.forEach {
                it.place(0, 0)
            }
            snackbarPlaceables.forEach {
                it.place(
                    (layoutWidth - snackbarWidth) / 2 +
                        contentWindowInsets.getLeft(
                            this@SubcomposeLayout,
                            layoutDirection,
                        ),
                    layoutHeight - snackbarOffsetFromBottom,
                )
            }
            // The bottom bar is always at the bottom of the layout
            bottomBarPlaceables.forEach {
                it.place(0, layoutHeight - (bottomBarHeight ?: 0))
            }
            // Explicitly not using placeRelative here as `leftOffset` already accounts for RTL
            fabPlacement?.let { placement ->
                fabPlaceables.forEach {
                    it.place(placement.left, layoutHeight - fabOffsetFromBottom!!)
                }
            }
        }
    }
}

object ScaffoldDefaults {
    val contentWindowInsets: WindowInsets
        @Composable
        get() = WindowInsets.systemBarsForVisualComponents
}

@JvmInline
value class FabPosition internal constructor(
    @Suppress("unused") private val value: Int,
) {
    companion object {
        val Center = FabPosition(0)

        val End = FabPosition(1)
    }

    override fun toString(): String {
        return when (this) {
            Center -> "FabPosition.Center"
            else -> "FabPosition.End"
        }
    }
}

@Immutable
internal class FabPlacement(
    val left: Int,
    val width: Int,
    val height: Int,
)

internal val LocalFabPlacement = staticCompositionLocalOf<FabPlacement?> { null }

private val FabSpacing = 16.dp

private enum class ScaffoldLayoutContent { TopBar, MainContent, Snackbar, Fab, BottomBar }
