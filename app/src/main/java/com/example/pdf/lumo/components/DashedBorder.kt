package com.example.pdf.lumo.components

import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * A modifier that adds a dashed border around a composable.
 *
 * @param color The color of the dashed border
 * @param strokeWidth The width of the dashed border
 * @param cornerRadius The corner radius of the dashed border
 * @param dashLength The length of each dash in the border
 * @param gapLength The length of each gap between dashes
 * @param phase The phase offset of the dash pattern
 */
fun Modifier.dashedBorder(
    color: Color,
    strokeWidth: Dp = 1.dp,
    cornerRadius: Dp = 0.dp,
    dashLength: Dp = 4.dp,
    gapLength: Dp = 4.dp,
    phase: Float = 0f
) = composed {
    val density = LocalDensity.current
    val strokeWidthPx = with(density) { strokeWidth.toPx() }
    val cornerRadiusPx = with(density) { cornerRadius.toPx() }
    val dashLengthPx = with(density) { dashLength.toPx() }
    val gapLengthPx = with(density) { gapLength.toPx() }
    
    val pathEffect = PathEffect.dashPathEffect(
        floatArrayOf(dashLengthPx, gapLengthPx), 
        phase
    )
    
    drawBehind {
        drawRoundRect(
            color = color,
            style = Stroke(
                width = strokeWidthPx,
                pathEffect = pathEffect
            ),
            cornerRadius = CornerRadius(cornerRadiusPx)
        )
    }
}
