package com.example.pdf

import android.content.Context
import androidx.startup.Initializer
import com.example.pdf.android.aspose.AsposeWords
import com.example.pdf.biz.NotificationSwipeReceiver
import com.example.pdf.biz.PrefStore
import com.example.pdf.biz.TimeTickReceiver
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.biz.bi.BiApiRemoteConfig
import com.example.pdf.biz.bi.FcmTokenManager
import com.example.pdf.biz.bi._koin_bi_module
import com.example.pdf.biz.notification.FixedNotiService
import com.example.pdf.biz.remoteconfig.FirebaseRemoteConfig
import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import com.example.pdf.coroutine.CoroutineModule
import com.example.pdf.kdatetime.nowInstant
import com.example.pdf.kermit.debugLog
import com.example.pdf.mmkv.UserSettingsKvStore
import com.example.pdf.mmkv.validateAndCleanRecentDocuments
import com.google.firebase.messaging.FirebaseMessaging
import com.tencent.mmkv.MMKV
import com.tradplus.ads.open.TradPlusSdk
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.datetime.Instant
import org.koin.android.ext.koin.androidContext
import org.koin.core.context.GlobalContext
import org.koin.core.context.GlobalContext.startKoin
import org.koin.ksp.generated.defaultModule
import org.koin.ksp.generated.module
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.coroutines.resume
import kotlin.time.Duration.Companion.seconds

lateinit var appContext: Context

lateinit var appInitInstant: Instant

@Suppress("unused")
class AppInitializer : Initializer<Unit> {
  override fun create(context: Context) {
    appContext = context
    appInitInstant = nowInstant()
    initMMKV(context)
    initKoin(context)
    configure1stTimeLaunchAppIfNeeded()
    configureRemoteConfig()
    configureBiApiRemoteConfig()
    configureFixedNoti(context)
    configureFcmToken()
    TimeTickReceiver.register(context)
    NotificationSwipeReceiver.register(context)

    AsposeWords.init2303()
//    AsposeWords.registerWord2412()
    validateAndCleanRecentDocuments()

    // Initialize PdfBox
    com.example.pdf.android.pdf.PdfBoxInitializer.init(context)

    // 在后台预热Aspose.Words引擎
    GlobalScope.launch {
      AsposeWords.preload(context)
    }


    preloadLottieRes(context)
  }

  override fun dependencies(): List<Class<out Initializer<*>>> = emptyList()

  companion object {
    fun initMMKV(context: Context) {
      MMKV.initialize(context)
    }

    fun initKoin(context: Context) {
      startKoin {
        androidContext(context)
        modules(
          defaultModule,
          CoroutineModule().module,
//          DbModule().module,
          _koin_bi_module
        )
      }
    }
  }
}

private fun configureRemoteConfig() {
  GlobalContext.get().get<FirebaseRemoteConfig>().fetchAndActivate()
}

fun configureBiApiRemoteConfig() {
  val koin = GlobalContext.get()

  koin.get<BiApiRemoteConfig>().init()
}

private fun configure1stTimeLaunchAppIfNeeded() {
  val prefStore = GlobalContext.get().get<PrefStore>()

  if (prefStore.firstTimeLaunchAppInstant() == null) {
    prefStore.storeFirstTimeLaunchAppInstant()
  }
}

private fun configureFixedNoti(context: Context) {
  GlobalScope.launch(Dispatchers.Main) {
    delay(300)
    FixedNotiService.startService(context)
  }
}


private fun validateAndCleanRecentDocuments() {
  GlobalScope.launch {
    delay(500L)
    GlobalContext.get().get<UserSettingsKvStore>().validateAndCleanRecentDocuments()
  }
}

//private val mutex = Mutex()
//private var sdkIsInitialized = false
//
//suspend fun configureMaxAdIfNeeded(context: Context) = mutex.withLock {
//  val appLovinSdkInstance = AppLovinSdk.getInstance(context)
//
//  val applovinMaxSdkKey = GlobalContext.get().get<RealRemoteConfig>()
//    .adConfig1.sdk_key
//
//  if (sdkIsInitialized || appLovinSdkInstance.isInitialized)
//    return
//
//  if (applovinMaxSdkKey.trim().isEmpty()) {
//    debugLog("configureMaxAdIfNeeded max_ad_non_init_by_sdk_key_empty")
//    logEventRecord("max_ad_non_init_by_sdk_key_empty")
//    return
//  }
//
//  debugLog("configureMaxAdIfNeeded max_ad_init")
//  logEventRecord("max_ad_init")
//
//  val initConfig = AppLovinSdkInitializationConfiguration
//    .builder(applovinMaxSdkKey)
//    .setMediationProvider(AppLovinMediationProvider.MAX)
//    .build()
//
//  suspendCancellableCoroutine<Unit> { con ->
//    appLovinSdkInstance.apply {
//      settings.isCreativeDebuggerEnabled = BuildConfig.DEBUG
////    showMediationDebugger()
//    }.initialize(initConfig) {
//      debugLog("configureMaxAdIfNeeded max_ad_init_success")
//      logEventRecord("max_ad_init_success")
//      sdkIsInitialized = true
//      con.resume(Unit)
//    }
//  }
//
//}

val configureTpSdkMutex = Mutex()
private var tpSdkIsInitialized = false

private var lastestInitTpAppId = ""
suspend fun configureTradPlusSdk(
  context: Context,
  tpAppId: String = GlobalContext.get().get<RealRemoteConfig>().adConfig3.app_id,
) = configureTpSdkMutex.withLock {
  val hasInit = tpSdkIsInitialized || TradPlusSdk.getIsInit()
  val equalsTpAppId = lastestInitTpAppId == tpAppId
  if (hasInit && equalsTpAppId) {
    debugLog(tag = "TradPlus") { "TradPlus SDK already initialized" }
    return@withLock
  }

  suspendCancellableCoroutine { con ->
    val resumed = AtomicBoolean(false)

    // Set initialization listener
    TradPlusSdk.setTradPlusInitListener {
      debugLog(tag = "TradPlus") { "TradPlus SDK initialized successfully" }
      logEventRecord("tradplus_init_success")
      // 初始化成功，建议在该回调后发起广告请求

      tpSdkIsInitialized = true
      if (resumed.compareAndSet(false, true)) {
        con.resume(Unit)
      }
    }

    // Initialize SDK with application ID
    TradPlusSdk.initSdk(context.applicationContext, tpAppId)
    lastestInitTpAppId = tpAppId
    logEventRecord("tradplus_init_start")
  }
}

private fun configureFcmToken() {
  GlobalScope.launch {
    delay(3.seconds)
    FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
      if (task.isSuccessful) {
        task.result?.let { token ->
          val fcmTokenManager: FcmTokenManager = GlobalContext.get().get()
          fcmTokenManager.handleNewFcmToken(token)
        }
      }
    }
  }
}