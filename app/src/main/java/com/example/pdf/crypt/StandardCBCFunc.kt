package com.example.pdf.crypt

import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import org.apache.commons.codec.binary.Base64
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

fun String.encryptCBC(key: String = "Hj9PzK3Mn6Qw8RtY", iv: String = "Dx4Fb7Lv2Sg5Uk1N"): String {
  if (this.trim().isEmpty()) return ""

  val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
  val secretKeySpec = SecretKeySpec(key.toByteArray(), "AES")
  val ivParameterSpec = IvParameterSpec(iv.toByteArray())
  cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec)
  val encryptedBytes = cipher.doFinal(this.toByteArray())
  return Base64.encodeBase64String(encryptedBytes)
}

fun String.decryptCBC(key: String = "Hj9PzK3Mn6Qw8RtY", iv: String = "Dx4Fb7Lv2Sg5Uk1N"): String {
  if (this.trim().isEmpty()) return ""

  return try {
    val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
    val secretKeySpec = SecretKeySpec(key.toByteArray(), "AES")
    val ivParameterSpec = IvParameterSpec(iv.toByteArray())
    cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec)
    val encryptedBytes = Base64.decodeBase64(this)
    val decryptedBytes = cipher.doFinal(encryptedBytes)
    String(decryptedBytes)
  } catch (e: Exception) {
    e.printStackTrace()
    Firebase.crashlytics.recordException(e)
    ""
  }

}