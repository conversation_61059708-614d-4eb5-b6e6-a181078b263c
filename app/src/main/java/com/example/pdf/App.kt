package com.example.pdf

import android.app.Application
import com.example.pdf.kermit.debugLog
import com.google.android.gms.ads.MobileAds
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.Locale

class App : Application() {
  override fun onCreate() {
    super.onCreate()
    initAdmob(this)
    registerActivityLifecycleCallbacks(AdAppActivityLifecycleCallbacks)
  }
}

private fun initAdmob(application: Application) {
  GlobalScope.launch(Dispatchers.IO) {
    MobileAds.initialize(application) { initializationStatus ->
      try {
        val statusMap =
          initializationStatus.adapterStatusMap
        for (adapterClass in statusMap.keys) {
          val status = statusMap[adapterClass]
          debugLog(tag = "MobileAds Init") {
            String.format(
              Locale.getDefault(),
              "Adapter name: %s, Description: %s, Latency: %d",
              adapterClass, status?.description, status?.latency
            )
          }
        }
      } catch (e: Exception) {
        Firebase.crashlytics.recordException(
          IllegalStateException(
            "initAdmob() failure",
            e
          )
        )
      }
    }
  }
}