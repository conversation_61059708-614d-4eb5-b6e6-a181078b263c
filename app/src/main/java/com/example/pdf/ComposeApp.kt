package com.example.pdf

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.compose.LifecycleStartEffect
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.android.context.findActivity
import com.example.pdf.biz.bi.BiReporter
import com.example.pdf.biz.bi.reportPageOnStartEvent
import com.example.pdf.biz.notification.NotificationPermissionRequester
import com.example.pdf.guia.GlobalNavigator
import com.example.pdf.guia.ScreenNode
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lyricist.ConfigureGlobalStringsEffect
import com.example.pdf.lyricist.ProvideStrings
import com.example.pdf.lyricist.Strings
import com.example.pdf.lyricist.configureGlobalStrings
import com.example.pdf.lyricist.rememberStrings
import com.example.pdf.lyricist.runtimeLanguageTagFlow

import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.roudikk.guia.containers.NavContainer
import com.roudikk.guia.core.NavigationKey
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.currentKey
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.compose.koinInject

@Composable
fun ComposeAppContainer() {
  val context = LocalContext.current
  val coroutineScope = rememberCoroutineScope()

  val runtimeLanguageTag by runtimeLanguageTagFlow.collectAsState()

  AppTheme {
    ProvideStrings(lyricist = rememberStrings(currentLanguageTag = runtimeLanguageTag)) {

      val navigator = appNavigation()

      navigator.NavContainer(modifier = Modifier.fillMaxSize())



      GlobalNavigator.Register(navigator = navigator, coroutineScope = coroutineScope)
      ConfigureGlobalStringsEffect(LocalStrings.current)

      val notificationPermissionRequester: NotificationPermissionRequester = koinInject()
      notificationPermissionRequester.RegisterRequesterIfNeeded(
        activity = context.findActivity(),
        navigator = navigator,
      )
//      MaxRewardedAdLoadingRegister(navigator)
      BiReportRegister(currentKey = navigator.currentKey)
    }
  }
}




@Composable
fun BiReportRegister(
  currentKey: NavigationKey?
) {
  val biReporter: BiReporter = koinInject()
  LaunchedEffect(currentKey) {
    currentKey?.tag()?.let {
      launch(Dispatchers.Default) {
        biReporter.reportPageOnStartEvent(it)
      }
    }
  }
}

