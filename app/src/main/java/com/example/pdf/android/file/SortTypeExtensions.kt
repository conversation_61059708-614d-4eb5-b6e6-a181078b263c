package com.example.pdf.android.file

import androidx.compose.runtime.Composable
import cafe.adriel.lyricist.LocalStrings

/**
 * Extension function to get localized display name for SortType
 * This function must be called from a Composable context to access current strings
 */
@Composable
fun DocumentFilesManager.SortType.getDisplayName(): String {
  val strings = LocalStrings.current
  return when (this) {
    DocumentFilesManager.SortType.NAME_ASC -> strings.sortNameAsc
    DocumentFilesManager.SortType.NAME_DESC -> strings.sortNameDesc
    DocumentFilesManager.SortType.DATE_MODIFIED_ASC -> strings.sortDateModifiedAsc
    DocumentFilesManager.SortType.DATE_MODIFIED_DESC -> strings.sortDateModifiedDesc
    DocumentFilesManager.SortType.SIZE_ASC -> strings.sortSizeAsc
    DocumentFilesManager.SortType.SIZE_DESC -> strings.sortSizeDesc
  }
}