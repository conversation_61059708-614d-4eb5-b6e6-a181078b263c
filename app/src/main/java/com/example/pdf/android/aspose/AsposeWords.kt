package com.example.pdf.android.aspose

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.os.Build
import androidx.core.graphics.createBitmap
import com.aspose.words.Document
import com.aspose.words.DocumentBuilder
import com.aspose.words.FontSettings
import com.aspose.words.SaveFormat
import com.example.pdf.kermit.debugLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.File


object AsposeWords {
  private const val TAG = "AsposeWords"

  fun init() {
    try {
      // --- 第一部分：修改 zzZ6E.zzYhn ---
      val zzZ6EClass = Class.forName("com.aspose.words.zzZ6E")
      val constructorZ6E = zzZ6EClass.declaredConstructors[0]
      constructorZ6E.isAccessible = true
      val instanceZ6E = constructorZ6E.newInstance(null, null)
      // zzYhn = 1 zzZ6E里面的zzYhn
      val zzYhnField = zzZ6EClass.getDeclaredField("zzYhn")
      zzYhnField.isAccessible = true
      zzYhnField.set(instanceZ6E, 1) // 设置实例字段

      // --- 第二部分：修改 zzZ6F.zzYhw ---
      // 假设 zzYhw 是静态字段，值为包含第一部分实例的列表
      val zzZ6FClass = Class.forName("com.aspose.words.zzZ6F")
      val zzYhwField = zzZ6FClass.getDeclaredField("zzYhw")
      zzYhwField.isAccessible = true
      // 创建一个包含 instanceZ6E 的列表
      val zzYhwValue = arrayListOf<Any>(instanceZ6E)
      zzYhwField.set(null, zzYhwValue) // 设置静态字段

      // --- 第三部分：修改 zzY4F.zzX4y 和 zzY4F.zzX4x ---
      // 生成文档会掉这个来判断 zzVSF
      val zzY4FClass = Class.forName("com.aspose.words.zzY4F")

      // zzY4F类中的zzX4y
      val zzX4yField = zzY4FClass.getDeclaredField("zzX4y")
      zzX4yField.isAccessible = true
      zzX4yField.set(null, 128) // 设置静态字段

      // zzY4F类中的zzX4x
      val zzX4xField = zzY4FClass.getDeclaredField("zzX4x")
      zzX4xField.isAccessible = true
      zzX4xField.set(null, false) // 设置静态字段

    } catch (e: Exception) {
      // 处理可能的反射异常，例如 ClassNotFoundException, NoSuchFieldException 等
      e.printStackTrace()
      // 您可能希望在此处记录错误或抛出自定义异常
    }
  }

  fun init2303() {
    try {
      // --- 第一部分：修改 zzZ6E.zzYhn ---
      val zzZ6EClass = Class.forName("com.aspose.words.zzZ6R")
      val constructorZ6E = zzZ6EClass.declaredConstructors[0]
      constructorZ6E.isAccessible = true
      val instanceZ6E = constructorZ6E.newInstance(null, null)
      // zzYhn = 1 zzZ6E里面的zzYhn
      val zzYhnField = zzZ6EClass.getDeclaredField("zzYij") // or zzYii
      zzYhnField.isAccessible = true
      zzYhnField.set(instanceZ6E, 1) // 设置实例字段

      // --- 第二部分：修改 zzZ6F.zzYhw ---
      // 假设 zzYhw 是静态字段，值为包含第一部分实例的列表
      val zzZ6FClass = Class.forName("com.aspose.words.zzZ6S")
      val zzYhwField = zzZ6FClass.getDeclaredField("zzYis")
      zzYhwField.isAccessible = true
      // 创建一个包含 instanceZ6E 的列表
      val zzYhwValue = arrayListOf<Any>(instanceZ6E)
      zzYhwField.set(null, zzYhwValue) // 设置静态字段

      // --- 第三部分：修改 zzY4F.zzX4y 和 zzY4F.zzX4x ---
      // 生成文档会掉这个来判断 zzVSF
      val zzY4FClass = Class.forName("com.aspose.words.zzY4Z")

      // zzY4F类中的zzX4y
      val zzX4yField = zzY4FClass.getDeclaredField("zzX61")
      zzX4yField.isAccessible = true
      zzX4yField.set(null, 128) // 设置静态字段

      // zzY4F类中的zzX4x
      val zzX4xField = zzY4FClass.getDeclaredField("zzX60")
      zzX4xField.isAccessible = true
      zzX4xField.set(null, false) // 设置静态字段

    } catch (e: Exception) {
      // 处理可能的反射异常，例如 ClassNotFoundException, NoSuchFieldException 等
      e.printStackTrace()
      // 您可能希望在此处记录错误或抛出自定义异常
    }
  }

  /**
   * 预热Aspose.Words引擎以提高首次使用性能
   *
   * 这个方法创建一个简单的Document对象并执行Word到PDF的转换操作，
   * 触发Aspose.Words内部组件的加载和初始化，特别是Word到PDF转换路径的组件。
   * 应该在应用启动时在后台线程中调用此方法。
   */
  suspend fun preload(context: Context) = withContext(Dispatchers.IO) {
    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) return@withContext

    try {
      debugLog(tag = TAG, message = "Start preload Aspose.Words...")

      // 可选: 如果您使用字体设置，预先初始化
      try {
        FontSettings.getDefaultInstance()
      } catch (e: Exception) {
        // 忽略错误
      }

      // 创建一个空白文档对象
      val doc = Document()

      // 添加更复杂的内容以确保更多引擎组件被加载
      val builder = DocumentBuilder(doc)
      builder.write("Preload text with formatting")
      builder.font.bold = true
      builder.write(" Bold text ")
      builder.font.bold = false
      builder.font.italic = true
      builder.write(" Italic text ")
      builder.font.italic = false

      // 设置不同的字体
      builder.font.name = "Arial"
      builder.writeln("Different font")
      builder.font.name = "Times New Roman"
      builder.writeln("Another font")

      // 插入表格
      builder.insertParagraph()
      builder.startTable()
      builder.insertCell()
      builder.write("Table cell 1")
      builder.insertCell()
      builder.write("Table cell 2")
      builder.endRow()
      builder.insertCell()
      builder.write("Table cell 3")
      builder.insertCell()
      builder.write("Table cell 4")
      builder.endRow()
      builder.endTable()

      // 添加列表
      builder.insertParagraph()
      builder.listFormat.applyBulletDefault()
      builder.writeln("List item 1")
      builder.writeln("List item 2")
      builder.listFormat.removeNumbers()

      // 插入图片（创建一个内存中的图片）
      builder.insertParagraph()
      val bitmap = createSampleBitmap(200, 100)
      val imageStream = convertBitmapToInputStream(bitmap)
      builder.insertImage(imageStream)
      imageStream.close()

      builder.insertParagraph()
      val bitmap2 = createSampleBitmap(150, 150)
      val imageStream2 = convertBitmapToInputStream(bitmap2)
      builder.insertImage(imageStream2, 100.0, 75.0)  // 指定宽度和高度
      imageStream2.close()

      // 保存到文件，然后再从文件加载
      val preloadDocFile = File(context.cacheDir, "preload.docx")
      doc.save(preloadDocFile.absolutePath, SaveFormat.DOCX)

      // 从文件重新加载文档，这会触发文件IO相关的组件
      val loadedDoc = Document(preloadDocFile.absolutePath)

      // 保存为PDF文件
      val preloadFile = File(context.cacheDir, "preload.pdf")
      loadedDoc.save(preloadFile.absolutePath, SaveFormat.PDF)

      // 清理临时文件
      preloadDocFile.delete()
      preloadFile.delete()

      debugLog(tag = TAG, message = "Aspose.Words preload completed.")
    } catch (e: Exception) {
      // 静默处理预热过程中的异常
      e.printStackTrace()
    }
  }

  /**
   * 创建一个简单的示例位图用于预热
   */
  private fun createSampleBitmap(width: Int, height: Int): Bitmap {
    val bitmap = createBitmap(width, height)
    val canvas = Canvas(bitmap)

    // 填充背景
    val bgPaint = Paint()
    bgPaint.color = Color.WHITE
    canvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), bgPaint)

    // 绘制一些形状
    val paint = Paint()
    paint.color = Color.BLUE
    paint.strokeWidth = 5f
    paint.style = Paint.Style.STROKE
    canvas.drawCircle(width / 2f, height / 2f, 40f, paint)

    paint.color = Color.RED
    canvas.drawLine(10f, 10f, width - 10f, height - 10f, paint)
    canvas.drawLine(width - 10f, 10f, 10f, height - 10f, paint)

    return bitmap
  }

  /**
   * 将Bitmap转换为InputStream
   */
  private fun convertBitmapToInputStream(bitmap: Bitmap): ByteArrayInputStream {
    val outputStream = ByteArrayOutputStream()
    bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
    return ByteArrayInputStream(outputStream.toByteArray())
  }
}