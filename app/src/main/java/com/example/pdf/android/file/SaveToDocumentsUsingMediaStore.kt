package com.example.pdf.android.file

import android.content.ContentValues
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import androidx.annotation.RequiresApi
import com.example.pdf.appContext
import com.example.pdf.kermit.debugLog
import java.io.File
import java.io.FileOutputStream

@RequiresApi(Build.VERSION_CODES.Q)
private fun saveToDownloadsUsingMediaStore(
  sourceFile: File,
  fileName: String,
  mimeType: String
): Boolean {
  val contentValues = ContentValues().apply {
    put(MediaStore.MediaColumns.DISPLAY_NAME, fileName)
    put(MediaStore.MediaColumns.MIME_TYPE, mimeType)
    put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_DOCUMENTS)
  }

  val resolver = appContext.contentResolver
  var outputStream: FileOutputStream? = null
  var uri: Uri? = null
  var success = false

  try {
    uri = resolver.insert(MediaStore.Downloads.EXTERNAL_CONTENT_URI, contentValues)
    if (uri != null) {
      outputStream = resolver.openOutputStream(uri) as? FileOutputStream
      if (outputStream != null) {
        sourceFile.inputStream().use { input ->
          outputStream.use { output -> // 使用 use 确保流被关闭
            input.copyTo(output)
          }
        }
        success = true
      } else {
        // 如果无法打开流，尝试删除不完整的条目
        uri.let { resolver.delete(it, null, null) }
      }
    }
  } catch (e: Exception) {
    debugLog(tag = "SaveToDownloadsUsingMediaStore", message = "MediaStore Save Error: ${e.stackTraceToString()}")
    uri?.let { resolver.delete(it, null, null) }
    success = false
  } finally {
    try {
      outputStream?.close()
    } catch (e: Exception) {
      // ignore
    }
  }

  return success
}