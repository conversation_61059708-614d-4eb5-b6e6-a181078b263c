package com.example.pdf.android.file.document_converter

import android.content.Context
import android.net.Uri
import android.util.Log
import com.aspose.words.Document
import com.aspose.words.SaveFormat
import com.example.pdf.kermit.debugLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Single
import java.io.File
import java.io.InputStream

@Single
class DocumentConverter(
  private val context: Context
) {

  companion object {
    private const val TAG = "DocumentConverter"
  }

  /**
   * 使用 Aspose.Words 将 Word 文档 (.doc 或 .docx) 转换为 PDF。
   * 这是推荐的方式，如果预算允许，因为它提供了高质量的转换并且代码简单。
   *
   * 注意：需要添加 Aspose.Words for Android via Java 的库依赖，并确保拥有有效的许可证。
   *
   * @param inputWordPath 输入 Word 文件的绝对路径。
   * @param outputPdfPath 输出 PDF 文件的绝对路径。
   * @return 如果转换成功则返回 true，否则返回 false。
   */
  // 添加 suspend 关键字，并在 IO 调度器上执行
  suspend fun convertDocToPdfByAspose(
    inputWordPath: String,
    outputPdfPath: String
  ): Boolean = withContext(Dispatchers.IO) {
    debugLog(tag = TAG) { "Attempting conversion (Aspose): $inputWordPath -> $outputPdfPath" }

    try {
      // 1. 加载 Word 文档
      val doc = Document(inputWordPath)
      Document()

      // 2. 保存为 PDF
      // Aspose 会处理文件创建和写入
      doc.save(outputPdfPath, SaveFormat.PDF)

      Log.i("DocumentConverter", "Conversion successful (Aspose)")
      val outputFile = File(outputPdfPath)
      outputFile.exists() && outputFile.length() > 0 // 基本检查

    } catch (e: Exception) {
      // Aspose 可能会抛出特定的异常，根据需要进行捕获
      Log.e("DocumentConverter", "Error during Aspose conversion", e)
      // 清理可能产生的空文件或损坏文件
      kotlin.runCatching {
        File(outputPdfPath).delete() // 确保在 IO 线程执行
      }
      false
    }
  }

  /**
   * 使用 Aspose.Words 将 Word 文档 (.doc 或 .docx) 转换为 PDF (通过 URI)。
   * 这是推荐的方式，如果预算允许，因为它提供了高质量的转换并且代码简单。
   *
   * 注意：需要添加 Aspose.Words for Android via Java 的库依赖，并确保拥有有效的许可证。
   *
   * @param inputWordUri 输入 Word 文件的 URI。
   * @param outputPdfPath 输出 PDF 文件的绝对路径。
   * @return 如果转换成功则返回 true，否则返回 false。
   */
  // 添加 suspend 关键字，并在 IO 调度器上执行
  suspend fun convertDocToPdfByAspose(
    inputWordUri: Uri,
    outputPdfPath: String
  ): Boolean = withContext(Dispatchers.IO) {
    debugLog(tag = TAG) { "Attempting conversion (Aspose, Uri): $inputWordUri -> $outputPdfPath" }
    var inputStream: InputStream? = null // 将 inputStream 声明移到 try 外部以便 finally 中访问
    try {
      // 1. 从 URI 获取 InputStream
      // 注意：ContentResolver 操作也应该在 IO 线程执行
      inputStream = context.contentResolver.openInputStream(inputWordUri)
      if (inputStream == null) {
        Log.e(TAG, "Failed to open InputStream from Uri: $inputWordUri")
        return@withContext false // 返回 withContext 的结果
      }

      // 2. 使用 InputStream 加载 Word 文档
      // 使用 use block 来确保流被关闭
      inputStream.use { stream ->
        val doc = Document(stream)

        // 3. 保存为 PDF
        doc.save(outputPdfPath, SaveFormat.PDF)
      } // InputStream 在这里自动关闭

      Log.i(TAG, "Conversion successful (Aspose, Uri)")
      val outputFile = File(outputPdfPath)
      outputFile.exists() && outputFile.length() > 0

    } catch (e: Exception) {
      // 捕获可能的异常，包括打开流或 Aspose 转换的异常
      Log.e(TAG, "Error during Aspose conversion (Uri)", e)
      // 清理可能产生的空文件或损坏文件
      File(outputPdfPath).delete() // 确保在 IO 线程执行
      false
    }
    // 注意：inputStream?.close() 由 use block 处理，无需 finally 块手动关闭
  }
}
