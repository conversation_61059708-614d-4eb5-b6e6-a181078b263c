package com.example.pdf.android.context

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.content.Intent
import android.content.Intent.ACTION_SEND
import android.content.Intent.ACTION_SENDTO
import android.content.Intent.EXTRA_EMAIL
import android.content.Intent.EXTRA_SUBJECT
import android.content.Intent.EXTRA_TEXT
import android.content.Intent.FLAG_ACTIVITY_NEW_TASK
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.activity.ComponentActivity
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import androidx.core.net.toUri
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.biz.skipSplash
import com.example.pdf.kermit.debugLog

@RequiresApi(Build.VERSION_CODES.O)
fun ComponentActivity.openNotificationSettings(
  onResult: (resultCode: Int) -> Unit = {}
) {
  try {
    val launcher = activityResultRegistry.register(
      "notification_settings_${System.currentTimeMillis()}",
      ActivityResultContracts.StartActivityForResult()
    ) { result ->
      onResult(result.resultCode)
    }

    val intent = Intent(Settings.ACTION_APP_NOTIFICATION_SETTINGS).apply {
      putExtra(
        Settings.EXTRA_APP_PACKAGE,
        <EMAIL>
      )
    }

    launcher.launch(intent)
    skipSplash()
    logEventRecord("noti_permission_request")
    debugLog { "noti_permission_request" }
  } catch (e: Exception) {
    e.printStackTrace()
  }
}

fun Context.findActivity(): Activity {
  var context = this
  while (context is ContextWrapper) {
    if (context is Activity) return context
    context = context.baseContext
  }
  throw IllegalStateException("CAN NOT FIND ANY ACTIVITY")
}

fun Context.openBrowser(url: String) {
  val browserIntent = Intent(Intent.ACTION_VIEW, url.toUri())

  try {
    startActivity(browserIntent)
    skipSplash()
  } catch (e: Exception) {
    e.printStackTrace()
  }
}

private fun createFeedbackMailtoIntent(
  email: String,
  subject: String?,
  body: String?
): Intent {
  return Intent(ACTION_SEND)
    .putExtra(EXTRA_EMAIL, arrayOf(email))
    .putExtra(EXTRA_SUBJECT, subject)
    .putExtra(EXTRA_TEXT, body)
    .apply {
      selector = Intent(ACTION_SENDTO).setData("mailto:".toUri())
    }
}

fun Context.sendEmail(email: String, subject: String? = null, body: String? = null) {
  val emailIntent = createFeedbackMailtoIntent(email, subject, body)

  try {
    startActivity(emailIntent)
    skipSplash()
  } catch (e: Exception) {
    e.printStackTrace()
  }
}

fun Context.openAppDetailsSettings() {
  try {
    startActivity(
      Intent(
        Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
        Uri.fromParts("package", packageName, null)
      ).apply {
        addFlags(FLAG_ACTIVITY_NEW_TASK)
      }
    )
    skipSplash()
  } catch (e: Exception) {
    e.printStackTrace()
  }
}