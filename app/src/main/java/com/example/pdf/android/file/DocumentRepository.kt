package com.example.pdf.android.file

import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import org.koin.core.annotation.Single
import java.io.File

@Single
class DocumentRepository(
  private val documentFilesManager: DocumentFilesManager,
  private val importDocumentFilesHelper: ImportDocumentFilesHelper
) {
  private val _documentsFlow: MutableStateFlow<List<File>> = MutableStateFlow(emptyList())
  val documentsFlow: StateFlow<List<File>> get() = _documentsFlow

  fun onFetchDocuments() {
    GlobalScope.launch {
      val mediaStoreDocumentFiles = documentFilesManager.fetchWithMediaStore()
      val importDocumentFiles = importDocumentFilesHelper.fetchImports()

      _documentsFlow.emit(mediaStoreDocumentFiles + importDocumentFiles)
    }
  }
}