package com.example.pdf.android.file

import com.example.pdf.android.toast.showToast
import com.example.pdf.lyricist.globalStrings
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.IOException

suspend fun documentsDelete(
  documents: List<File>,
  onSuccess: () -> Unit,
  onFailure: (Throwable) -> Unit = { showToast(message = globalStrings.failedToDelete) }
) = withContext(Dispatchers.IO) {
  runCatching {
    documents.forEach { file ->
      file.delete()
    }
  }.onSuccess {
    onSuccess()
  }.onFailure {
    onFailure(it)
  }
}

suspend fun documentRename(
  document: File,
  newName: String,
  onSuccess: (File) -> Unit,
  onFailure: (Throwable) -> Unit = { showToast(message = globalStrings.failedToRename) }
) = withContext(Dispatchers.IO) {
  runCatching {
    val parentDir = document.parentFile
    if (parentDir == null || !parentDir.exists() || !parentDir.isDirectory) {
      throw IOException("Invalid parent directory for ${document.absolutePath}")
    }

    // Ensure the new name doesn't contain path separators to avoid moving the file unexpectedly
    if (newName.contains(File.separatorChar)) {
      throw IllegalArgumentException("New name cannot contain path separators: $newName")
    }

    // Check if the original file exists
    if (!document.exists()) {
      throw java.io.FileNotFoundException("Original file does not exist: ${document.absolutePath}")
    }

    val newFile = File(parentDir, newName)

    // Check if a file with the new name already exists
    if (newFile.exists()) {
      throw FileAlreadyExistsException(newFile)
    }

    val success = document.renameTo(newFile)
    if (!success) {
      // Provide a more specific error if possible, otherwise a generic one
      throw IOException("Failed to rename ${document.name} to $newName. Unknown error.")
    }
    newFile // Return the renamed file object
  }.onSuccess { renamedFile ->
    // Run the success callback, passing the File object representing the renamed file
    onSuccess(renamedFile)
  }.onFailure { exception ->
    // Run the failure callback, passing the caught exception
    onFailure(exception)
  }
}