package com.example.pdf.android.permission

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.Settings
import androidx.core.content.ContextCompat
import com.example.pdf.ui.node.file_access_requester.FileAccessRequesterNode
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.push
import org.koin.core.annotation.Single

@Single
class FileAccessManager(private val context: Context) {

  /**
   * Checks if the app has the necessary file access permissions
   * @return true if permissions are granted, false otherwise
   */
  fun hasFileAccessPermission(): Boolean {
    // For Android R (API 30) and above, we need to check MANAGE_EXTERNAL_STORAGE
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
      Environment.isExternalStorageManager()
    } else {
      // For Android Q (API 29) and below, we check READ_EXTERNAL_STORAGE
      ContextCompat.checkSelfPermission(
        context,
        Manifest.permission.READ_EXTERNAL_STORAGE
      ) == PackageManager.PERMISSION_GRANTED &&
        ContextCompat.checkSelfPermission(
          context,
          Manifest.permission.WRITE_EXTERNAL_STORAGE
        ) == PackageManager.PERMISSION_GRANTED
    }
  }

  /**
   * Creates an intent to request file access permission
   * @return Intent to launch for permission request
   */
  fun createFileAccessPermissionIntent(): Intent? {
    // For Android R and above, we need to request MANAGE_EXTERNAL_STORAGE
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
      Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION).apply {
        data = Uri.parse("package:${context.packageName}")
      }
    } else {
      null
    }
  }

  /**
   * Check for file access permission and show request dialog if not granted
   * @param navigator Navigator to show the FileAccessRequesterNode
   * @return true if permission is already granted, false if request dialog was shown
   */
  fun checkAndRequestFileAccess(navigator: Navigator): Boolean {
    return if (hasFileAccessPermission()) {
      true
    } else {
      navigator.push(FileAccessRequesterNode())
      false
    }
  }

}