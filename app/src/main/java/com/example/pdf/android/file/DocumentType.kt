package com.example.pdf.android.file

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import cafe.adriel.lyricist.LocalStrings

enum class DocumentType {
  ALL, PDF, WORD, EXCEL, POWERPOINT
}

data class DocumentTypeItemData(
  val type: DocumentType,
  val label: String = ""
)


@Composable
fun rememberDocumentTypeItemDataList() = remember(LocalStrings.current) {
  listOf(
    DocumentTypeItemData(DocumentType.ALL, "All"),
    DocumentTypeItemData(DocumentType.PDF, "PDF"),
    DocumentTypeItemData(DocumentType.WORD, "Word"),
    DocumentTypeItemData(DocumentType.EXCEL, "Excel"),
    DocumentTypeItemData(DocumentType.POWERPOINT, "PPT"),
  )
}