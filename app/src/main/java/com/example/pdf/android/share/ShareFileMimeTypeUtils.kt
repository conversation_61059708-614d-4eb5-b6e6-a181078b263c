package com.example.pdf.android.share

import android.webkit.MimeTypeMap
import java.io.File

/**
 * Utility class for handling file MIME type detection
 */
object ShareFileMimeTypeUtils {
  /**
   * Get MIME type from file extension
   * @param file The file to check
   * @return The MIME type based on file extension
   */
  fun getMimeType(file: File): String {
    return when (val extension = file.extension.lowercase()) {
      // Microsoft Office formats
      "doc" -> "application/msword"
      "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      "xls" -> "application/vnd.ms-excel"
      "xlsx" -> "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      "ppt" -> "application/vnd.ms-powerpoint"
      "pptx" -> "application/vnd.openxmlformats-officedocument.presentationml.presentation"

      // PDF
      "pdf" -> "application/pdf"

      // Try to get from system
      else -> {
        MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension) ?: "application/octet-stream"
      }
    }
  }

  /**
   * Determine common MIME type for a list of files
   * @param files List of files to analyze
   * @return A common MIME type that can represent all files
   */
  fun getCommonMimeType(files: List<File>): String {
    if (files.isEmpty()) return "application/octet-stream"
    if (files.size == 1) return getMimeType(files.first())

    val mimeTypes = files.map { getMimeType(it) }.toSet()

    return when {
      mimeTypes.size == 1 -> mimeTypes.first()
      mimeTypes.all { it.startsWith("application/vnd.openxmlformats") } ->
        "application/vnd.openxmlformats-officedocument"

      mimeTypes.all { it.startsWith("application/vnd.ms") } ->
        "application/vnd.ms-office"

      mimeTypes.all { it == "application/pdf" } ->
        "application/pdf"

      mimeTypes.all { it.startsWith("application/") } ->
        "application/*"

      else -> "application/octet-stream"
    }
  }
}