package com.example.pdf.android.pdf

import android.content.Context
import com.example.pdf.kermit.debugLog
import com.tom_roush.pdfbox.android.PDFBoxResourceLoader
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Utility class to initialize and manage PdfBox library
 */
object PdfBoxInitializer {
    private const val TAG = "PdfBoxInitializer"
    private var isInitialized = false

    /**
     * Initialize PdfBox library
     * 
     * @param context Application context
     */
    fun init(context: Context) {
        if (!isInitialized) {
            PDFBoxResourceLoader.init(context)
            isInitialized = true
            debugLog(tag = TAG) { "PdfBox initialized" }
        }
    }

    /**
     * Check if PdfBox is initialized
     * 
     * @return true if PdfBox is initialized, false otherwise
     */
    fun isInitialized(): Boolean {
        return isInitialized
    }

    /**
     * Ensure PdfBox is initialized before executing a suspending operation
     * 
     * @param context Application context
     * @param block The suspending operation to execute
     * @return The result of the operation
     */
    suspend fun <T> ensureInitialized(context: Context, block: suspend () -> T): T = withContext(Dispatchers.Default) {
        if (!isInitialized) {
            init(context)
        }
        return@withContext block()
    }
}
