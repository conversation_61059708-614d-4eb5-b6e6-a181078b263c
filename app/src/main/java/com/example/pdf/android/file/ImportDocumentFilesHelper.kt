package com.example.pdf.android.file

import android.content.Context
import android.net.Uri
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Single
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

@Single
class ImportDocumentFilesHelper(private val context: Context) {

  companion object {
    // Document MIME types for MS Office and PDF
    val DOCUMENT_MIME_TYPES = arrayOf(
      // PDF
      "application/pdf",
      // Word
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      // Excel
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      // PowerPoint
      "application/vnd.ms-powerpoint",
      "application/vnd.openxmlformats-officedocument.presentationml.presentation"
    )

    val PDF_MIME_TYPE = arrayOf("application/pdf")

    val WORD_MIME_TYPE = arrayOf(
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    )

    private const val INTERNAL_DOCUMENT_DIR_NAME = "documents"
  }

  /**
   * Handle the selected document from the file picker
   * @param uri Uri of the selected document
   * @param callback Callback to be invoked when document processing is complete
   */
  fun handleSelectedDocument(uri: Uri?, callback: (Boolean, File?) -> Unit) {
    if (uri == null) {
      callback(false, null)
      return
    }

    // Launch coroutine to handle file operations
    GlobalScope.launch(Dispatchers.IO) {
      try {
        // Get the file name from the URI
        val fileName = getFileNameFromUri(uri)

        if (fileName.isNullOrBlank()) {
          withContext(Dispatchers.Main) {
            callback(false, null)
          }
          return@launch
        }

        // Create document directory if it doesn't exist
        val documentDir = getInternalDocumentDirectory()
        if (!documentDir.exists()) {
          documentDir.mkdirs()
        }
        
        // Create file in app's document subdirectory
        val destinationFile = File(documentDir, fileName)

        // Copy the file content to app's private directory
        context.contentResolver.openInputStream(uri)?.use { inputStream ->
          FileOutputStream(destinationFile).use { outputStream ->
            inputStream.copyTo(outputStream)
          }
        }

        // Invoke the callback with success and the imported file
        withContext(Dispatchers.Main) {
          callback(true, destinationFile)
        }
      } catch (e: IOException) {
        // Handle file read/write errors
        withContext(Dispatchers.Main) {
          callback(false, null)
        }
      } catch (e: Exception) {
        // Handle any other exceptions
        withContext(Dispatchers.Main) {
          callback(false, null)
        }
      }
    }
  }

  /**
   * Extract the file name from a content URI
   * @param uri Uri to get the file name from
   * @return The file name or null if not found
   */
  private fun getFileNameFromUri(uri: Uri): String? {
    val cursor = context.contentResolver.query(
      uri,
      null,
      null,
      null,
      null
    ) ?: return null

    cursor.use {
      if (it.moveToFirst()) {
        val displayNameIndex = it.getColumnIndex("_display_name")
        if (displayNameIndex >= 0) {
          return it.getString(displayNameIndex)
        }
      }
    }

    // If cursor approach failed, try to get from path segments
    uri.lastPathSegment?.let { segment ->
      if (segment.contains("/")) {
        return segment.substring(segment.lastIndexOf("/") + 1)
      }
      return segment
    }

    return null
  }

  /**
   * Fetches all document files from the app's document directory
   * @return List of document files or empty list if none found or directory doesn't exist
   */
  suspend fun fetchImports(): List<File> = withContext(Dispatchers.IO) {
    val documentDir = getInternalDocumentDirectory()

    if (!documentDir.exists()) {
      return@withContext emptyList<File>()
    }

    return@withContext documentDir.listFiles()?.filter { it.isFile }?.sorted() ?: emptyList()
  }

  /**
   * Returns the document directory File
   */
  fun getInternalDocumentDirectory(): File {
    return File(context.filesDir, INTERNAL_DOCUMENT_DIR_NAME)
  }
}