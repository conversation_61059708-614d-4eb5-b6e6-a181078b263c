package com.example.pdf.android.file

import android.content.Context
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Single
import java.io.File

@Single
class DocumentFilesManager(private val context: Context) {

  companion object {
    private val DOCUMENT_EXTENSIONS = listOf(
      "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx"
    )

    private val DOCUMENT_MIME_TYPES = listOf(
      // PDF
      "application/pdf",
      // Word
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      // Excel
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      // PowerPoint
      "application/vnd.ms-powerpoint",
      "application/vnd.openxmlformats-officedocument.presentationml.presentation"
    )
  }

  /**
   * Enum class defining sort types for document files
   */
  enum class SortType {
    NAME_ASC,
    NAME_DESC,
    DATE_MODIFIED_ASC,
    DATE_MODIFIED_DESC,
    SIZE_ASC,
    SIZE_DESC
  }

  /**
   * Get all document files from the device storage using MediaStore API by MIME type
   * This is more efficient than recursively scanning the file system
   * @return List of document files
   */
  suspend fun fetchWithMediaStore(): List<File> = withContext(Dispatchers.IO) {
    val result = mutableListOf<File>()
    val contentResolver = context.contentResolver

    // Use different MediaStore collection based on Android version
    val collection = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
      MediaStore.Files.getContentUri(MediaStore.VOLUME_EXTERNAL)
    } else {
      MediaStore.Files.getContentUri("external")
    }

    // Columns to retrieve
    val projection = arrayOf(
      MediaStore.Files.FileColumns._ID,
      MediaStore.Files.FileColumns.MIME_TYPE,
      MediaStore.Files.FileColumns.DATA,
      MediaStore.Files.FileColumns.DISPLAY_NAME,
      MediaStore.Files.FileColumns.SIZE,
      MediaStore.Files.FileColumns.DATE_MODIFIED
    )

    // Build selection for MIME types using OR conditions
    val selectionBuilder = StringBuilder()
    val selectionArgs = mutableListOf<String>()

    DOCUMENT_MIME_TYPES.forEachIndexed { index, mimeType ->
      if (index > 0) selectionBuilder.append(" OR ")
      selectionBuilder.append("${MediaStore.Files.FileColumns.MIME_TYPE} = ?")
      selectionArgs.add(mimeType)
    }

    // Perform query
    contentResolver.query(
      collection,
      projection,
      selectionBuilder.toString(),
      selectionArgs.toTypedArray(),
      null
    )?.use { cursor ->
      val dataColumn = cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns.DATA)

      while (cursor.moveToNext()) {
        val filePath = cursor.getString(dataColumn)
        val file = File(filePath)
        if (file.exists()) {
          result.add(file)
        }
      }
    }

    result
  }

  /**
   * Get all document files from MediaStore and sort them based on the given sort type
   * This provides a direct sorted result from MediaStore without the fallback mechanism
   * @param sortType The sort type to use
   * @return Sorted list of document files from MediaStore
   */
  suspend fun fetchWithMediaStoreSorted(sortType: SortType): List<File> {
    val files = fetchWithMediaStore()
    return withContext(Dispatchers.Default) {
      sortDocumentFiles(files, sortType)
    }
  }

  /**
   * Get all document files using MediaStore if possible, with fallback to file system scanning
   * @return List of document files
   */
  suspend fun fetchAll(): List<File> = withContext(Dispatchers.IO) {
    // Try using MediaStore first (more efficient)
    val mediaStoreResult = fetchWithMediaStore()

    // If MediaStore returned results, use them
    if (mediaStoreResult.isNotEmpty()) {
      mediaStoreResult
    } else {
      // Fallback to file system scan if MediaStore returned no results
      val result = mutableListOf<File>()
      val externalStorageDir = Environment.getExternalStorageDirectory()
      searchDocumentFiles(externalStorageDir, result)
      result
    }
  }

  /**
   * Recursively search for document files in the given directory
   * @param directory The directory to search in
   * @param result The list to add found files to
   */
  private suspend fun searchDocumentFiles(directory: File, result: MutableList<File>) {
    withContext(Dispatchers.IO) {
      if (!directory.exists() || !directory.isDirectory) return@withContext

      val files = directory.listFiles() ?: return@withContext
      for (file in files) {
        if (file.isDirectory) {
          searchDocumentFiles(file, result)
        } else if (isDocumentFile(file)) {
          result.add(file)
        }
      }
    }
  }

  /**
   * Check if a file is a document file based on its extension
   * @param file The file to check
   * @return true if the file is a document file, false otherwise
   */
  private fun isDocumentFile(file: File): Boolean {
    val extension = file.extension.lowercase()
    return DOCUMENT_EXTENSIONS.contains(extension)
  }

  /**
   * Get MIME type for a file based on its extension
   * @param file The file to get MIME type for
   * @return MIME type of the file or null if not a known document type
   */
  fun matchDocumentMimeType(file: File): String? {
    return when (file.extension.lowercase()) {
      "pdf" -> "application/pdf"
      "doc" -> "application/msword"
      "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      "xls" -> "application/vnd.ms-excel"
      "xlsx" -> "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      "ppt" -> "application/vnd.ms-powerpoint"
      "pptx" -> "application/vnd.openxmlformats-officedocument.presentationml.presentation"
      else -> null
    }
  }

  /**
   * Delete a document file
   * @param file The file to delete
   * @return true if the file was deleted successfully, false otherwise
   */
  suspend fun deleteDocumentFile(file: File): Boolean = withContext(Dispatchers.IO) {
    if (file.exists()) {
      file.delete()
    } else {
      false
    }
  }

  /**
   * Sort document files based on the given sort type
   * @param files The list of document files to sort
   * @param sortType The sort type to use
   * @return Sorted list of document files
   */
  fun sortDocumentFiles(files: List<File>, sortType: SortType): List<File> {
    return when (sortType) {
      SortType.NAME_ASC -> files.sortedWith(compareBy { it.name })
      SortType.NAME_DESC -> files.sortedWith(compareByDescending { it.name })
      SortType.DATE_MODIFIED_ASC -> files.sortedWith(compareBy { it.lastModified() })
      SortType.DATE_MODIFIED_DESC -> files.sortedWith(compareByDescending { it.lastModified() })
      SortType.SIZE_ASC -> files.sortedWith(compareBy { it.length() })
      SortType.SIZE_DESC -> files.sortedWith(compareByDescending { it.length() })
    }
  }

  /**
   * Get all document files and sort them based on the given sort type
   * @param sortType The sort type to use
   * @return Sorted list of document files
   */
  suspend fun fetchAllSorted(sortType: SortType): List<File> {
    val files = fetchAll()
    return withContext(Dispatchers.Default) {
      sortDocumentFiles(files, sortType)
    }
  }

  /**
   * Get document files from a specific directory
   * @param directory The directory to search in
   * @return List of document files in the specified directory
   */
  suspend fun documentFilesFromDirectory(directory: File): List<File> =
    withContext(Dispatchers.IO) {
    val result = mutableListOf<File>()
    if (!directory.exists() || !directory.isDirectory) return@withContext result

    val files = directory.listFiles() ?: return@withContext result
    for (file in files) {
      if (!file.isDirectory && isDocumentFile(file)) {
        result.add(file)
      }
    }
    result
  }

  /**
   * Get document files from a specific directory and sort them
   * @param directory The directory to search in
   * @param sortType The sort type to use
   * @return Sorted list of document files in the specified directory
   */
  suspend fun documentFilesFromDirectorySorted(directory: File, sortType: SortType): List<File> {
    val files = documentFilesFromDirectory(directory)
    return withContext(Dispatchers.Default) {
      sortDocumentFiles(files, sortType)
    }
  }

  /**
   * Filter document files by document type
   * @param files The list of document files to filter
   * @param documentType The document type to filter by
   * @return Filtered list of document files
   */
  fun filterByDocumentType(files: List<File>, documentType: DocumentType): List<File> {
    return when (documentType) {
      DocumentType.PDF -> files.filter { it.extension.lowercase() == "pdf" }
      DocumentType.WORD -> files.filter {
        val ext = it.extension.lowercase()
        ext == "doc" || ext == "docx"
      }

      DocumentType.EXCEL -> files.filter {
        val ext = it.extension.lowercase()
        ext == "xls" || ext == "xlsx"
      }

      DocumentType.POWERPOINT -> files.filter {
        val ext = it.extension.lowercase()
        ext == "ppt" || ext == "pptx"
      }

      DocumentType.ALL -> files
    }
  }

}