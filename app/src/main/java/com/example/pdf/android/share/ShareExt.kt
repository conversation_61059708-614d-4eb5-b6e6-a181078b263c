package com.example.pdf.android.share

import android.app.Activity
import android.content.Intent
import android.net.Uri
import androidx.core.content.FileProvider
import java.io.File

/**
 * Share a single document file
 * @param file The file to share
 * @param mimeType The MIME type of the file. If null, will be auto-detected
 * @param title The title displayed in the system's chooser dialog
 */
fun Activity.shareFile(file: File, mimeType: String? = null, title: String = "Share File") {
  val uri = FileProvider.getUriForFile(
    this,
    "$packageName.provider",
    file
  )

  val detectedMimeType = mimeType ?: ShareFileMimeTypeUtils.getMimeType(file)

  val intent = Intent(Intent.ACTION_SEND).apply {
    putExtra(Intent.EXTRA_STREAM, uri)
    type = detectedMimeType
    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
  }

  startActivity(Intent.createChooser(intent, title))
}


/**
 * Share multiple document files
 * @param files The files to share
 * @param mimeType The MIME type of the files. If null, will auto-detect appropriate type
 * @param title The title displayed in the system's chooser dialog
 */
fun Activity.shareFiles(files: List<File>, mimeType: String? = null, title: String = "Share Files") {
  if (files.isEmpty()) return

  if (files.size == 1) {
    shareFile(file = files[0], mimeType = mimeType, title = title)
    return
  }

  val uriList = ArrayList<Uri>()

  for (file in files) {
    val uri = FileProvider.getUriForFile(
      this,
      "$packageName.provider",
      file
    )
    uriList.add(uri)
  }

  // Determine the MIME type to use
  val finalMimeType = mimeType ?: ShareFileMimeTypeUtils.getCommonMimeType(files)

  val intent = Intent(Intent.ACTION_SEND_MULTIPLE).apply {
    putParcelableArrayListExtra(Intent.EXTRA_STREAM, uriList)
    type = finalMimeType
    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
  }

  startActivity(Intent.createChooser(intent, title))
}