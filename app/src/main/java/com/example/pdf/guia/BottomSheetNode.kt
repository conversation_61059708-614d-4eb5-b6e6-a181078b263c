package com.example.pdf.guia

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import com.roudikk.guia.core.BottomSheet
import com.roudikk.guia.core.NavigationKey
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.localBottomSheet
import com.roudikk.guia.extensions.requireLocalNavigator

abstract class
BottomSheetNode(
  private val tag: String,
  private val bottomSheetOptions: BottomSheet.BottomSheetOptions = BottomSheet.BottomSheetOptions()
) : NavigationKey.WithNode<BottomSheet> {

  override fun tag() = tag

  override fun navigationNode() = BottomSheet {
    val bottomSheet = localBottomSheet()
    LaunchedEffect(bottomSheet) {
      bottomSheet?.bottomSheetOptions = bottomSheetOptions
    }

    Content(
      navigator = requireLocalNavigator(),
      bottomSheet = bottomSheet
    )
  }

  @Composable
  abstract fun Content(
    navigator: Navigator,
    bottomSheet: BottomSheet?,
  )
}