package com.example.pdf.guia

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.ui.graphics.Color
import androidx.lifecycle.compose.LifecycleStartEffect
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.roudikk.guia.core.NavigationKey
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.core.Screen
import com.roudikk.guia.extensions.requireLocalNavigator

abstract class ScreenNode(
  private val tag: String
) : NavigationKey.WithNode<Screen> {

  override fun tag() = tag

  override fun navigationNode() = Screen {
    Content(
      navigator = requireLocalNavigator(),
    )
  }

  @Composable
  abstract fun Content(
    navigator: Navigator,
  )
}

@Composable
fun ScreenNode.UseStatusBarDarkIcons() {
  val systemUiController = rememberSystemUiController()

  LifecycleStartEffect(Unit) {
    systemUiController.setStatusBarColor(
      color = Color.Transparent,
      darkIcons = true
    )

    onStopOrDispose {
      systemUiController.setStatusBarColor(
        color = Color.Transparent,
        darkIcons = false
      )
    }
  }
}

@Composable
fun NavigationKey.SetStatusBarDarkIcons() {
  val systemUiController = rememberSystemUiController()

  DisposableEffect(Unit) {
    systemUiController.setStatusBarColor(
      color = Color.Transparent,
      darkIcons = true
    )

    onDispose { }
  }

}