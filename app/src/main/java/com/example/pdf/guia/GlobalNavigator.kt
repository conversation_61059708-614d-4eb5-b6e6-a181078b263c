package com.example.pdf.guia

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import com.example.pdf.kermit.debugLog
import com.roudikk.guia.core.Navigator
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.receiveAsFlow
import org.koin.core.component.KoinComponent

object GlobalNavigator : KoinComponent {

  private const val TAG = "GlobalNavigator"

  private var _navigator: Navigator? = null
  val navigator get() = _navigator

  private val navigateActionChannel =
    Channel<NavigateAction>(Channel.BUFFERED)

  @Composable
  fun Register(
    navigator: Navigator,
    coroutineScope: CoroutineScope
  ) {
    LaunchedEffect(navigator) {
      _navigator = navigator

      navigateActionChannel.receiveAsFlow().onEach {
        debugLog(
          tag = TAG,
          message = "Register: $it"
        )
        it(navigator)
      }.launchIn(coroutineScope)
    }
  }

  fun tryTransaction(action: NavigateAction) {
    navigateActionChannel.trySend(action)
  }

  suspend fun transaction(action: NavigateAction) {
    navigateActionChannel.send(action)
  }

}

typealias NavigateAction = Navigator.() -> Unit
