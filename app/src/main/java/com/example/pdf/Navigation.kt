package com.example.pdf

import androidx.compose.runtime.Composable
import com.example.pdf.guia.FadeTransitions
import com.example.pdf.guia.HorizontalSlideTransitions
import com.example.pdf.ui.node.search.SearchNode
import com.example.pdf.ui.node.selection.SelectionNode
import com.example.pdf.ui.node.home.HomeNode
import com.roudikk.guia.core.NavigationKey
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.core.rememberNavigator

@Composable
internal fun appNavigation(): Navigator {
  val initialNode = HomeNode()

  val navigator = rememberNavigator(initialKey = initialNode) {
    defaultTransition { previous: NavigationKey, new: NavigationKey, isPop ->
      when {
        new is HomeNode && previous is HomeNode && isPop -> FadeTransitions.FastFullFade.popEnterExit
        new is HomeNode && previous is HomeNode && !isPop -> FadeTransitions.FastFullFade.enterExit
        new is SearchNode && !isPop -> FadeTransitions.FastFullFade.enterExit
        previous is SearchNode && isPop -> FadeTransitions.FastFullFade.popEnterExit
        new is SelectionNode && !isPop -> FadeTransitions.FastFullFade.enterExit
        previous is SelectionNode && isPop -> FadeTransitions.FastFullFade.popEnterExit
        !isPop -> HorizontalSlideTransitions.Default.enterExit
        else -> HorizontalSlideTransitions.Default.popEnterExit
      }
    }
  }

  return navigator
}
