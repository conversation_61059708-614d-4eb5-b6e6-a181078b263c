package com.example.pdf.kdatetime

import kotlinx.datetime.*
import java.time.format.TextStyle
import java.util.Locale

fun nowInstant() = Clock.System.now()

fun Instant.todayStartInstant(timeZone: TimeZone = TimeZone.currentSystemDefault()): Instant {
  return this.todayStartDateTime(timeZone).toInstant(timeZone)
}

fun Instant.todayEndInstant(timeZone: TimeZone): Instant {
  return this.todayEndDateTime(timeZone).toInstant(timeZone)
}

fun Instant.lessMinutesAndSeconds(timeZone: TimeZone): Instant {
  return this.toLocalDateTime(timeZone).let {
    LocalDateTime(it.year, it.month, it.dayOfMonth, it.hour, 0, 0).toInstant(timeZone)
  }
}

fun Instant.todayStartDateTime(timeZone: TimeZone): LocalDateTime {
  return this.toLocalDateTime(timeZone).let {
    LocalDateTime(it.year, it.month, it.dayOfMonth, 0, 0, 0)
  }
}

fun Instant.todayInstants(timeZone: TimeZone): List<Instant> {
  val hourOfDayStartInstants = arrayListOf<Instant>()

  val todayStartInstant = todayStartInstant(timeZone)
  hourOfDayStartInstants.add(todayStartInstant)

  repeat(23) {
    hourOfDayStartInstants.add(
      todayStartInstant.plus(
        DateTimePeriod(hours = it + 1),
        timeZone
      )
    )
  }

  return hourOfDayStartInstants
}

fun Instant.todayEndDateTime(timeZone: TimeZone): LocalDateTime {
  return this.toLocalDateTime(timeZone)
    .let { LocalDateTime(it.year, it.month, it.dayOfMonth, 23, 59, 59, 1000000000 - 1) }
}

fun Instant.thisWeekStartDateTime(timeZone: TimeZone): LocalDateTime {
  val localDateTime = this.toLocalDateTime(timeZone)

  val currentDayOfWeekValue = localDateTime.dayOfWeek.value

  val weekStartDayOffset = if (currentDayOfWeekValue == 7) 0 else 0 - currentDayOfWeekValue

  return this.plus(DateTimePeriod(days = weekStartDayOffset), timeZone).toLocalDateTime(timeZone)
    .let { LocalDateTime(it.year, it.month, it.dayOfMonth, 0, 0) }
}

fun Instant.thisWeekEndDateTime(timeZone: TimeZone): LocalDateTime {
  val localDateTime = this.toLocalDateTime(timeZone)

  val currentDayOfWeekValue = localDateTime.dayOfWeek.value

  val weekEndDayOffset = if (currentDayOfWeekValue == 7) 6 else 7 - currentDayOfWeekValue - 1

  return this.plus(DateTimePeriod(days = weekEndDayOffset), timeZone).toLocalDateTime(timeZone)
    .let { LocalDateTime(it.year, it.month, it.dayOfMonth, 23, 59, 59, 1000000000 - 1) }
}

fun Instant.thisWeekInstants(timeZone: TimeZone): List<Instant> {
  val dayOfWeekStartInstants = arrayListOf<Instant>()

  val thisWeekStartInstant = thisWeekStartDateTime(timeZone).toInstant(timeZone)
  dayOfWeekStartInstants.add(thisWeekStartInstant)

  repeat(6) {
    dayOfWeekStartInstants.add(
      thisWeekStartInstant.plus(
        DateTimePeriod(days = it + 1),
        timeZone
      )
    )
  }

  return dayOfWeekStartInstants
}

fun Instant.thisMonthStartInstant(timeZone: TimeZone): Instant {
  return this.toLocalDateTime(timeZone).let {
    LocalDateTime(it.year, it.month, 1, 0, 0, 0).toInstant(timeZone)
  }
}

fun Instant.thisMonthEndInstant(timeZone: TimeZone): Instant {
  val monthStartInstant = thisMonthStartInstant(timeZone)
  return monthStartInstant
    .plus(DateTimePeriod(months = 1), timeZone)
    .minus(DateTimePeriod(days = 1), timeZone)
    .todayEndInstant(timeZone)
}

fun Instant.thisMonthInstants(timeZone: TimeZone): List<Instant> {
  val dayOfMonthStartInstants = arrayListOf<Instant>()

  val thisMonthEndInstant = thisMonthEndInstant(timeZone)

  val dayOfMonth = thisMonthEndInstant.toLocalDateTime(timeZone).dayOfMonth

  repeat(dayOfMonth) {
    dayOfMonthStartInstants.add(
      thisMonthEndInstant.minus(DateTimePeriod(days = dayOfMonth - it, nanoseconds = -1), timeZone)
    )
  }
  return dayOfMonthStartInstants
}

fun Month.displayName(
  style: TextStyle = TextStyle.SHORT,
  locale: Locale = Locale.ENGLISH // todo
): String {
  return this.getDisplayName(style, locale)
}

fun DayOfWeek.displayName(
  style: TextStyle = TextStyle.SHORT,
  locale: Locale = Locale.ENGLISH
): String {
  return this.getDisplayName(style, locale)
}

fun LocalDateTime.monthAndDayDisplayName(
  style: TextStyle = TextStyle.SHORT,
  locale: Locale = Locale.ENGLISH
): String {
  val ldt = this

  return "${ldt.dayOfMonth} ${ldt.month.displayName(style, locale)}"
}