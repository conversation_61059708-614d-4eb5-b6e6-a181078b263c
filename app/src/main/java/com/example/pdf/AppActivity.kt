package com.example.pdf

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import com.example.pdf.android.toast.showToast
import com.example.pdf.biz.fcm.testFcmServiceNotification
import com.example.pdf.biz.notification.FixedNotiService
import com.example.pdf.biz.notification.NotificationPermissionRequester
import com.example.pdf.coroutine.AppCoroutineScope
import com.example.pdf.kermit.debugLog
import com.example.pdf.ui.activity.splashActivity
import com.example.pdf.ui.node.home.tools.areNotificationsEnabled
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.android.ext.android.inject
import kotlin.getValue

private const val TAG = "AppActivity"

var appActivity: AppActivity? = null

class AppActivity : BaseActivity() {
  private val appCoroutineScope: AppCoroutineScope by inject()
  private val notificationPermissionRequester: NotificationPermissionRequester by inject()

  init {
    notificationPermissionRequester.registerPermissionResult(this)
  }

  override fun onCreate(savedInstanceState: Bundle?) {
    appActivity = this
    debugLog(tag = TAG) { "appActivity: $appActivity" }
    super.onCreate(savedInstanceState)
    enableEdgeToEdge()
    setContent {
      ComposeAppContainer()
    }
  }

  override fun onStart() {
    super.onStart()
    debugLog { "areNotificationsEnabled: ${areNotificationsEnabled()}" }

    with(appCoroutineScope) {
      launch(Dispatchers.Default) {
        delay(3300)
        withContext(Dispatchers.Main) {
          FixedNotiService.startServiceIfNeeded(<EMAIL>)
        }


//        testFcmServiceNotification(<EMAIL>)
      }
    }
  }

  override fun onDestroy() {
    appActivity = null
    super.onDestroy()
  }
}
