package com.example.pdf.mmkv

import android.content.Context
import com.example.pdf.android.file.DocumentFilesManager
import com.example.pdf.android.file.DocumentType
import com.example.pdf.android.permission.FileAccessManager
import com.example.pdf.kermit.debugLog
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Single
import org.koin.core.context.GlobalContext
import java.io.File

@Single
class UserSettingsKvStore(context: Context) {
  companion object {
    const val TAG = "UserSettingsKvStore"
  }

  private val mmkv by lazy { MMKV.mmkvWithID(TAG) }

  val documentsSortedFlow = mmkv.getSerializableStateFlow(
    key = "documents_sorted",
    defaultValue = DocumentFilesManager.SortType.NAME_ASC
  )

  val documentTypeTabStateFlow = mmkv.getSerializableStateFlow(
    key = "document_type_tab",
    defaultValue = DocumentType.ALL
  )

  val bookmarkedDocumentPathsFlow = mmkv.getStringSetStateFlow(
    key = "bookmarked_document_paths",
    defaultValue = emptySet()
  )

  val recentDocumentsFlow = mmkv.getParcelableStateFlow<RecentDocuments>(
    key = "recent_documents",
    defaultValue = RecentDocuments(data = emptyMap())
  )

  val lastViewedDocumentsFlow get() = recentDocumentsFlow

  val displayImg2PdfReorderableTipsFlow = mmkv.getBooleanStateFlow(
    key = "display_img2pdf_reorderable_tips",
    defaultValue = true
  )

  val enabledNotification = mmkv.getBooleanStateFlow(
    key = "enabled_notification",
    defaultValue = true
  )
}

/**
 * 清理最近文档列表，移除其中已不存在的文件记录。
 * 注意：此函数是一个 suspend 函数，应在 CoroutineScope 中调用。
 */
suspend fun UserSettingsKvStore.validateAndCleanRecentDocuments() {
  val fileAccessManager = GlobalContext.get().get<FileAccessManager>()
  if (fileAccessManager.hasFileAccessPermission()) {
    // 使用 Dispatchers.IO 执行文件检查操作
    withContext(Dispatchers.IO) {
      val currentRecentDocuments = recentDocumentsFlow.value.data
      // 使用 filterKeys 创建一个新的 Map，只包含那些文件确实存在的条目
      val existingRecentDocuments = currentRecentDocuments.filterKeys { path ->
        try {
          File(path).exists()
        } catch (e: Exception) {
          // 处理可能的路径格式错误或文件系统访问异常
          // 使用 debugLog 替换 Log.w
          debugLog(tag = UserSettingsKvStore.TAG) { "Error checking file existence for path: $path, Error: ${e.message}" }
          false // 如果检查出错，也视为不存在
        }
      }

      // 只有当 Map 确实发生变化时才更新 Flow 的值
      if (existingRecentDocuments.size != currentRecentDocuments.size) {
        debugLog(tag = UserSettingsKvStore.TAG) { "Cleaning up recent documents. Removed ${currentRecentDocuments.size - existingRecentDocuments.size} entries." }
        recentDocumentsFlow.value = RecentDocuments(data = existingRecentDocuments)
      } else {
        debugLog(tag = UserSettingsKvStore.TAG) { "No non-existent recent documents found to clean up." }
      }
    }
  } else {
    debugLog(tag = UserSettingsKvStore.TAG) { "Cannot cleanup recent documents, file access permission not granted." }
  }
}

suspend fun UserSettingsKvStore.updateDocumentPathIfNeeded(
  oldPath: String,
  newPath: String
) = withContext(Dispatchers.IO) {
  val currentBookmarks = bookmarkedDocumentPathsFlow.first()
  if (currentBookmarks.contains(oldPath)) {
    val updatedBookmarks = currentBookmarks.toMutableSet().apply {
      remove(oldPath)
      add(newPath)
    }

    bookmarkedDocumentPathsFlow.value = updatedBookmarks
  }

  val currentRecentDocuments = recentDocumentsFlow.value.data
  if (currentRecentDocuments.contains(oldPath)) {
    val updatedRecentDocuments = currentRecentDocuments.toMutableMap().apply {
      val updatedRecentDocument = get(oldPath)?.copy(absolutePath = newPath)
      if (updatedRecentDocument != null) {
        remove(oldPath)
        put(newPath, updatedRecentDocument)
      }
    }
    recentDocumentsFlow.value = RecentDocuments(data = updatedRecentDocuments)
  }
}