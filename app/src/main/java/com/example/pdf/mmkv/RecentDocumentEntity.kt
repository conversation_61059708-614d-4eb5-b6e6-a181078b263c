package com.example.pdf.mmkv

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class RecentDocumentEntity(
  val absolutePath: String,
  val lastViewedTimestamp: Long
) : Parcelable

@Parcelize
data class RecentDocuments(
  val data: Map<String, RecentDocumentEntity>
) : Parcelable {

  /**
   * 添加或更新一个最近文档记录。
   * 如果具有相同路径的记录已存在，则更新它；否则，添加新记录。
   *
   * @param entity 要添加或更新的文档实体。
   * @return 包含更新后数据的新的 RecentDocuments 实例。
   */
  fun upsert(entity: RecentDocumentEntity): RecentDocuments {
    val updatedMap = data.toMutableMap()
    updatedMap[entity.absolutePath] = entity
    return RecentDocuments(data = updatedMap)
  }

  /**
   * 根据路径获取一个最近文档记录。
   *
   * @param absolutePath 文档的路径。
   * @return 如果找到，则返回对应的 RecentDocumentEntity；否则返回 null。
   */
  fun get(absolutePath: String): RecentDocumentEntity? {
    return data[absolutePath]
  }

  /**
   * 根据路径移除一个最近文档记录。
   *
   * @param path 要移除的文档的路径。
   * @return 包含更新后数据的新的 RecentDocuments 实例。如果路径不存在，则返回原始实例。
   */
  fun remove(path: String): RecentDocuments {
    if (!data.containsKey(path)) {
      return this
    }
    val updatedMap = data.toMutableMap()
    updatedMap.remove(path)
    return RecentDocuments(data = updatedMap)
  }

  /**
   * 获取所有最近文档记录的列表，按最后查看时间降序排列。
   *
   * @return 按最后查看时间排序的 RecentDocumentEntity 列表。
   */
  fun getAllSortedByLastViewed(): List<RecentDocumentEntity> {
    return data.values.sortedByDescending { it.lastViewedTimestamp }
  }
}