package com.example.pdf

import android.app.Activity
import android.app.Application
import android.os.Bundle
import com.example.pdf.biz.SplashController
import com.example.pdf.biz.analytic.TenjinHelper
import com.example.pdf.biz.bi.BiReporter
import com.example.pdf.biz.bi.DeviceInfoReader
import com.example.pdf.biz.bi.reportPageOnStartEvent
import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import com.example.pdf.coroutine.AppCoroutineScope
import com.example.pdf.kermit.debugLog
import com.example.pdf.ui.activity.SplashActivity
import com.example.pdf.ui.activity.SplashLaunchType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

private data class ActivityLifecycleCounter(
  var createdCounter: Int = 0,
  var startedCounter: Int = 0,
  var resumedCounter: Int = 0,
  var pausedCounter: Int = 0,
  var stoppedCounter: Int = 0,
  var saveInstanceStateCounter: Int = 0,
  var destroyedCounter: Int = 0,
)

object AdAppActivityLifecycleCallbacks : Application.ActivityLifecycleCallbacks, KoinComponent {

  private var _mainActivityLifecycleCounter = ActivityLifecycleCounter()
  private val _mainActivityClassName = AppActivity::class.java.name
  private val _splashActivityClassName = SplashActivity::class.java.name

  private val appCoroutineScope: AppCoroutineScope by inject()

  private val splashController: SplashController by inject()
  private val biReporter: BiReporter by inject()

  private val tenjinHelper: TenjinHelper by inject()

  private val remoteConfig: RealRemoteConfig by inject()

  override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
    if (_mainActivityClassName == activity.javaClass.name) {
      _mainActivityLifecycleCounter.createdCounter++
    }
  }

  override fun onActivityStarted(activity: Activity) {
    val activityClassName = activity.javaClass.name

    tenjinHelper.init(activity.applicationContext)
    appCoroutineScope.launch {
      DeviceInfoReader.configureUserIdIfNeeded()
    }

    if (_mainActivityClassName == activityClassName) {
      debugLog("AutoLaunchSplashAdScreenRegistrar _mainActivityLifecycleCounter: $_mainActivityLifecycleCounter")

      if (
        _mainActivityLifecycleCounter.startedCounter > 0
        && _mainActivityLifecycleCounter.createdCounter > _mainActivityLifecycleCounter.destroyedCounter
      ) {
        if (runBlocking { splashController.skipSplashFlow.first().not() }) {
          activity.startActivity(SplashActivity.createIntent(activity, type = SplashLaunchType.WARM_START))
        } else {
          splashController.skipSplash(false)
        }
      }

      _mainActivityLifecycleCounter.startedCounter++
    } else {
      appCoroutineScope.launch(Dispatchers.Default) {
        biReporter.reportPageOnStartEvent(activity.javaClass.name)
      }
    }
  }

  override fun onActivityResumed(activity: Activity) {
    if (_mainActivityClassName == activity.javaClass.name) {
      _mainActivityLifecycleCounter.resumedCounter++
    }
  }

  override fun onActivityPaused(activity: Activity) {
    if (_mainActivityClassName == activity.javaClass.name) {
      _mainActivityLifecycleCounter.pausedCounter++
    }
  }

  override fun onActivityStopped(activity: Activity) {
    if (_mainActivityClassName == activity.javaClass.name) {
      _mainActivityLifecycleCounter.stoppedCounter++
    }
  }

  override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
    if (_mainActivityClassName == activity.javaClass.name) {
      _mainActivityLifecycleCounter.saveInstanceStateCounter++
    }
  }

  override fun onActivityDestroyed(activity: Activity) {
    if (_mainActivityClassName == activity.javaClass.name) {
      _mainActivityLifecycleCounter = ActivityLifecycleCounter()
    }
  }
}