package com.example.pdf.biz.ad.nat1ve

enum class NativeAdPlace(val named: String) {
  Test("test"),
  All("all"),
  Recent("Recent"),
  Bookmarks("Bookmarks"),
  PdfPicker("pdf_picker"),
  ImgCropper("img_cropper"),
  Img2Pdf("img_2_pdf"),
  Word2Pdf("word_2_pdf"),
  Selection("selection"),
  Search("search"),
  ImagePicker("image_picker"),
  Document("document"),
  Tools("tools"),
  ConvertSuccess("convert_success"),
  Img2PdfLeaveConfirm("img_2_pdf_leave"),
  EditPdfLeaveConfirm("edit_pdf_leave"),
}

private val dialogPlace = emptyList<NativeAdPlace>()

fun NativeAdPlace.isDialogPlace(): Boolean {
  return this in dialogPlace
}