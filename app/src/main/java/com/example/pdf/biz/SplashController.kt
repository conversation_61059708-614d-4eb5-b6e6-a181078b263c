package com.example.pdf.biz

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import org.koin.core.annotation.Single
import org.koin.core.context.GlobalContext

@Single
class SplashController {
  private val _skipSplash = MutableStateFlow(false)
  val skipSplashFlow: StateFlow<Boolean> get() = _skipSplash

  fun skipSplash(doSkip: <PERSON>ole<PERSON>) {
    _skipSplash.update { doSkip }
  }
}

fun skipSplash() {
  GlobalContext.get().get<SplashController>().skipSplash(true)
}