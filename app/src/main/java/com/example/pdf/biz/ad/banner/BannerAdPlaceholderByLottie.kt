package com.example.pdf.biz.ad.banner

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import com.airbnb.lottie.AsyncUpdates
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition

@Composable
fun BannerAdPlaceholderByLottie(
  modifier: Modifier = Modifier
) {
  val composition by rememberLottieComposition(
    spec = LottieCompositionSpec.Asset("ad_banner.json")
  )

  Box(modifier = modifier) {
    LottieAnimation(
      composition = composition,
      iterations = Int.MAX_VALUE,
      modifier = modifier
        .padding(horizontal = 1.dp)
        .fillMaxWidth(),
      contentScale = ContentScale.FillBounds,
      asyncUpdates = AsyncUpdates.ENABLED
    )
  }
}