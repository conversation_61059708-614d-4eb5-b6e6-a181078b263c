package com.example.pdf.biz.bi

import com.example.pdf.BuildConfig
import com.example.pdf.kermit.debugLog
import com.example.pdf.serialization.defaultJsonSerializer
import de.jensklingenberg.ktorfit.Ktorfit
import de.jensklingenberg.ktorfit.converter.ResponseConverterFactory
import io.ktor.client.engine.okhttp.OkHttp
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.serialization.kotlinx.json.json
import org.koin.core.qualifier.named
import kotlin.time.DurationUnit
import kotlin.time.toDuration

val KTORFIT_BI = named("ktorfit_bi")

fun buildBiKtorfit(
  baseUrl: String,
): Ktorfit {
  return Ktorfit.Builder()
    .baseUrl(baseUrl)
    .httpClient(OkHttp.create {
      if (BuildConfig.DEBUG) {
        addInterceptor {
          debugLog(tag = "bi") { "${it.request().url} send" }

          it.proceed(it.request())
        }
      }
    }) {
      install(ContentNegotiation) {
        json(defaultJsonSerializer)
      }
      install(HttpTimeout) {
        requestTimeoutMillis = 5.toDuration(DurationUnit.MINUTES).inWholeMilliseconds
        socketTimeoutMillis = 5.toDuration(DurationUnit.MINUTES).inWholeMilliseconds
      }
    }
    .converterFactories(ResponseConverterFactory())
    .build()
}