package com.example.pdf.biz.remoteconfig

import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import com.example.pdf.biz.bi.BiApiRemoteConfig
import org.koin.core.annotation.Single
import org.koin.core.context.GlobalContext

val useLegacyAdConfig: Boolean
  get() {
    return GlobalContext.get().get<RealRemoteConfig>().useLegacyAdConfig
  }

@Single
class RealRemoteConfig(
  private val firebaseRemoteConfig: FirebaseRemoteConfig,
  private val apiRemoteConfig: BiApiRemoteConfig
) {
  val useLegacyAdConfig: Boolean
    get() = apiRemoteConfig.useLegacyAdConfig()
      ?: firebaseRemoteConfig.useLegacyAdConfig()

  val useGhostAdProvider: Boolean
    get() = apiRemoteConfig.useGhostAdProvider()
      ?: firebaseRemoteConfig.useGhostAdProvider()

  val adConfig1: AdConfig1
    get() = apiRemoteConfig.adConfig1() ?: firebaseRemoteConfig.adConfig1()

  val adConfig2: AdConfig2
    get() = apiRemoteConfig.adConfig2() ?: firebaseRemoteConfig.adConfig2()

  val adConfig3: AdConfig3
    get() = apiRemoteConfig.adConfig3() ?: firebaseRemoteConfig.adConfig3()

  @Composable
  fun rememberUseLegacyAdConfig(): Boolean {
    // 不需要 remember(remoteConfig)，因为 remoteConfig 是单例
    // 直接使用 derivedStateOf 来观察 useLegacyAdConfig 的变化
    val configValue by remember {
      derivedStateOf { useLegacyAdConfig }
    }

    return configValue
  }

  @Composable
  fun rememberUseGhostAdProvider(): Boolean {
    val configValue by remember {
      derivedStateOf { useGhostAdProvider }
    }

    return configValue
  }
}