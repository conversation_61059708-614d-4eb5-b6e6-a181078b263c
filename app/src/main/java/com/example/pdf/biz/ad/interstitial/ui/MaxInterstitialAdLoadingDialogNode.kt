package com.example.pdf.biz.ad.interstitial.ui

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import com.example.pdf.guia.DialogNode
import com.example.pdf.biz.ad.AdLoadingDialog
import com.example.pdf.biz.ad.interstitial.MaxInterstitialAdHelper
import com.roudikk.guia.core.Dialog
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.currentKey
import com.roudikk.guia.extensions.pop
import kotlinx.parcelize.Parcelize
import org.koin.compose.koinInject


@Parcelize
class MaxInterstitialAdLoadingDialogNode : DialogNode(
  tag = "interstitial_ad_loading_dialog",
  dialogOptions = Dialog.DialogOptions(
    dismissOnClickOutside = false,
    dismissOnBackPress = false,
  )
) {

  @Composable
  override fun Content(navigator: Navigator, dialog: Dialog?) {
    val maxInterstitialAdHelper: MaxInterstitialAdHelper = koinInject()

    LaunchedEffect(Unit) {
      maxInterstitialAdHelper.instantLoadTimeoutDelay()
      if (navigator.currentKey is MaxInterstitialAdLoadingDialogNode) {
        navigator.pop()
      }
    }

    AdLoadingDialog()
  }
}