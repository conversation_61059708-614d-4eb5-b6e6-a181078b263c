package com.example.pdf.biz.ad.interstitial

import android.app.Activity
import android.content.Context
import com.example.pdf.biz.BizConfig
import com.example.pdf.biz.SplashController
import com.example.pdf.biz.ad.FullscreenAdManager
import com.example.pdf.biz.analytic.AnalyticsLogEvent
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.biz.bi.BiReporter
import com.example.pdf.biz.bi.reportAdEvent
import com.example.pdf.biz.remoteconfig.AdConfig3
import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import com.example.pdf.coroutine.AppCoroutineScope
import com.example.pdf.flow.EventFlow
import com.example.pdf.flow.send
import com.example.pdf.kdatetime.nowInstant
import com.example.pdf.kermit.debugLog
import com.tradplus.ads.open.TradPlusSdk
import com.tradplus.ads.base.bean.TPAdError
import com.tradplus.ads.base.bean.TPAdInfo
import com.tradplus.ads.open.interstitial.InterstitialAdListener
import com.tradplus.ads.open.interstitial.TPInterstitial
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.datetime.Instant
import org.koin.core.annotation.Single
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.time.Duration.Companion.seconds

private const val AD_CACHE_PERIOD_SECONDS = 55 * 60L

@Single
class TradPlusInterstitialAdManager(
  private val splashController: SplashController,
  private val fullscreenAdManager: FullscreenAdManager,
  private val remoteConfig: RealRemoteConfig,
  private val appCoroutineScope: AppCoroutineScope,
  private val biReporter: BiReporter,
) {
  @Suppress("PrivatePropertyName")
  private val TAG = "TradplusInterstitialAdManager"

  private val config: AdConfig3
    get() = remoteConfig.adConfig3

  private val adKey: String
    get() {
      return if (InterAdKeyController.useNextAdKey(config.next_inter_ad_key_active_interval_minutes)) {
        debugLog(tag = TAG) { "adKey use key2" }
        config.inter_ad_key2.ifEmpty { BizConfig.TRAD_PLUS_INTERSTITIAL_AD_UNIT_ID_2 }
      } else {
        debugLog(tag = TAG) { "adKey use key1" }
        config.inter_ad_key.ifEmpty { BizConfig.TRAD_PLUS_INTERSTITIAL_AD_UNIT_ID }
      }
    }

  private var tpInterstitial: TPInterstitial? = null
  private val isLoadingAd = AtomicBoolean(false)
  private var loadingStartTime: Long = 0
  private var latestLoadAdSuccessInstant: Instant = Instant.fromEpochSeconds(0)

  private val latestActiveAdPlaceNameFlow = MutableStateFlow<String?>(null)

  val adLoadingStateEventFlow = EventFlow<InterstitialAdLoadingStateEvent>()
  val adShowStateEventFlow = EventFlow<InterstitialAdShowStateEvent>()

  val adShowStateFlow = MutableStateFlow(false)

  private var sendLoadingTimeOutJob: Job? = null

  fun tryToLoadAd(context: Context) {
    debugLog(tag = TAG) { "tryToLoadAd" }

    // 兜底检查：如果加载时间过长，强制重置
    if (isLoadingTooLong()) {
      debugLog(tag = TAG) { "Loading too long, force reset" }
      resetLoadingState()
    }

    if (isLoadingAd.get()) return

    if (isAdAvailable()) {
      debugLog(tag = TAG) { "hasAdAvailable" }
    } else {
      debugLog(tag = TAG) { "noAdAvailable" }
      loadAd(context)
    }
  }

  private fun loadAd(context: Context) {
    sendLoadingTimeOutJob?.cancel()
    sendLoadingTimeOutJob = null
    sendLoadingTimeOutJob = appCoroutineScope.launch(Dispatchers.Default) {
      delay(config.inter_ad_loading_timeout_seconds.seconds)
      debugLog(tag = TAG) { "send(AdLoadingStateEvent.TimeOut)" }
      adLoadingStateEventFlow.send(InterstitialAdLoadingStateEvent.TimeOut)
      async(Dispatchers.Main.immediate) { resetLoadingState() }
    }

    if (isLoadingAd.get()) {
      debugLog(tag = TAG) { "do not loadAd, cuz isLoadingAd" }
      return
    }

    // Check if TradPlus SDK is initialized
    if (!TradPlusSdk.getIsInit()) {
      debugLog(tag = TAG) { "TradPlus SDK not initialized yet, cannot load ad" }
      resetLoadingState()
      adLoadingStateEventFlow.send(InterstitialAdLoadingStateEvent.FailedToLoad)
      return
    }

    debugLog(tag = TAG) { "loadAd" }

    startLoading()

    // Initialize TPInterstitial
    tpInterstitial = TPInterstitial(context, adKey)

    // Set ad scenario entry (optional)
    latestActiveAdPlaceNameFlow.value?.let { scenarioId ->
      tpInterstitial?.entryAdScenario(scenarioId)
    }

    // Set listener
    tpInterstitial?.setAdListener(object : InterstitialAdListener {
      override fun onAdLoaded(adInfo: TPAdInfo?) {
        debugLog(tag = TAG) { "onAdLoaded" }
        resetLoadingState()
        latestLoadAdSuccessInstant = nowInstant()

        adLoadingStateEventFlow.send(InterstitialAdLoadingStateEvent.Loaded)
        debugLog(tag = TAG) { "send(AdLoadingStateEvent.Loaded)" }

        logEventRecord("ad_interstitial_load_success")
      }

      override fun onAdFailed(error: TPAdError?) {
        debugLog(tag = TAG) { "onAdFailed: ${error?.errorMsg}" }
        resetLoadingState()
        adLoadingStateEventFlow.send(InterstitialAdLoadingStateEvent.FailedToLoad)
      }

      override fun onAdClicked(adInfo: TPAdInfo?) {
        splashController.skipSplash(true)
        logEventRecord("ad_interstitial_click")
        debugLog(tag = TAG) { "onAdClicked" }
      }

      override fun onAdImpression(adInfo: TPAdInfo?) {
        logEventRecord("ad_interstitial_impress")
        debugLog(tag = TAG) { "onAdImpression" }

        fullscreenAdManager.latestShowAdSuccessInstant = nowInstant()
        adShowStateEventFlow.send(InterstitialAdShowStateEvent.Showing)
        adShowStateFlow.update { true }

        // Record revenue analytics
        adInfo?.let { info ->
          val adFormat = "interstitial"

          AnalyticsLogEvent.tryToRecordTotalAdsRevenue001(info)
          AnalyticsLogEvent.recordAdImpressionRevenue(info, adFormat)
          AnalyticsLogEvent.recordAdImpression(info, adFormat)
          AnalyticsLogEvent.tenjinEventAdImpressionTradPlus(info)
        }
      }


      override fun onAdVideoStart(adInfo: TPAdInfo?) {
        debugLog(tag = TAG) { "onAdVideoStart" }

      }

      override fun onAdVideoEnd(adInfo: TPAdInfo?) {
        debugLog(tag = TAG) { "onAdVideoEnd" }
      }

      override fun onAdVideoError(adInfo: TPAdInfo?, error: TPAdError?) {
        debugLog(tag = TAG) { "onAdVideoError: ${error?.errorMsg}" }
        tpInterstitial = null
        loadAd(context)
        adShowStateEventFlow.send(InterstitialAdShowStateEvent.FailedToShow)
        adShowStateFlow.update { false }
      }

      override fun onAdClosed(adInfo: TPAdInfo?) {
        debugLog(tag = TAG) { "onAdClosed" }
        tpInterstitial?.onDestroy()
        tpInterstitial = null
        loadAd(context)
        adShowStateEventFlow.send(InterstitialAdShowStateEvent.Finish)
        appCoroutineScope.launch {
          delay(2000)
          adShowStateFlow.emit(false)
        }
      }
    })

    // Load the ad
    tpInterstitial?.loadAd()

    logEventRecord("ad_interstitial_load")
  }

  private fun isAdAvailable(): Boolean {
    return tpInterstitial?.isReady() == true && checkAdIsValidAtCachePeriod()
  }

  private fun checkAdIsValidAtCachePeriod(adCachePeriodSeconds: Long = AD_CACHE_PERIOD_SECONDS): Boolean {
    val secondsDifference: Long =
      nowInstant().epochSeconds - latestLoadAdSuccessInstant.epochSeconds
    return secondsDifference < adCachePeriodSeconds
  }

  private fun showAd(activity: Activity) {
    debugLog(tag = TAG) { "showAd" }

    logEventRecord("ad_interstitial_show")

    if (tpInterstitial?.isReady() == true) {
      tpInterstitial?.showAd(activity, "")
    } else {
      debugLog(tag = TAG) { "showAd failed: ad not ready" }
      adShowStateEventFlow.send(InterstitialAdShowStateEvent.FailedToShow)
      adShowStateFlow.update { false }
    }
  }

  suspend fun tryToShowAd(
    activity: Activity,
    adPlaceName: String? = null,
    immediate: Boolean = false,
    onReadyShowAd: (() -> Unit)? = null
  ) = withContext(Dispatchers.Main.immediate) {
    debugLog(tag = TAG) { "tryToShowAd" }

    adPlaceName?.let {
      latestActiveAdPlaceNameFlow.update { "inter_$adPlaceName" }
    }

    if (fullscreenAdManager.isAdShowTimeInShowInterval()) {
      debugLog(tag = TAG) { "isAdShowTimeInShowInterval" }
      adShowStateEventFlow.send(InterstitialAdShowStateEvent.SkipToShow)
    } else { // over the show interval, need to show ad
      appCoroutineScope.launch(Dispatchers.Default) {
        if (adPlaceName != null) {
          val currentAdPlaceName = latestActiveAdPlaceNameFlow.first() ?: ""
          biReporter.reportAdEvent(adType = "interstitial", adPlace = currentAdPlaceName)
        }
      }

      onReadyShowAd?.invoke()
      debugLog(tag = TAG) { "over the show interval, need to show ad" }
      if (isAdAvailable()) { // cache available
        debugLog(tag = TAG) { "cache available" }
        if (!immediate) {
          delay(1_000)
        }
        showAd(activity)
      } else { // cache not available
        debugLog(tag = TAG) { "cache not available" }
        loadAd(activity)
      }
    }
  }

  private fun startLoading() {
    loadingStartTime = System.currentTimeMillis()
    isLoadingAd.set(true)
  }

  private fun resetLoadingState() {
    loadingStartTime = 0
    isLoadingAd.set(false)
    sendLoadingTimeOutJob?.cancel()
    sendLoadingTimeOutJob = null
  }

  private fun isLoadingTooLong(): Boolean {
    return isLoadingAd.get() &&
      loadingStartTime > 0 &&
      (System.currentTimeMillis() - loadingStartTime) >= 60_000L
  }

  fun onDestroy() {
    debugLog(tag = TAG) { "onDestroy" }
    resetLoadingState()
    tpInterstitial?.onDestroy()
    tpInterstitial = null
  }
}