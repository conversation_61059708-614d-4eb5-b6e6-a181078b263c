package com.example.pdf.biz.bi

import android.content.Context
import com.tenjin.android.params.TenjinParams
import com.tenjin.android.store.SharedPrefsStore
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import java.util.UUID

object DeviceInfoReader : KoinComponent {

  private val context: Context by inject()
  private val biPrefs: BiPrefs by inject()

  suspend fun configureUserIdIfNeeded() {
    if (!userId().isNullOrEmpty()) return

    val adId = adId()
    if (!adId.isNullOrEmpty() && adIdIsValid(adId)) {
      biPrefs.storeUserId(adId)
      return
    }

    val mmpId = mmpId()
    if (!mmpId.isNullOrEmpty()) {
      biPrefs.storeUserId(mmpId)
      return
    }

    val uuid = UUID.randomUUID().toString()
    biPrefs.storeUserId(uuid)
  }

  suspend fun biEventHeader(): BiEventHeader? {
    val userId = userId() ?: return null
    val tenjinId = tenjinId() ?: ""
    val appInstanceId = appInstanceId() ?: ""

    return BiEventHeader(
      user_id = userId,
      app_instance_id = appInstanceId,
      tenjin_id = tenjinId,
    )
  }

  private suspend fun adId() = biPrefs.adId()

  private fun adIdIsValid(adId: String): Boolean {
    var s = ""

    adId.forEach {
      if (it.isLetterOrDigit()) {
        s += it
      }
    }

    s.forEach {
      if (!it.isDigit()) {
        return true
      } else if (it != '0') {
        return true
      }
    }

    return false
  }

  private fun mmpId() = tenjinId()

  private fun tenjinId(): String? {
    return TenjinParams(SharedPrefsStore(context)).analyticsInstallationID ?: null
  }

  private suspend fun appInstanceId(): String? = biPrefs.appInstanceId()

  fun userId() = biPrefs.userId

}