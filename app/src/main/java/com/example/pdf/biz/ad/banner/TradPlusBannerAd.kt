package com.example.pdf.biz.ad.banner

import android.content.Context
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.animation.Crossfade
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.koin.core.parameter.parametersOf
import androidx.core.view.isEmpty
import com.example.pdf.android.lifecycle.OnLifecycleEvent
import com.example.pdf.biz.ad.AdColors
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.kermit.debugLog

@Composable
fun TradPlusBannerAd(
  adPlace: BannerAdPlace,
  modifier: Modifier = Modifier,
) {
  @Suppress("UnusedVariable", "unused")
  val viewModel: TradPlusBannerAdViewModel = koinViewModel { parametersOf(adPlace) }

  val context = LocalContext.current
  val scope = rememberCoroutineScope()
  val bannerAdManager: TradPlusBannerAdManager = koinInject()

  val tpBanner by bannerAdManager.getBannerFlow(adPlace).collectAsState()
  val adState by bannerAdManager.getAdStateFlow(adPlace).collectAsState()

  var adContainer by remember { mutableStateOf(context.buildFrameLayout()) }

  OnLifecycleEvent { _, event ->
    when (event) {
      Lifecycle.Event.ON_START -> scope.launch {
        bannerAdManager.resume(adPlace)
      }

      Lifecycle.Event.ON_RESUME -> scope.launch {
        bannerAdManager.resume(adPlace)
      }

      Lifecycle.Event.ON_STOP -> scope.launch {
        bannerAdManager.pause(adPlace)
      }

      else -> {}
    }
  }

  DisposableEffect(Unit) {
    logEventRecord("ad_banner_show")

    if (tpBanner == null) {
      debugLog(tag = "TRADPLUS_BANNER_AD") { "bannerAdManager.buildAd(adPlace)" }
      bannerAdManager.buildAd(adPlace)
    }

    onDispose {
      adContainer.removeAllViews()
    }
  }

  LaunchedEffect(tpBanner) {
    if (tpBanner != null && adContainer.isEmpty()) {
      debugLog(tag = "TRADPLUS_BANNER_AD") { "showBanner for place: ${adPlace.name}" }
      bannerAdManager.showBanner(adPlace, adContainer)
    }
  }

  val bannerLayoutModifier = modifier.height(80.dp)

  Box(
    modifier = bannerLayoutModifier.background(color = AdColors.backgroundColor),
    contentAlignment = Alignment.Center
  ) {
    Crossfade(
      targetState = tpBanner != null && adState.isReady,
    ) { isReady ->
      if (isReady) {
        AndroidView(
          factory = { _ ->
            adContainer
          },
          modifier = Modifier
            .clip(RoundedCornerShape(4.dp))
            .fillMaxWidth()
            .padding(vertical = 12.dp)
        )
      } else {
        BannerAdPlaceholderByLottie()
      }
    }
  }
}

private fun Context.buildFrameLayout() = FrameLayout(this).apply {
  layoutParams = ViewGroup.LayoutParams(
    ViewGroup.LayoutParams.MATCH_PARENT,
    ViewGroup.LayoutParams.WRAP_CONTENT
  )
  setBackgroundColor(Color.White.toArgb())
}

@Preview
@Composable
private fun TradPlusBannerAdPreview() {
  TradPlusBannerAd(adPlace = BannerAdPlace.Test)
}