package com.example.pdf.biz.rating

import android.app.Activity
import android.content.Context
import com.example.pdf.biz.PrefStore
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.coroutine.AppCoroutineScope
import com.example.pdf.guia.GlobalNavigator
import com.example.pdf.kdatetime.nowInstant
import com.example.pdf.kdatetime.todayStartInstant
import com.example.pdf.kermit.debugLog
import com.google.android.play.core.review.ReviewManagerFactory
import com.roudikk.guia.extensions.push
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.datetime.Instant
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import kotlin.time.Duration.Companion.days

object RatingHelper : KoinComponent {
  private const val TAG = "RatingHelper"

  private val doRatingStateFlow = MutableStateFlow(false)

  private val context: Context by inject()
  private val appCoroutineScope: AppCoroutineScope by inject()
  private val mmkv: MMKV by inject()
  private val prefStore: PrefStore by inject()
  private val reviewManager by lazy { ReviewManagerFactory.create(context) }

  fun tryToOpenReviews() {
    appCoroutineScope.launch(Dispatchers.Main.immediate) {
      val ratingTimes = ratingTimes()
      if (!doRatingStateFlow.first() || ratingTimes() >= 3) return@launch

      val firstTimeLaunchAppInstant = prefStore.firstTimeLaunchAppInstant() ?: Instant.DISTANT_PAST
      val firstTimeLaunchAppInstantTodayStartInstant = firstTimeLaunchAppInstant.todayStartInstant()

      val now = nowInstant()

      val is1stDay = firstTimeLaunchAppInstantTodayStartInstant < now
      val isOver1Days =
        firstTimeLaunchAppInstantTodayStartInstant < now.minus(1.days)
      val isOver6Days =
        firstTimeLaunchAppInstantTodayStartInstant < now.minus(6.days)

      val needRating = when {
        ratingTimes == 0 && is1stDay -> true
        ratingTimes == 1 && isOver1Days-> true
        ratingTimes == 2 && isOver6Days-> true
        else -> false
      }

      if (needRating) {
        GlobalNavigator.tryTransaction {
          push(RatingGuideDialogNode())
        }
        ratingIncrease()
        setCanRatting(false)
      }
    }
  }

  fun openGpInAppReviews(activity: Activity) {
    reviewManager.requestReviewFlow().addOnCompleteListener { task ->
      debugLog(tag = TAG, message = "requestReviewFlow task.isSuccessful: ${task.isSuccessful}")
      runCatching {
        debugLog(tag = TAG, message = "requestReviewFlow task.result: ${task.result}")
      }
      if (task.isSuccessful) {
        runCatching {
          reviewManager.launchReviewFlow(activity, task.result)
            .addOnCompleteListener {
              debugLog(
                tag = TAG,
                message = "launchReviewFlow it.isSuccessful: ${it.isSuccessful}"
              )
              debugLog(tag = TAG, message = "launchReviewFlow it.result: ${it.result}")

              logEventRecord("gp_review_display")
              ratingIncrease(3)
            }
        }
      }
    }
  }

  fun setCanRatting(canDo: Boolean) {
    doRatingStateFlow.update { canDo }
  }

  suspend fun willBeRatting() = doRatingStateFlow.first()

  private fun ratingTimes(): Int {
    return mmkv.decodeInt("${TAG}_rating_times", 0)
  }

  private fun ratingIncrease(increaseTo: Int? = null) {
    mmkv.encode("${TAG}_rating_times", (increaseTo ?: ratingTimes()) + 1)
  }
}