package com.example.pdf.biz.ad.banner

import android.content.Context
import androidx.lifecycle.ViewModel
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdFormat
import com.applovin.mediation.MaxAdRevenueListener
import com.applovin.mediation.MaxAdViewAdListener
import com.applovin.mediation.MaxError
import com.applovin.mediation.ads.MaxAdView
import com.example.pdf.biz.BizConfig
import com.example.pdf.biz.SplashController
import com.example.pdf.biz.analytic.logEventApplovinMaxAdRevenue
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import com.example.pdf.kermit.debugLog
import org.koin.android.annotation.KoinViewModel
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

@KoinViewModel
class MaxBannerAdViewModel(
  private val splashController: SplashController,
  private val remoteConfig: RealRemoteConfig,
) : ViewModel(), ContainerHost<MaxBannerAdViewState, Nothing> {

  override val container: Container<MaxBannerAdViewState, Nothing> =
    container(MaxBannerAdViewState())

  private val adKey
    get() = remoteConfig.adConfig1.banner_ad_key.ifEmpty { BizConfig.APPLOVIN_MAX_BANNER_AD }

  private val adListener = object : MaxAdViewAdListener {
    override fun onAdLoaded(ad: MaxAd) {
      intent {
        reduce { state.copy(loaded = true) }
      }
    }

    override fun onAdLoadFailed(adUnitId: String, error: MaxError) {
      intent {
        reduce { state.copy(loaded = false) }
      }
    }

    override fun onAdHidden(ad: MaxAd) {
    }

    override fun onAdDisplayFailed(ad: MaxAd, error: MaxError) {
    }

    override fun onAdDisplayed(ad: MaxAd) {
      debugLog("ad_banner_impress")
      logEventRecord("ad_banner_impress")
    }

    override fun onAdClicked(ad: MaxAd) {
      splashController.skipSplash(true)
      logEventRecord("ad_banner_click")
      debugLog("ad_banner_click")
    }

    override fun onAdExpanded(ad: MaxAd) {
    }

    override fun onAdCollapsed(ad: MaxAd) {
    }
  }

  private val revenueListener = MaxAdRevenueListener { ad ->
    debugLog("banner onAdRevenuePaid")
    logEventApplovinMaxAdRevenue(ad)
  }

  private fun buildBannerAdView(adUnitId: String, context: Context): MaxAdView {
    return MaxAdView(adUnitId, MaxAdFormat.BANNER, context).apply {
      setListener(adListener)
      setRevenueListener(revenueListener)
      loadAd()
    }
  }

  fun onConfigureAdView(context: Context) = intent {
    if (state.adView == null) {
      val newBuildBannerAdView = buildBannerAdView(adKey, context)

      reduce { state.copy(adView = newBuildBannerAdView) }
    }
  }

  @Suppress("MemberVisibilityCanBePrivate")
  fun destroyAdView() = intent {
    state.adView?.destroy()
    reduce { MaxBannerAdViewState() }
  }

  override fun onCleared() {
    destroyAdView()
    super.onCleared()
  }
}