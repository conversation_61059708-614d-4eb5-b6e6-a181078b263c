package com.example.pdf.biz.ad

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Surface
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.progressindicators.CircularProgressIndicator
import com.example.pdf.ui.composable.BlankSpacer
import cafe.adriel.lyricist.LocalStrings
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update

val AdLoadingDialogShowStateFlow = MutableStateFlow(false)

@Composable
fun AdLoadingDialog() {
  DisposableEffect(Unit) {
    AdLoadingDialogShowStateFlow.update { true }
    onDispose { AdLoadingDialogShowStateFlow.update { false } }
  }

  Dialog(onDismissRequest = {}) {
    Surface(shape = RoundedCornerShape(12.dp)) {
      Column(
        modifier = Modifier
          .padding(horizontal = 16.dp)
          .padding(top = 20.dp, bottom = 18.dp),
        horizontalAlignment = Alignment.CenterHorizontally
      ) {
        CircularProgressIndicator(modifier = Modifier.size(56.dp))

        BlankSpacer(12.dp)

        Text(
          text = LocalStrings.current.loadingAd,
          style = AppTheme.typography.h4,
          modifier = Modifier.offset(x = 2.dp)
        )
      }
    }
  }
}

@Preview
@Composable
private fun AdLoadingDialogPreview() {
  AdLoadingDialog()
}