package com.example.pdf.biz

import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.core.app.PendingIntentCompat
import androidx.core.content.ContextCompat
import com.example.pdf.appContext
import com.example.pdf.biz.fcm.cancelFcmServiceNotificationJob
import com.example.pdf.biz.fcm.isFcmServiceNotification
import com.example.pdf.kermit.debugLog

class NotificationSwipeReceiver : BroadcastReceiver() {

  companion object {
    private const val TAG = "NotificationSwipeReceiver"
    private val ACTION_NOTIFICATION_DISMISSED =
      "${appContext.packageName}.ACTION_NOTIFICATION_DISMISSED"

    private const val NOTIFICATION_DISMISSED_ID_KEY = "${TAG}_notification_dismissed_id"

    fun register(context: Context) {
      val filter = IntentFilter().apply {
        addAction(ACTION_NOTIFICATION_DISMISSED)
      }
      ContextCompat.registerReceiver(
        context,
        NotificationSwipeReceiver(),
        filter,
        ContextCompat.RECEIVER_NOT_EXPORTED
      )
      debugLog(tag = TAG) { "NotificationSwipeReceiver registered" }
    }

    fun createPendingIntent(context: Context, notificationId: Int): PendingIntent? {
      val intent = Intent().apply {
        action = ACTION_NOTIFICATION_DISMISSED
        putExtra(NOTIFICATION_DISMISSED_ID_KEY, notificationId)
        setPackage(context.packageName)
      }

      return PendingIntentCompat.getBroadcast(
        context,
        notificationId,
        intent,
        PendingIntent.FLAG_UPDATE_CURRENT,
        false
      )
    }
  }

  override fun onReceive(context: Context, intent: Intent) {
    if (intent.action == ACTION_NOTIFICATION_DISMISSED) {
      val notificationId = intent.getIntExtra(NOTIFICATION_DISMISSED_ID_KEY, -1)
      if (notificationId == -1) return
      debugLog(tag = TAG) { "Notification dismissed with id: $notificationId" }

      if (isFcmServiceNotification(notificationId)) {
        cancelFcmServiceNotificationJob()
      }
    }
  }
}