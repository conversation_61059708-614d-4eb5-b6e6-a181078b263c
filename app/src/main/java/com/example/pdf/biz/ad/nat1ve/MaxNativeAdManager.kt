package com.example.pdf.biz.ad.nat1ve

import android.content.Context
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxError
import com.applovin.mediation.nativeAds.MaxNativeAdListener
import com.applovin.mediation.nativeAds.MaxNativeAdLoader
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.applovin.mediation.nativeAds.MaxNativeAdViewBinder
import com.example.pdf.R
import com.example.pdf.biz.BizConfig
import com.example.pdf.biz.SplashController
import com.example.pdf.biz.analytic.logEventApplovinMaxAdRevenue
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import com.example.pdf.kermit.debugLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.koin.core.annotation.Single

data class MaxNativeAd(
  val ad: MaxAd,
  val adView: MaxNativeAdView?,
  val adLoader: MaxNativeAdLoader,
)

@Single
class MaxNativeAdManager(
  private val context: Context,
  private val splashController: SplashController,
  private val realRemoteConfig: RealRemoteConfig
) {

  @Suppress("PrivatePropertyName")
  private val TAG = "MaxNativeAdManager"
  private val adUnitNameLowercase = "native"

  private val adKey
    get() = realRemoteConfig.adConfig1.native_ad_key.ifEmpty { BizConfig.APPLOVIN_MAX_NATIVE_AD }

  private val binder: MaxNativeAdViewBinder = MaxNativeAdViewBinder
    .Builder(R.layout.layout_native_ad_content)
    .setTitleTextViewId(R.id.ad_headline)
    .setBodyTextViewId(R.id.ad_body)
    .setAdvertiserTextViewId(R.id.ad_advertiser)
    .setIconImageViewId(R.id.ad_app_icon)
    .setMediaContentViewGroupId(R.id.ad_media)
    .setOptionsContentViewGroupId(R.id.ad_options)
//        .setStarRatingContentViewGroupId(R.id.star_rating_view)
    .setCallToActionButtonId(R.id.ad_call_to_action)
    .build()

  private val adFlowMap: MutableMap<NativeAdPlace, MutableStateFlow<MaxNativeAd?>> =
    mutableMapOf()

  private fun adLoader(
    place: NativeAdPlace
  ) = MaxNativeAdLoader(adKey).apply {
    setRevenueListener { ad ->
      logEventApplovinMaxAdRevenue(ad)
    }

    setNativeAdListener(object : MaxNativeAdListener() {
      override fun onNativeAdLoaded(loadedNativeAdView: MaxNativeAdView?, ad: MaxAd) {
        logEventRecord("ad_${adUnitNameLowercase}_load_success")
        debugLog(tag = TAG) { "onNativeAdLoaded" }
        adFlow(place).update { MaxNativeAd(ad, loadedNativeAdView, this@apply) }
      }

      override fun onNativeAdLoadFailed(adUnitId: String, error: MaxError) {
        debugLog(tag = TAG) { "onNativeAdLoadFailed" }
      }

      override fun onNativeAdClicked(ad: MaxAd) {
        splashController.skipSplash(true)
        logEventRecord("ad_${adUnitNameLowercase}_click")
        debugLog(tag = TAG) { "onNativeAdClicked" }
      }

      override fun onNativeAdExpired(nativeAd: MaxAd) {
      }
    })
  }

  fun adFlow(place: NativeAdPlace): MutableStateFlow<MaxNativeAd?> {
    return adFlowMap.getOrPut(place) { MutableStateFlow(null) }
  }

  fun buildAd(place: NativeAdPlace, context: Context) {
    logEventRecord("ad_${adUnitNameLowercase}_load")
    debugLog(tag = TAG) { "buildAd(${place.named})" }

    val maxNativeAd = MaxNativeAdView(binder, context)

    adLoader(place).apply {
      loadAd(maxNativeAd)
    }
  }

  fun destroy(place: NativeAdPlace) {
    GlobalScope.launch(Dispatchers.Main.immediate) {
      adFlowMap[place]?.first()?.run {
        adLoader.destroy(ad)
        adLoader.destroy()
      }
      adFlowMap[place]?.update { null }
    }
  }

  fun destroyAll() {
    GlobalScope.launch {
      adFlowMap.keys.forEach(::destroy)
    }
  }

  @Suppress("RedundantIf")
  fun needToBlockSomeClickAreas(ad: MaxAd): Boolean {
    return if (
      ad.networkName.contains("admob", ignoreCase = true)
      || ad.networkName.contains("facebook", ignoreCase = true)
    ) {
      true
    } else {
      false
    }
  }
}