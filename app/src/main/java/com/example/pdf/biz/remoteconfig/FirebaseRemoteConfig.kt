package com.example.pdf.biz.remoteconfig

import android.content.Context
import com.example.pdf.configureTradPlusSdk
import com.example.pdf.coroutine.AppCoroutineScope
import com.example.pdf.serialization.toObj
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.ConfigUpdate
import com.google.firebase.remoteconfig.ConfigUpdateListener
import com.google.firebase.remoteconfig.FirebaseRemoteConfigException
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings
import com.google.firebase.remoteconfig.FirebaseRemoteConfigValue
import com.google.firebase.remoteconfig.get
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.datetime.Instant
import org.koin.core.annotation.Single
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

@Single
class FirebaseRemoteConfig(
  private val context: Context,
  private val appCoroutineScope: AppCoroutineScope,
) {
  companion object {
    private const val TAG = "FirebaseRemoteConfig"
  }

  init {
    Firebase.remoteConfig.addOnConfigUpdateListener(object : ConfigUpdateListener {
      override fun onUpdate(configUpdate: ConfigUpdate) {
        appCoroutineScope.launch(Dispatchers.Default) {
          configureTradPlusSdk(context)
        }
      }

      override fun onError(error: FirebaseRemoteConfigException) {
      }
    })
  }

  object RemoteConfigPref : KoinComponent {

    private const val TAG = "RemoteConfigPref"

    private val mmkv: MMKV by inject()

    private const val LATEST_UPDATE_INSTANT_SECOND_KEY = TAG + "_latestUpdateInstantSecond"
    val latestUpdateInstantSeconds: Long
      get() = mmkv.decodeLong(
        LATEST_UPDATE_INSTANT_SECOND_KEY,
        0L
      )

    fun setLatestUpdateInstantSeconds(instant: Instant) {
      mmkv.encode(LATEST_UPDATE_INSTANT_SECOND_KEY, instant.epochSeconds)
    }
  }

  private val remoteConfigPref = RemoteConfigPref

  private var hasSetMinimumFetchInterval = false
  private val _remoteConfig
    get() = Firebase.remoteConfig.apply {
      if (!hasSetMinimumFetchInterval) {
        setConfigSettingsAsync(
          FirebaseRemoteConfigSettings.Builder().setMinimumFetchIntervalInSeconds(60 * 60L).build()
        ).addOnSuccessListener {
          hasSetMinimumFetchInterval = true
        }
      }
    }

  fun tryToUpdateConfig(now: Instant) {
    val latestUpdateInstantSeconds = remoteConfigPref.latestUpdateInstantSeconds

    if (now.epochSeconds - latestUpdateInstantSeconds >= 60 * 60L) { // 1 hour
      fetchAndActivate { isSuccessful ->
        if (isSuccessful) {
          remoteConfigPref.setLatestUpdateInstantSeconds(now)
        }
      }
    }
  }

  fun fetchAndActivate(onCompleteBlock: ((isSuccessful: Boolean) -> Unit)? = null) {
    _remoteConfig.fetchAndActivate().addOnCompleteListener {
      onCompleteBlock?.invoke(it.isSuccessful)
    }
  }

  fun reset() {
    _remoteConfig.reset()
  }

  fun useLegacyAdConfig(): Boolean {
    val key = RemoteConfigKey.USE_LEGACY_AD_CONFIG

    return _remoteConfig.getBoolean(key)
  }

  fun adConfig1(): AdConfig1 {
    val key = RemoteConfigKey.AD_CONFIG_1

    return _remoteConfig[key].toObjOrNull() ?: AdConfig1.Default
  }

  fun adConfig2(): AdConfig2 {
    val key = RemoteConfigKey.AD_CONFIG_2

    return _remoteConfig[key].toObjOrNull() ?: AdConfig2.Default
  }

  fun adConfig3(): AdConfig3 {
    val key = RemoteConfigKey.AD_CONFIG_3

    return _remoteConfig[key].toObjOrNull() ?: AdConfig3.Default
  }

  private inline fun <reified T> FirebaseRemoteConfigValue.toObjOrNull(): T? {
    return try {
      asString().toObj<T>()
    } catch (_: Exception) {
      return null
    }
  }
}
