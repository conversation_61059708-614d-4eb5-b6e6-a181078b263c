package com.example.pdf.biz.fcm

import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.content.getSystemService
import com.example.pdf.R
import com.example.pdf.biz.bi.BiRecordCkEvent
import com.example.pdf.biz.notification.NotificationActionNavigator
import com.example.pdf.guia.ScreenNode
import com.example.pdf.kermit.debugLog
import com.example.pdf.serialization.toJson
import com.example.pdf.ui.node.home.HomeNode
import org.koin.core.annotation.Single

@Single
class SimpleFirebaseMessagingNotification(
  private val context: Context,
) {

  companion object {
    const val EXTRA_KEY_FCM_NOTI_TYPE = "noti_type"

    private const val TAG = "FcmNotification"

    private const val NOTI_ID_STARTING_NUMBER = 10000
  }


  private val androidNotificationManager
    get() = context.getSystemService<NotificationManager>()


  fun notify(
    context: Context,
    title: String,
    content: String,
    fcmNotiType: Int,
    clickCkEvent: BiRecordCkEvent?
  ) {
    notify(
      context = context,
      title = title,
      content = content,
      fcmNotiType = fcmNotiType,
      clickCkEventJson = clickCkEvent?.toJson()
    )
  }

  fun notify(
    context: Context,
    title: String,
    content: String,
    fcmNotiType: Int,
    clickCkEventJson: String?
  ) {
    debugLog(tag = TAG) { "$TAG notify() fcmNotiType: $fcmNotiType" }

    val screenNode = notiTypeToScreenNode(fcmNotiType)

    val notificationId = NOTI_ID_STARTING_NUMBER + fcmNotiType
    val channelId = TAG

    val navIntent = NotificationActionNavigator.createNavigateIntent(
      context = context,
      notificationId = notificationId,
      screenNode = screenNode,
      clickCkEventJson = clickCkEventJson
    )

    val notificationBuilder = NotificationCompat.Builder(
      context, channelId
    ).apply {
      setSmallIcon(R.drawable.ic_pdf)
      setContentTitle(title)
      setContentText(content)
      setContentIntent(navIntent)
    }

    createNotificationChannel(context, channelId)

    androidNotificationManager?.notify(notificationId, notificationBuilder.build())
  }

  fun notiTypeToScreenNode(fcmNotiType: Int): ScreenNode {
    return when (fcmNotiType) {
      else -> HomeNode()
    }
  }

  private fun createNotificationChannel(
    context: Context,
    channelId: String
  ) {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
      val serviceChannel = NotificationChannel(
        channelId,
        context.getString(R.string.app_name),
        NotificationManager.IMPORTANCE_HIGH
      )

      androidNotificationManager?.createNotificationChannel(serviceChannel)
    }
  }
}