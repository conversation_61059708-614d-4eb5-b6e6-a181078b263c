package com.example.pdf.biz

import com.example.pdf.kdatetime.nowInstant
import com.tencent.mmkv.MMKV
import kotlinx.datetime.Instant
import org.koin.core.annotation.Single

@Suppress("PrivatePropertyName")
@Single
class PrefStore(
  private val mmkv: MMKV
) {
  companion object {
    private const val TAG = "PrefStore"
  }

  // ----------------------------------------------------------------------------------------------
  private val KEY_1ST_LAUNCH_APP_INSTANT = TAG + "_1st_launch_app_instant"
  fun storeFirstTimeLaunchAppInstant(now: Instant = nowInstant()) =
    mmkv.encode(KEY_1ST_LAUNCH_APP_INSTANT, now.epochSeconds)

  fun firstTimeLaunchAppInstant(): Instant? {
    val decodeLong = mmkv.decodeLong(KEY_1ST_LAUNCH_APP_INSTANT, -1L)
    return if (decodeLong == -1L) {
      null
    } else {
      Instant.fromEpochSeconds(decodeLong)
    }
  }

  // ----------------------------------------------------------------------------------------------

  private val KEY_SELECT_LANGUAGE_FINISH = TAG + "_select_language_finish"
  fun saveSelectLanguageFinish() {
    mmkv.encode(KEY_SELECT_LANGUAGE_FINISH, true)
  }

  val selectLanguageFinish get() = mmkv.decodeBool(KEY_SELECT_LANGUAGE_FINISH, false)

  // ----------------------------------------------------------------------------------------------
}