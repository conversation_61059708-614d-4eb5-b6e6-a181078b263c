package com.example.pdf.biz.ad.appopen

import android.R
import android.app.Activity
import android.content.Context
import android.view.ViewGroup
import com.example.pdf.biz.BizConfig
import com.example.pdf.biz.SplashController
import com.example.pdf.biz.ad.FullscreenAdManager
import com.example.pdf.biz.analytic.AnalyticsLogEvent
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.biz.bi.BiReporter
import com.example.pdf.biz.bi.reportAdEvent
import com.example.pdf.biz.remoteconfig.AdConfig3
import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import com.example.pdf.coroutine.AppCoroutineScope
import com.example.pdf.flow.EventFlow
import com.example.pdf.flow.send
import com.example.pdf.kdatetime.nowInstant
import com.example.pdf.kermit.debugLog
import com.example.pdf.ui.activity.SplashActivity
import com.tradplus.ads.open.TradPlusSdk
import com.tradplus.ads.base.bean.TPAdError
import com.tradplus.ads.base.bean.TPAdInfo
import com.tradplus.ads.base.bean.TPBaseAd
import com.tradplus.ads.open.splash.SplashAdListener
import com.tradplus.ads.open.splash.TPSplash
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.datetime.Instant
import org.koin.core.annotation.Single
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.time.Duration.Companion.seconds

private const val AD_CACHE_PERIOD_SECONDS = 55 * 60L // 55 minutes
private const val TAG = "TradPlusSplashAdManager"

@Single
class TradPlusSplashAdManager(
  private val appCoroutineScope: AppCoroutineScope,
  private val splashController: SplashController,
  private val fullscreenAdManager: FullscreenAdManager,
  private val remoteConfig: RealRemoteConfig,
  private val biReporter: BiReporter,
) {
  private var _splashAdContainer: ViewGroup? = null

  private var tpSplash: TPSplash? = null
  private val isLoadingAd = AtomicBoolean(false)
  private var loadingStartTime: Long = 0
  private var latestLoadAdSuccessInstant: Instant = Instant.fromEpochSeconds(0)

  private val config: AdConfig3 get() = remoteConfig.adConfig3

  private val adKey
    get() = config.app_open_ad_key.ifEmpty { BizConfig.TRAD_PLUS_APP_OPEN_AD_UNIT_ID }

  val adLoadingStateEventFlow = EventFlow<AppOpenAdLoadingStateEvent>()
  val adShowStateEventFlow = EventFlow<AppOpenAdShowStateEvent>()

  private var loadingTimeoutJob: Job? = null

  fun tryToLoadAd(activity: Activity) {
    debugLog(tag = TAG) { "tryToLoadAd" }

    // 兜底检查：如果加载时间过长，强制重置
    if (isLoadingTooLong()) {
      debugLog(tag = TAG) { "Loading too long, force reset" }
      resetLoadingState()
    }

    if (isLoadingAd.get()) {
      debugLog(tag = TAG) { "Already loading ad, skipping" }
      return
    }

    if (isAdAvailable()) {
      debugLog(tag = TAG) { "Ad already available, skipping load" }
      adLoadingStateEventFlow.send(AppOpenAdLoadingStateEvent.Loaded)
    } else {
      debugLog(tag = TAG) { "No ad available, loading new ad" }
      loadAd(activity)
    }
  }

  private fun loadAd(context: Context) {
    debugLog(tag = TAG) { "loadAd - TradPlus Splash" }

    // Cancel any existing timeout job
    loadingTimeoutJob?.cancel()
    loadingTimeoutJob = null

    // Set up timeout job
    loadingTimeoutJob = appCoroutineScope.launch(Dispatchers.Default) {
      delay(config.app_open_ad_loading_timeout_seconds.seconds)
      debugLog(tag = TAG) { "Ad loading timeout" }
      adLoadingStateEventFlow.send(AppOpenAdLoadingStateEvent.TimeOut)
      async(Dispatchers.Main.immediate) { resetLoadingState() }
    }

    if (isLoadingAd.get()) {
      debugLog(tag = TAG) { "Already loading ad, returning" }
      return
    }

    // Check if TradPlus SDK is initialized
    if (!TradPlusSdk.getIsInit()) {
      debugLog(tag = TAG) { "TradPlus SDK not initialized, cannot load ad" }
      adLoadingStateEventFlow.send(AppOpenAdLoadingStateEvent.FailedToLoad)
      resetLoadingState()
      return
    }

    startLoading()

    // Initialize TPSplash
    tpSplash = TPSplash(context, adKey)

    // Set ad listener
    tpSplash?.setAdListener(object : SplashAdListener() {
      override fun onAdLoaded(adInfo: TPAdInfo?, tpBaseAd: TPBaseAd?) {
        debugLog(tag = TAG) { "onAdLoaded" }
        resetLoadingState()
        latestLoadAdSuccessInstant = nowInstant()

        adLoadingStateEventFlow.send(AppOpenAdLoadingStateEvent.Loaded)

        logEventRecord("ad_splash_load_success")
      }

      override fun onAdLoadFailed(error: TPAdError?) {
        debugLog(tag = TAG) { "onAdLoadFailed: ${error?.errorMsg}" }
        adLoadingStateEventFlow.send(AppOpenAdLoadingStateEvent.FailedToLoad)
        resetLoadingState()

        logEventRecord("ad_splash_load_failed")
      }

      override fun onAdClicked(adInfo: TPAdInfo?) {
        debugLog(tag = TAG) { "onAdClicked" }
        logEventRecord("ad_splash_click")
        splashController.skipSplash(true)
      }

      override fun onAdImpression(adInfo: TPAdInfo?) {
        debugLog(tag = TAG) { "onAdImpression" }
        logEventRecord("ad_splash_impress")

        fullscreenAdManager.latestShowAdSuccessInstant = nowInstant()
        adShowStateEventFlow.send(AppOpenAdShowStateEvent.Showing)

        // Record revenue analytics
        adInfo?.let { info ->
          val adFormat = "splash"

          AnalyticsLogEvent.tryToRecordTotalAdsRevenue001(info)
          AnalyticsLogEvent.recordAdImpressionRevenue(info, adFormat)
          AnalyticsLogEvent.recordAdImpression(info, adFormat)
          AnalyticsLogEvent.tenjinEventAdImpressionTradPlus(info)
        }
      }

      override fun onAdClosed(adInfo: TPAdInfo?) {
        debugLog(tag = TAG) { "onAdClosed" }
        tpSplash?.onDestroy()
        tpSplash = null
        adShowStateEventFlow.send(AppOpenAdShowStateEvent.Finish)
        _splashAdContainer?.removeAllViews()

        // Preload next ad
        loadAd(context)
      }
    })

    // Load the ad
    tpSplash?.loadAd(null)
    logEventRecord("ad_splash_load")
  }

  fun isAdAvailable(): Boolean {
    return tpSplash?.isReady() == true && checkAdIsValidAtCachePeriod()
  }

  private fun checkAdIsValidAtCachePeriod(adCachePeriodSeconds: Long = AD_CACHE_PERIOD_SECONDS): Boolean {
    val secondsDifference: Long =
      nowInstant().epochSeconds - latestLoadAdSuccessInstant.epochSeconds
    return secondsDifference < adCachePeriodSeconds
  }

  fun showAd(activity: Activity) {
    debugLog(tag = TAG) { "showAd - TradPlus Splash" }

    if (!isAdAvailable()) {
      debugLog(tag = TAG) { "Ad not available, cannot show" }
      adShowStateEventFlow.send(AppOpenAdShowStateEvent.FailedToShow)
      return
    }

    logEventRecord("ad_splash_show")

    try {
      val splashAdContainer = if (activity is SplashActivity) {
        activity.splashAdContainer ?: activity.findViewById(R.id.content)
      } else {
        activity.findViewById(R.id.content)
      }
      _splashAdContainer = splashAdContainer

      tpSplash?.showAd(splashAdContainer)
    } catch (e: Exception) {
      debugLog(tag = TAG) { "Error showing splash ad: ${e.message}" }
      adShowStateEventFlow.send(AppOpenAdShowStateEvent.FailedToShow)
    }
  }

  suspend fun tryToShowAd(activity: Activity, immediate: Boolean = false) =
    withContext(Dispatchers.Main.immediate) {
      debugLog(tag = TAG) { "tryToShowAd, immediate: $immediate" }

      if (fullscreenAdManager.isAdShowTimeInShowInterval()) {
        debugLog(tag = TAG) { "Ad show time is in show interval, skipping" }
        adShowStateEventFlow.send(AppOpenAdShowStateEvent.SkipToShow)
        return@withContext
      }

      appCoroutineScope.launch(Dispatchers.Default) {
        if (!immediate) {
          biReporter.reportAdEvent(adType = "app_open", adPlace = "splash")
        }
      }

      if (isAdAvailable()) {
        debugLog(tag = TAG) { "Ad available, showing" }
        if (!immediate) {
          delay(1000) // Small delay before showing
        }
        showAd(activity)
      } else {
        debugLog(tag = TAG) { "Ad not available, loading first" }
        loadAd(activity)
      }
    }

  private fun startLoading() {
    loadingStartTime = System.currentTimeMillis()
    isLoadingAd.set(true)
  }

  private fun resetLoadingState() {
    loadingStartTime = 0
    isLoadingAd.set(false)
    loadingTimeoutJob?.cancel()
    loadingTimeoutJob = null
  }

  private fun isLoadingTooLong(): Boolean {
    return isLoadingAd.get() &&
      loadingStartTime > 0 &&
      (System.currentTimeMillis() - loadingStartTime) >= 60_000L
  }

  fun onDestroy() {
    debugLog(tag = TAG) { "onDestroy" }
    resetLoadingState()
    tpSplash?.onDestroy()
    tpSplash = null
  }
}