package com.example.pdf.biz.ad.interstitial

import android.app.Activity
import android.content.Context
import com.example.pdf.biz.BizConfig
import com.example.pdf.biz.SplashController
import com.example.pdf.biz.ad.FullscreenAdManager
import com.example.pdf.biz.analytic.AnalyticsLogEvent
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.biz.bi.BiReporter
import com.example.pdf.biz.bi.reportAdEvent
import com.example.pdf.biz.remoteconfig.AdConfig2
import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import com.example.pdf.coroutine.AppCoroutineScope
import com.example.pdf.flow.EventFlow
import com.example.pdf.flow.send
import com.example.pdf.kdatetime.nowInstant
import com.example.pdf.kermit.debugLog
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.OnPaidEventListener
import com.google.android.gms.ads.interstitial.InterstitialAd
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.datetime.Instant
import org.koin.core.annotation.Single
import kotlin.text.ifEmpty
import kotlin.time.Duration.Companion.seconds

private const val AD_CACHE_PERIOD_SECONDS = 55 * 60L

@Single
class AdmobInterstitialAdManager(
  private val splashController: SplashController,
  private val remoteConfig: RealRemoteConfig,
  private val appCoroutineScope: AppCoroutineScope,
  private val fullscreenAdManager: FullscreenAdManager,
  private val biReporter: BiReporter,
) {
  @Suppress("PrivatePropertyName")
  private val TAG = "InterstitialAdManager"

  private val config: AdConfig2
    get() = remoteConfig.adConfig2

  private val adKey: String
    get() {
      return if (InterAdKeyController.useNextAdKey(config.next_inter_ad_key_active_interval_minutes)) {
        debugLog(tag = TAG) { "adKey use key2" }
        config.inter_ad_key2.ifEmpty { BizConfig.ADMOB_AD_UNIT_ID_FOR_INTERSTITIAL_2 }
      } else {
        debugLog(tag = TAG) { "adKey use key1" }
        config.inter_ad_key.ifEmpty { BizConfig.ADMOB_AD_UNIT_ID_FOR_INTERSTITIAL }
      }
    }

  private var interstitialAd: InterstitialAd? = null
  private var isLoadingAd = false
  private var latestLoadAdSuccessInstant: Instant = Instant.fromEpochSeconds(0)

  private val latestActiveAdPlaceNameFlow = MutableStateFlow<String?>(null)

  val adLoadingStateEventFlow = EventFlow<InterstitialAdLoadingStateEvent>()
  val adShowStateEventFlow = EventFlow<InterstitialAdShowStateEvent>()

  val adShowStateFlow = MutableStateFlow(false)

  private var sendLoadingTimeOutJob: Job? = null

  fun tryToLoadAd(activity: Activity) {
    debugLog(tag = TAG) { "tryToLoadAd" }

    if (isLoadingAd) return

    if (isAdAvailable()) {
      debugLog(tag = TAG) { "hasAdAvailable" }
    } else {
      debugLog(tag = TAG) { "noAdAvailable" }

      loadAd(activity)
    }
  }

  private fun loadAd(context: Context) {
    sendLoadingTimeOutJob?.cancel()
    sendLoadingTimeOutJob = null
    sendLoadingTimeOutJob = appCoroutineScope.launch(Dispatchers.Default) {
      delay(config.inter_ad_loading_timeout_seconds.seconds)
      debugLog(tag = TAG) { "send(AdLoadingStateEvent.TimeOut)" }
      adLoadingStateEventFlow.send(InterstitialAdLoadingStateEvent.TimeOut)
    }

    if (isLoadingAd) {
      debugLog(tag = TAG) { "do not loadAd, cuz isLoadingAd" }
      return
    }

    debugLog(tag = TAG) { "loadAd" }

    isLoadingAd = true

    InterstitialAd.load(
      context,
      adKey,
      AdRequest.Builder().build(),
      object : InterstitialAdLoadCallback() {
        override fun onAdLoaded(ad: InterstitialAd) {
          debugLog(tag = TAG) { "onAdLoaded" }

          ad.onPaidEventListener = OnPaidEventListener { adValue ->
            val adSourceName = ad.responseInfo.loadedAdapterResponseInfo?.adSourceName
            val adFormat = "interstitial"
            val adUnitId = ad.adUnitId

            AnalyticsLogEvent.tryToRecordTotalAdsRevenue001(adValue, adSourceName)
            AnalyticsLogEvent.recordAdImpressionRevenue(
              adValue,
              adSourceName,
              adFormat,
              adPlacement = "inter_${latestActiveAdPlaceNameFlow.value}"
            )
            AnalyticsLogEvent.recordAdImpression(
              adValue,
              adSourceName,
              adFormat,
              adUnitId
            )

            AnalyticsLogEvent.tenjinEventAdImpressionAdMob(adValue, ad)
          }

          interstitialAd = ad
          isLoadingAd = false
          latestLoadAdSuccessInstant = nowInstant()

          sendLoadingTimeOutJob?.cancel()
          sendLoadingTimeOutJob = null
          adLoadingStateEventFlow.send(InterstitialAdLoadingStateEvent.Loaded)
          debugLog(tag = TAG) { "send(AdLoadingStateEvent.Loaded)" }

//                    logEventRecord("ad_${adUnitNameLowercase}_load_success")
          logEventRecord("ad_interstitial_load_success")
        }

        override fun onAdFailedToLoad(loadAdError: LoadAdError) {
          debugLog(tag = TAG) { "onAdFailedToLoad" }

          isLoadingAd = false

          sendLoadingTimeOutJob?.cancel()
          sendLoadingTimeOutJob = null
          adLoadingStateEventFlow.send(InterstitialAdLoadingStateEvent.FailedToLoad)
        }
      }
    )

    logEventRecord("ad_interstitial_load")
  }

  private fun isAdAvailable(): Boolean {
    return interstitialAd != null && checkAdIsValidAtCachePeriod()
  }

  private fun checkAdIsValidAtCachePeriod(adCachePeriodSeconds: Long = AD_CACHE_PERIOD_SECONDS): Boolean {
    val secondsDifference: Long =
      nowInstant().epochSeconds - latestLoadAdSuccessInstant.epochSeconds
    return secondsDifference < adCachePeriodSeconds
  }

  private fun showAd(activity: Activity) {
    debugLog(tag = TAG) { "showAd" }

    logEventRecord("ad_interstitial_show")

    interstitialAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
      override fun onAdDismissedFullScreenContent() {
        interstitialAd = null
        loadAd(activity)
        adShowStateEventFlow.send(InterstitialAdShowStateEvent.Finish)
        appCoroutineScope.launch {
          delay(2000)
          adShowStateFlow.emit(false)
        }
      }

      override fun onAdFailedToShowFullScreenContent(adError: AdError) {
        debugLog(tag = TAG) { "onAdFailedToShowFullScreenContent" }
        interstitialAd = null
        loadAd(activity)
        adShowStateEventFlow.send(InterstitialAdShowStateEvent.FailedToShow)
        adShowStateFlow.update { false }
      }

      override fun onAdShowedFullScreenContent() {
        fullscreenAdManager.latestShowAdSuccessInstant = nowInstant()
        adShowStateEventFlow.send(InterstitialAdShowStateEvent.Showing)
        adShowStateFlow.update { true }
        debugLog(tag = TAG) { "onAdShowedFullScreenContent" }
      }

      override fun onAdClicked() {
        splashController.skipSplash(true)
        logEventRecord("ad_interstitial_click")
      }

      override fun onAdImpression() {
        logEventRecord("ad_interstitial_impress")
      }
    }

    interstitialAd?.show(activity)
  }

  suspend fun tryToShowAd(
    activity: Activity,
    adPlaceName: String? = null,
    immediate: Boolean = false,
    onReadyShowAd: (() -> Unit)? = null
  ) = withContext(Dispatchers.Main.immediate) {
    debugLog(tag = TAG) { "tryToShowAd" }

    adPlaceName?.let {
      latestActiveAdPlaceNameFlow.update { adPlaceName }
    }

    if (fullscreenAdManager.isAdShowTimeInShowInterval()) {
      debugLog(tag = TAG) { "isAdShowTimeInShowInterval" }
      adShowStateEventFlow.send(InterstitialAdShowStateEvent.SkipToShow)
    } else { // over the show interval, need to show ad
      appCoroutineScope.launch(Dispatchers.Default) {
        if (adPlaceName != null) {
          val currentAdPlaceName = latestActiveAdPlaceNameFlow.first() ?: ""
          biReporter.reportAdEvent(adType = "interstitial", adPlace = currentAdPlaceName)
        }
      }

      onReadyShowAd?.invoke()
      debugLog(tag = TAG) { "over the show interval, need to show ad" }
      if (isAdAvailable()) { // cache available
        debugLog(tag = TAG) { "cache available" }
        if (!immediate) {
          delay(1_000)
        }
        showAd(activity)
      } else { // cache not available
        debugLog(tag = TAG) { "cache not available" }
        loadAd(activity)
      }
    }
  }
}