package com.example.pdf.biz.ad.appopen

import android.app.Activity
import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import com.example.pdf.flow.EventFlow
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.merge
import org.koin.core.annotation.Single

@Single
class AppOpenAdFacade(
  private val admobAppOpenAdManager: AdmobAppOpenAdManager,
  private val tradPlusSplashAdManager: TradPlusSplashAdManager,
  private val ghostAppOpenAdManager: GhostAppOpenAdManager,
  private val remoteConfig: RealRemoteConfig,
) {

  fun tryToLoadAd(activity: Activity) {
    when {
      remoteConfig.useGhostAdProvider -> ghostAppOpenAdManager.tryToLoadAd(activity)
      remoteConfig.useLegacyAdConfig -> tradPlusSplashAdManager.tryToLoadAd(activity)
      else -> admobAppOpenAdManager.tryToLoadAd(activity)
    }
  }

  suspend fun tryToShowAd(
    activity: Activity,
    immediate: Boolean = false
  ) {
    when {
      remoteConfig.useGhostAdProvider -> ghostAppOpenAdManager.tryToShowAd(activity, immediate)
      remoteConfig.useLegacyAdConfig -> tradPlusSplashAdManager.tryToShowAd(activity, immediate)
      else -> admobAppOpenAdManager.tryToShowAd(activity, immediate)
    }
  }

  val adLoadingStateEventFlow: EventFlow<AppOpenAdLoadingStateEvent>
    get() = when {
      remoteConfig.useGhostAdProvider -> ghostAppOpenAdManager.adLoadingStateEventFlow
      remoteConfig.useLegacyAdConfig -> tradPlusSplashAdManager.adLoadingStateEventFlow
      else -> admobAppOpenAdManager.adLoadingStateEventFlow
    }

  val adShowStateEventFlow: EventFlow<AppOpenAdShowStateEvent>
    get() = when {
      remoteConfig.useGhostAdProvider -> ghostAppOpenAdManager.adShowStateEventFlow
      remoteConfig.useLegacyAdConfig -> tradPlusSplashAdManager.adShowStateEventFlow
      else -> admobAppOpenAdManager.adShowStateEventFlow
    }

  val mergedAdLoadingStateEventFlow: Flow<AppOpenAdLoadingStateEvent>
    get() = merge(
      admobAppOpenAdManager.adLoadingStateEventFlow,
      tradPlusSplashAdManager.adLoadingStateEventFlow,
      ghostAppOpenAdManager.adLoadingStateEventFlow
    )

  val mergedAdShowStateEventFlow: Flow<AppOpenAdShowStateEvent>
    get() = merge(
      admobAppOpenAdManager.adShowStateEventFlow,
      tradPlusSplashAdManager.adShowStateEventFlow
    )
}