package com.example.pdf.biz.notification

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.platform.LocalWindowInfo
import androidx.core.content.ContextCompat
import com.example.pdf.AppActivity
import com.example.pdf.BaseActivity
import com.example.pdf.activityStack
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.biz.rating.RatingHelper
import com.example.pdf.guia.GlobalNavigator
import com.example.pdf.guia.ScreenNode
import com.example.pdf.kdatetime.nowInstant
import com.example.pdf.kermit.debugLog
import com.example.pdf.top
import com.example.pdf.ui.node.home.HomeNode
import com.example.pdf.ui.node.home.HomeStateFlow
import com.example.pdf.ui.node.home.tools.notificationPermissionGranted
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.currentKey
import com.roudikk.guia.extensions.push
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.delay
import kotlinx.datetime.Instant
import org.koin.core.annotation.Single
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.minutes

@Single
class NotificationPermissionRequester(
  private val mmkv: MMKV,
  private val fixedNotificationHelper: FixedNotificationHelper
) {

  companion object {
    private const val TAG = "NotificationPermissionRequester"
  }

  private var requestTimes
    get() = mmkv.decodeInt("${TAG}_requestTimes", 0)
    set(value) {
      mmkv.encode("${TAG}_requestTimes", value)
    }

  private var firstRequestInstant: Instant
    get() = mmkv.decodeLong("${TAG}_firstRequestInstant", 0L)
      .let(Instant.Companion::fromEpochSeconds)
    set(value) {
      mmkv.encode("${TAG}_firstRequestInstant", value.epochSeconds)
    }

  private var latestRequestInstant: Instant
    get() = mmkv.decodeLong("${TAG}_latestRequestInstant", 0L)
      .let(Instant.Companion::fromEpochSeconds)
    set(value) {
      mmkv.encode("${TAG}_latestRequestInstant", value.epochSeconds)
    }

  private var requestPermissionLauncher: ActivityResultLauncher<String>? = null

  @Composable
  fun RegisterRequesterIfNeeded(
    activity: Activity,
    navigator: Navigator,
  ) {
    debugLog(tag = TAG) { "registerRequesterIfNeeded" }

    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) return

    val currentNode = navigator.currentKey

    val isInForeground = activityStack.top() is AppActivity

    val windowInfo = LocalWindowInfo.current

    LaunchedEffect(
      currentNode,
      HomeStateFlow?.collectAsState()?.value?.currentTab,
      isInForeground
    ) {
      when {
        currentNode is HomeNode -> {
          if (RatingHelper.willBeRatting()) return@LaunchedEffect

          delay(150)

          if (
            activityStack.top() is AppActivity
            && windowInfo.isWindowFocused
          ) {
            tryToRequestIfNeeded(activity)
          }
        }

        currentNode is ScreenNode -> {
          delay(100)
          if (isInForeground) {
            tryToRequestIfNeeded(activity)
          }
        }
      }
    }
  }

  fun tryToRequestIfNeeded(
    activity: Activity,
    onShowCustomRequester: ((useSystemPermissionDialog: Boolean) -> Unit)? = null
  ) {
    debugLog(tag = TAG) { "tryToRequestIfNeeded requestTimes:$requestTimes" }

    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) return

    if (!shouldShowRequest()) return

    when (requestTimes) {
      0 -> {
        showSystemRequester(activity)
      }

      1 -> {
        if (onShowCustomRequester == null) {
          showCustomRequester(activity, true)
        } else {
          showCustomRequester(activity, true, onShowCustomRequester)
        }
      }

      in 2..9 -> {
        if (onShowCustomRequester == null) {
          showCustomRequester(activity, false)
        } else {
          showCustomRequester(activity, false, onShowCustomRequester)
        }
      }

      else -> {}
    }
  }

  @RequiresApi(Build.VERSION_CODES.TIRAMISU)
  fun showSystemRequester(
    activity: Activity,
    enabledRequestCounterIncrement: Boolean = true
  ) {
    debugLog(tag = TAG) { "showSystemRequester()" }
    if (ContextCompat.checkSelfPermission(
        activity,
        Manifest.permission.POST_NOTIFICATIONS
      ) != PackageManager.PERMISSION_GRANTED
    ) {
      requestPermissionLauncher?.run {
        try {
          if (enabledRequestCounterIncrement) {
            requestCounterIncrement()
          }
          launch(Manifest.permission.POST_NOTIFICATIONS)
        } catch (e: Exception) {
          Firebase.crashlytics.recordException(e)
          e.printStackTrace()
        }
      }
    }
  }

  fun showCustomRequester(
    activity: Activity,
    useSystemPermissionDialog: Boolean,
    onShowCustomRequester: (useSystemPermissionDialog: Boolean) -> Unit = {
      GlobalNavigator.tryTransaction {
        push(NotificationPermissionRequestDialogNode(useSystemPermissionDialog = it))
      }
    }
  ) {
    if (ContextCompat.checkSelfPermission(
        activity,
        Manifest.permission.POST_NOTIFICATIONS
      ) != PackageManager.PERMISSION_GRANTED
    ) {
      onShowCustomRequester(useSystemPermissionDialog)
    }
  }

  fun showDefaultFixedNotification() {
    fixedNotificationHelper.startNoti(null)
  }

  fun tryToShowDefaultFixedNotification(context: Context) {
    if (ContextCompat.checkSelfPermission(
        context,
        Manifest.permission.POST_NOTIFICATIONS
      ) == PackageManager.PERMISSION_GRANTED
    ) {
      showDefaultFixedNotification()
    }
  }

  fun registerPermissionResult(
    activity: BaseActivity
  ) {
    requestPermissionLauncher =
      activity.registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
        if (isGranted) {
          fixedNotificationHelper.updateNoti(activity)
          if (requestTimes == 1) {
            logEventRecord("noti_permission_request_1s_success")
            debugLog { "noti_permission_request_1s_success" }
          } else {
            logEventRecord("noti_permission_request_success")
            debugLog { "noti_permission_request_success" }
          }
        } else {
          logEventRecord("noti_permission_request_fail")
          debugLog { "noti_permission_request_fail" }
        }
      }
  }

  fun handleActivityResult(
    activity: Activity,
  ) {
    if (activity.notificationPermissionGranted()) {
      logEventRecord("noti_permission_request_success")
      debugLog { "noti_permission_request_success" }
      fixedNotificationHelper.updateNoti(activity)
    } else {
      logEventRecord("noti_permission_request_fail")
      debugLog { "noti_permission_request_fail" }
    }
  }

  fun requestCounterIncrement() {
    val now = nowInstant()

    if (firstRequestInstant.epochSeconds == 0L) {
      firstRequestInstant = now
    }
    latestRequestInstant = now
    requestTimes++

    if (requestTimes == 1) {
      logEventRecord("noti_permission_request_1st")
      debugLog { "noti_permission_request_1st" }
    } else {
      logEventRecord("noti_permission_request")
      debugLog { "noti_permission_request" }
    }
  }

  private fun shouldShowRequest(): Boolean {
    if (requestTimes == 0) return true

    val now = nowInstant()

    val timeSinceLastRequest = now - latestRequestInstant
    val timeSinceFirstRequest = now - firstRequestInstant

    when {
      requestTimes == 1 && timeSinceLastRequest > 2.minutes -> {
        // 第二次弹窗，间隔需要大于2分钟
        return true
      }

      timeSinceFirstRequest <= 24.hours && timeSinceLastRequest > 1.hours -> {
        // 24小时内，间隔需要大于1小时
        return true
      }

      timeSinceFirstRequest > 24.hours && timeSinceLastRequest > 12.hours -> {
        // 24小时后，间隔需要大于12小时
        return true
      }
    }

    return false
  }
}