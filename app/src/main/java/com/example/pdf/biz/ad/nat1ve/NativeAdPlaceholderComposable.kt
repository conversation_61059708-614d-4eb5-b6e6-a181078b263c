package com.example.pdf.biz.ad.nat1ve

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.airbnb.lottie.AsyncUpdates
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition

@Composable
fun NativeAdPlaceholder(
  modifier: Modifier = Modifier
) {
  Box(
    modifier = Modifier
      .zIndex(1f)
      .padding(top = 6.dp, start = 6.dp)
      .background(Color(0xFF999999), RoundedCornerShape(2.dp))
  ) {
    Text(
      " AD ",
      color = Color.White,
      fontSize = 9.sp,
      fontWeight = FontWeight.SemiBold,
      lineHeight = 9.sp,
      modifier = Modifier.padding(1.dp)
    )
  }

  val composition by rememberLottieComposition(
    spec = LottieCompositionSpec.Asset("ad_native.json")
  )

  LottieAnimation(
    composition = composition,
    modifier = modifier,
    iterations = Int.MAX_VALUE,
    contentScale = ContentScale.FillBounds,
    asyncUpdates = AsyncUpdates.ENABLED
  )

}

@Composable
fun NativeAdWithoutMediaPlaceholder(
  modifier: Modifier = Modifier
) {
  Box {
    Box(
      modifier = Modifier
        .align(Alignment.TopEnd)
        .zIndex(1f)
        .padding(top = 4.dp, end = 4.dp)
        .background(Color(0xFF999999), RoundedCornerShape(2.dp))
    ) {
      Text(
        " AD ",
        color = Color.White,
        fontSize = 9.sp,
        fontWeight = FontWeight.SemiBold,
        lineHeight = 9.sp,
        modifier = Modifier.padding(1.dp)
      )
    }

    val composition by rememberLottieComposition(
      spec = LottieCompositionSpec.Asset("ad_native_xs.json")
    )

    LottieAnimation(
      composition = composition,
      modifier = modifier,
      iterations = Int.MAX_VALUE,
      contentScale = ContentScale.FillBounds,
      asyncUpdates = AsyncUpdates.ENABLED
    )
  }
}