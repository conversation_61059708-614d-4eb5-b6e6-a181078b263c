package com.example.pdf.biz.ump

import android.app.Activity
import android.content.Context
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.kermit.debugLog
import com.google.android.ump.ConsentInformation
import com.google.android.ump.ConsentRequestParameters
import com.google.android.ump.UserMessagingPlatform
import org.koin.core.annotation.Single

@Single
class UserMessagingPlatformManager(
  private val context: Context
) {
  companion object {
    private const val TAG = "UserMessagingPlatformManager"
  }

  private val consentInformation: ConsentInformation by lazy {
    UserMessagingPlatform.getConsentInformation(context)
  }

  fun requestConsentInfoUpdate(activity: Activity, onUpdate: () -> Unit) {
//    val debugSettings = ConsentDebugSettings.Builder(context)
//      .setDebugGeography(ConsentDebugSettings.DebugGeography.DEBUG_GEOGRAPHY_EEA)
//      .addTestDeviceHashedId("TEST-DEVICE-HASHED-ID")
//      .build()

    val params = ConsentRequestParameters
      .Builder()
//      .setConsentDebugSettings(debugSettings)
      .build()

    consentInformation.requestConsentInfoUpdate(
      activity,
      params,
      {
        UserMessagingPlatform.loadAndShowConsentFormIfRequired(activity) { error ->
          if (error == null) {
            if (consentInformation.canRequestAds()) {
              debugLog(tag = TAG) { "loadAndShowConsentFormIfRequired consentInformation.canRequestAds() is true" }
              logEventRecord("ump_can_request_ads_ture")
            } else {
              debugLog(tag = TAG) { "loadAndShowConsentFormIfRequired consentInformation.canRequestAds() is false" }
              logEventRecord("ump_can_request_ads_false")
            }
          } else {
            debugLog(tag = TAG) { "loadAndShowConsentFormIfRequired error" }
            logEventRecord("ump_load_and_show_error")
          }

          onUpdate()
        }

      },
      { _ ->
        debugLog(tag = TAG) { "requestConsentInfoUpdate error" }
        logEventRecord("ump_request_error")
        onUpdate()
      }
    )
  }
}