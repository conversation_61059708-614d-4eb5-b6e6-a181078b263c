package com.example.pdf.biz.ad.nat1ve

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import com.example.pdf.android.lifecycle.OnLifecycleEvent
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.kermit.debugLog
import org.koin.compose.koinInject

@Composable
fun GhostNativeAd(
  place: NativeAdPlace,
  modifier: Modifier = Modifier,
  isWithoutMedia: Boolean = false,
) {
  val localContext = LocalContext.current
  val nativeAdManager: GhostNativeAdManager = koinInject()

  debugLog(tag = "GHOST_NATIVE_AD") { "GhostNativeAd for place: ${place.name}" }

  OnLifecycleEvent { _, event ->
    when (event) {
      Lifecycle.Event.ON_DESTROY -> {
        debugLog(tag = "GHOST_NATIVE_AD") { "Lifecycle.Event.ON_DESTROY call destroy() place: ${place.name}" }
        nativeAdManager.destroy(place)
      }
      else -> {}
    }
  }

  LaunchedEffect(Unit) {
    logEventRecord("ad_native_show")

    debugLog(tag = "GHOST_NATIVE_AD") { "nativeAdManager.buildAd(place) (Ghost - no-op)" }
    nativeAdManager.buildAd(place, localContext, isWithoutMedia)
  }

  // Display nothing - this is a no-op ad provider
  // We maintain the same height as other native ads to preserve layout consistency
  val adHeight = if (isWithoutMedia) 100.dp else 138.dp
  
  Box(
    modifier = modifier
      .fillMaxWidth()
      .height(adHeight)
  ) {
    // Completely empty - no ad content displayed
  }
}
