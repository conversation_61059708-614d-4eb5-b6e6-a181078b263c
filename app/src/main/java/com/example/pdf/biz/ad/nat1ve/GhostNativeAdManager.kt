package com.example.pdf.biz.ad.nat1ve

import android.content.Context
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.kermit.debugLog
import kotlinx.coroutines.flow.MutableStateFlow
import org.koin.core.annotation.Single

@Single
class GhostNativeAdManager(
  private val context: Context,
) {
  @Suppress("PrivatePropertyName")
  private val TAG = "GhostNativeAdManager"
  private val adUnitNameLowercase = "native"

  private val adStateFlowMap = mutableMapOf<NativeAdPlace, MutableStateFlow<GhostNativeAdState>>()

  fun getAdStateFlow(place: NativeAdPlace): MutableStateFlow<GhostNativeAdState> {
    return adStateFlowMap.getOrPut(place) {
      MutableStateFlow(GhostNativeAdState.Companion.Empty)
    }
  }

  fun buildAd(place: NativeAdPlace, context: Context? = null, isWithoutMedia: Boolean = false) {
    debugLog(tag = TAG) { "buildAd for place: ${place.name} (Ghost - no-op)" }
    
    // Log the load event to maintain analytics consistency
    logEventRecord("ad_${adUnitNameLowercase}_load")
    
    // Immediately mark as not ready since we don't actually load anything
    getAdStateFlow(place).value = GhostNativeAdState(isReady = false)
    
    // Log success event to maintain analytics consistency
    logEventRecord("ad_${adUnitNameLowercase}_load_success")
  }

  fun destroy(place: NativeAdPlace) {
    debugLog(tag = TAG) { "destroy for place: ${place.name} (Ghost - no-op)" }
    getAdStateFlow(place).value = GhostNativeAdState.Companion.Empty
  }

  fun destroyAll() {
    debugLog(tag = TAG) { "destroyAll (Ghost - no-op)" }
    adStateFlowMap.values.forEach {
      it.value = GhostNativeAdState.Companion.Empty
    }
  }
}
