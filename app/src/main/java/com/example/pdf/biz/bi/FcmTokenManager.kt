package com.example.pdf.biz.bi

import com.example.pdf.kermit.debugLog
import com.tencent.mmkv.MMKV
import de.jensklingenberg.ktorfit.Response
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.core.annotation.Single
import kotlin.time.Duration.Companion.minutes

@Single
class FcmTokenManager(
  private val biApi: BiApi,
) {
  companion object {
    private const val TAG = "FcmTokenManager"
    private val RETRY_DELAY = 2.minutes
    private const val MAX_RETRY_COUNT = 20
  }

  private val mmkv by lazy { MMKV.mmkvWithID(TAG) }

  @Volatile
  private var retryCount = 0
  private var retryJob: Job? = null

  var fcmToken
    get() = mmkv.decodeString("fcm_token")
    private set(value) {
      mmkv.encode("fcm_token", value)
    }


  // 处理新的FCM Token
  fun handleNewFcmToken(newToken: String) {
    if (newToken != fcmToken) {
      uploadFcmTokenWithRetry(newToken)
    }
  }

  // 带重试机制的上传函数
  private fun uploadFcmTokenWithRetry(token: String) {
    retryCount = 0
    retryJob?.cancel()
    retryJob = null
    retryJob = GlobalScope.launch {
      while (retryCount < MAX_RETRY_COUNT) {
        val response = try {
          updateFcmToken(token)
        } catch (e: Exception) {
          null // 网络异常等情况返回null
        }

        when {
          response?.isSuccessful == true -> {
            fcmToken = token
            // 上传成功，终止重试
            retryCount = MAX_RETRY_COUNT
            break
          }

          retryCount < MAX_RETRY_COUNT -> {
            // 上传失败，准备重试
            retryCount++
            delay(RETRY_DELAY)
          }

          else -> {
            // 达到最大重试次数
            break
          }
        }
      }
    }
  }

  private suspend fun updateFcmToken(fcmToken: String): Response<Unit>? {
    return biApi.updateFcmToken(UpdateFcmTokenRequest(fcmToken = fcmToken)).apply {
      debugLog(tag = TAG) { "response: $this" }
    }
  }

}