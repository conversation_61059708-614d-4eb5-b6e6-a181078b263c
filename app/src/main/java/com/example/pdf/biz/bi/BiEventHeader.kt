package com.example.pdf.biz.bi

import androidx.compose.ui.text.intl.Locale
import com.example.pdf.BuildConfig
import com.example.pdf.appContext
import com.example.pdf.ui.node.home.tools.notificationPermissionGranted
import kotlinx.serialization.EncodeDefault
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.util.TimeZone

@OptIn(ExperimentalSerializationApi::class)
@Serializable
@Suppress("PropertyName")
data class BiEventHeader(
  @EncodeDefault @SerialName("package_id") val package_id: String = "com.wydevteam.mypdf",
  @EncodeDefault @SerialName("version_name") val version_name: String = BuildConfig.VERSION_NAME,
  @EncodeDefault @SerialName("version_code") val version_code: Int = BuildConfig.VERSION_CODE,
  @EncodeDefault @SerialName("user_id") val user_id: String = "",
  @EncodeDefault @SerialName("language") val language: String = Locale.current.toLanguageTag(),
  @EncodeDefault @SerialName("app_instance_id") val app_instance_id: String = "",
  @EncodeDefault @SerialName("tenjin_id") val tenjin_id: String = "",
  @EncodeDefault @SerialName("timezone") val timezone: Int = DEVICE_TIME_ZONE,
  @EncodeDefault @SerialName("permission_status") val permission_status: Int = notificationPermissionStatus.ordinal,
)

private val DEVICE_TIME_ZONE = TimeZone.getDefault().getOffset(System.currentTimeMillis()) / 3600000

private enum class PermissionStatus {
  GRANTED,
  DENIED,
}

private val notificationPermissionStatus: PermissionStatus
  get() {
    return if (appContext.notificationPermissionGranted()) PermissionStatus.GRANTED else PermissionStatus.DENIED
  }