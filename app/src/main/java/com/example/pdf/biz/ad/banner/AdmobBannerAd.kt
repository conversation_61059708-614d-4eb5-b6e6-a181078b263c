package com.example.pdf.biz.ad.banner

import android.annotation.SuppressLint
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.animation.Crossfade
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import com.example.pdf.android.lifecycle.OnLifecycleEvent
import com.example.pdf.biz.ad.AdColors
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.kermit.debugLog
import kotlinx.coroutines.launch
import org.koin.compose.koinInject

@SuppressLint("ConfigurationScreenWidthHeight")
@Composable
fun AdmobBannerAd(
  adPlace: BannerAdPlace,
  modifier: Modifier = Modifier,
) {
  val context = LocalContext.current
  val scope = rememberCoroutineScope()
  val bannerAdManager: AdmobBannerAdManager = koinInject()
  val adWidth = LocalConfiguration.current.screenWidthDp - (12 * 2)

  val adView by bannerAdManager.getAdViewFlow(adPlace).collectAsState()

  var adContainer by remember { mutableStateOf<FrameLayout?>(null) }

  OnLifecycleEvent { _, event ->
    debugLog(tag = "BANNER_AD") { "OnLifecycleEvent() event:${event.name} adPlace:${adPlace.name}" }
    when (event) {
      Lifecycle.Event.ON_START -> scope.launch {
        bannerAdManager.resume(adPlace)
      }

      Lifecycle.Event.ON_RESUME -> scope.launch {
        bannerAdManager.resume(adPlace)
      }

      Lifecycle.Event.ON_PAUSE -> scope.launch {
        bannerAdManager.pause(adPlace)
      }

      Lifecycle.Event.ON_DESTROY -> bannerAdManager.destroy(adPlace)
      else -> {}
    }
  }

  DisposableEffect(Unit) {
    logEventRecord("ad_banner_show")

    if (adView == null) {
      debugLog(tag = "BANNER_AD") { "bannerAdManager.buildAd(adPlace, adWidth)" }
      bannerAdManager.buildAd(adPlace, adWidth)
    }

    onDispose {
      runCatching { adContainer?.removeAllViews() }
    }
  }

  Box(
    modifier = modifier.height(84.dp).background(color = AdColors.backgroundColor),
    contentAlignment = Alignment.Center
  ) {
    Crossfade(
      targetState = adView != null,
    ) { adViewNotNull ->
      if (adViewNotNull) {
        AndroidView(
          factory = { context ->
            FrameLayout(context).apply {
              layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
              )
              runCatching {
                debugLog(tag = "BANNER_AD") { "addView(adView)" }
                (adView?.parent as? ViewGroup)?.removeAllViews()
                addView(adView)
              }
            }.apply {
              adContainer = this
            }
          },
          modifier = Modifier
            .clip(RoundedCornerShape(4.dp))
            .fillMaxWidth()
            .padding(12.dp)
        )
      } else {
        BannerAdPlaceholderByLottie()
      }
    }
  }
}

@Preview
@Composable
private fun BannerAdPreview() {
  AdmobBannerAd(adPlace = BannerAdPlace.Test)
}
