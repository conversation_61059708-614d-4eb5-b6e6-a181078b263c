package com.example.pdf.biz.ad.interstitial

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import com.example.pdf.android.context.findActivity
import com.example.pdf.biz.ad.AdLoadingDialog
import com.example.pdf.biz.remoteconfig.useLegacyAdConfig
import com.example.pdf.guia.NavigateAction
import com.example.pdf.guia.previousKey
import com.example.pdf.ui.node.home.HomeNode
import com.example.scan.biz.ad.interstitial.TradPlusInterstitialAdViewModel
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import org.koin.androidx.compose.koinViewModel
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

typealias OnTryToShowInterAdAndNavAction = (adPlace: String, NavigateAction) -> Unit
typealias OnBackAction = () -> Unit

data class InterstitialAdHandlers(
  val onTryToShowInterAdAndNavAction: OnTryToShowInterAdAndNavAction,
  val onBackAction: OnBackAction,
  val admobViewState: AdmobInterstitialAdViewState,
  val tradplusViewState: TradPlusInterstitialAdViewState
)

@Composable
private fun interstitialAdHandlers(
  navigator: Navigator
): InterstitialAdHandlers {
  val context = LocalContext.current

  val admobInterstitialAdViewModel: AdmobInterstitialAdViewModel = koinViewModel()
  val admobInterstitialAdViewState by admobInterstitialAdViewModel.collectAsState()

  val tradplusInterstitialAdViewModel: TradPlusInterstitialAdViewModel = koinViewModel()
  val tradplusInterstitialAdViewState by tradplusInterstitialAdViewModel.collectAsState()

  LaunchedEffect(Unit) {
    admobInterstitialAdViewModel.registerInterAdEventFlow(this)
    tradplusInterstitialAdViewModel.registerInterAdEventFlow(this)
  }

  admobInterstitialAdViewModel.collectSideEffect {
    when (it) {
      is AdmobInterstitialAdSideEffect.NavTo -> {
        it.navAction(navigator)
      }

      AdmobInterstitialAdSideEffect.NavUp -> {
        navigator.pop()
      }
    }
  }

  tradplusInterstitialAdViewModel.collectSideEffect {
    when (it) {
      is TradPlusInterstitialAdSideEffect.NavTo -> {
        it.navAction(navigator)
      }

      TradPlusInterstitialAdSideEffect.NavUp -> {
        navigator.pop()
      }
    }
  }

  val onTryToShowInterAdAndNavAction: OnTryToShowInterAdAndNavAction =
    remember {
      { adPlaceName, navAction ->
        if (useLegacyAdConfig.not()) {
          admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
            activity = context.findActivity(),
            navAction = navAction,
            adPlaceName = adPlaceName
          )
        } else {
          tradplusInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
            activity = context.findActivity(),
            navAction = navAction,
            adPlaceName = adPlaceName
          )
        }
      }
    }

  val onBackAction: OnBackAction = remember {
    {
      if (navigator.previousKey is HomeNode) {
        val adPlaceName = "back_to_home"
        if (useLegacyAdConfig.not()) {
          admobInterstitialAdViewModel.onBackAndBeforeTryToShowInterAd(
            activity = context.findActivity(),
            adPlaceName = adPlaceName
          )
        } else {
          tradplusInterstitialAdViewModel.onBackAndBeforeTryToShowInterAd(
            activity = context.findActivity(),
            adPlaceName = adPlaceName
          )
        }
      } else {
        navigator.pop()
      }
      Unit
    }
  }

  return InterstitialAdHandlers(
    onTryToShowInterAdAndNavAction = onTryToShowInterAdAndNavAction,
    onBackAction = onBackAction,
    admobViewState = admobInterstitialAdViewState,
    tradplusViewState = tradplusInterstitialAdViewState
  )
}

@Composable
fun interstitialAdRegister(
  navigator: Navigator,
): Pair<OnTryToShowInterAdAndNavAction, OnBackAction> {
  val handlers = interstitialAdHandlers(navigator)

  val adLoading = handlers.admobViewState.adLoading || handlers.tradplusViewState.adLoading

  if (adLoading) {
    AdLoadingDialog()
  }

  return handlers.onTryToShowInterAdAndNavAction to handlers.onBackAction
}

@Composable
fun interstitialAdRegisterAndLoadingState(
  navigator: Navigator,
): Triple<OnTryToShowInterAdAndNavAction, OnBackAction, Boolean> {
  val handlers = interstitialAdHandlers(navigator)

  val adLoading = handlers.admobViewState.adLoading || handlers.tradplusViewState.adLoading

  return Triple(
    handlers.onTryToShowInterAdAndNavAction,
    handlers.onBackAction,
    adLoading
  )
}