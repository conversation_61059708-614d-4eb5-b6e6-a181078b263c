package com.example.pdf.biz.bi

import android.content.Context
import com.google.android.gms.ads.identifier.AdvertisingIdClient
import com.google.firebase.Firebase
import com.google.firebase.analytics.analytics
import com.google.firebase.crashlytics.crashlytics
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class BiPrefs(
  private val context: Context,
  private val mmkv: MMKV
) {
  companion object {
    const val TAG = "BiPrefs"
  }

  val userId get() = mmkv.decodeString("${TAG}_userId")

  suspend fun adId(): String? {
    val adId = mmkv.decodeString("${TAG}_adId")

    return if (adId.isNullOrEmpty()) {
      try {
        val resultAdId = withContext(Dispatchers.IO) {
          try {
            AdvertisingIdClient.getAdvertisingIdInfo(context).id
          } catch (e: Exception) {
            null
          }
        }

        if (resultAdId != null) {
          mmkv.encode("${TAG}_adId", resultAdId)
        }

        resultAdId
      } catch (e: Exception) {
        null
      }
    } else {
      adId
    }
  }

  fun storeUserId(id: String) {
    mmkv.encode("${TAG}_userId", id)
  }

  suspend fun appInstanceId(): String? {
    val key = "${TAG}_appInstanceId"

    val storedAppInstanceId = mmkv.decodeString(key)
    if (storedAppInstanceId != null) return storedAppInstanceId

    val fetchFirebaseInstanceId = withContext(Dispatchers.IO) {
      try {
        Firebase.analytics.firebaseInstanceId
      } catch (e: Exception) {
        Firebase.crashlytics.recordException(e)
        null
      }
    }

    if (fetchFirebaseInstanceId != null) {
      mmkv.encode(key, fetchFirebaseInstanceId)
    }

    return fetchFirebaseInstanceId
  }
}