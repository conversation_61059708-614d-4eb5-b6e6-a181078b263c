package com.example.pdf.biz.ad.nat1ve

import android.content.Context
import android.view.ViewGroup
import com.example.pdf.biz.SplashController
import com.example.pdf.biz.analytic.AnalyticsLogEvent
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import com.example.pdf.kermit.debugLog
import com.tradplus.ads.base.bean.TPAdError
import com.tradplus.ads.base.bean.TPAdInfo
import com.tradplus.ads.base.bean.TPBaseAd
import com.tradplus.ads.open.nativead.NativeAdListener
import com.tradplus.ads.open.nativead.TPNative
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.koin.core.annotation.Single

@Single
class TradPlusNativeAdManager(
  private val context: Context,
  private val splashController: SplashController,
  private val remoteConfig: RealRemoteConfig,
) {
  @Suppress("PrivatePropertyName")
  private val TAG = "TradPlusNativeAdManager"
  private val adUnitNameLowercase = "native"

  private val adKey get() = remoteConfig.adConfig3.native_ad_key

  private val tpBaseAdFlowMap = mutableMapOf<NativeAdPlace, MutableStateFlow<TPBaseAd?>>()
  private val adStateFlowMap =
    mutableMapOf<NativeAdPlace, MutableStateFlow<TradPlusNativeAdState>>()
  private val tpNativeMap = mutableMapOf<NativeAdPlace, TPNative>()
  private val tpNativeAdViewMap = mutableMapOf<NativeAdPlace, ViewGroup>()

  fun getTpBaseAdFlow(place: NativeAdPlace): MutableStateFlow<TPBaseAd?> {
    return tpBaseAdFlowMap.getOrPut(place) {
      MutableStateFlow(null)
    }
  }

  fun getTpAdStateFlow(place: NativeAdPlace): MutableStateFlow<TradPlusNativeAdState> {
    return adStateFlowMap.getOrPut(place) {
      MutableStateFlow(TradPlusNativeAdState())
    }
  }

  fun getTpNative(place: NativeAdPlace): TPNative? {
    return tpNativeMap[place]
  }

  fun cacheAddView(place: NativeAdPlace, adView: ViewGroup) {
    tpNativeAdViewMap[place] = adView
  }

  fun getTpNativeAdView(place: NativeAdPlace): ViewGroup? {
    return tpNativeAdViewMap[place]
  }

  fun buildAd(
    place: NativeAdPlace,
    isWithoutMedia: Boolean
  ) {
    debugLog(tag = TAG) { "buildAd for place: ${place.name}" }

    // Create TPNative instance for this place
    val tpNative = TPNative(context, adKey)
    tpNativeMap[place] = tpNative

    tpNative.setAdListener(object : NativeAdListener() {
      override fun onAdLoaded(tpAdInfo: TPAdInfo?, tpBaseAd: TPBaseAd?) {
        debugLog(tag = TAG) { "onAdLoaded: ${tpAdInfo?.adSourceName} for place: ${place.name}" }

        GlobalScope.launch(Dispatchers.Main.immediate) {
          val adFlow = getTpBaseAdFlow(place)
          val cacheNativeAd = adFlow.first()

          // Destroy previous ad if exists
          cacheNativeAd?.let {
            debugLog(tag = TAG) { "Destroying previous ad for place: ${place.name}" }
          }

          // Update flow with new ad
          adFlow.update { tpBaseAd }

          // Update state
          adStateFlowMap.getOrPut(place) { MutableStateFlow(TradPlusNativeAdState()) }
            .update { TradPlusNativeAdState(isReady = true) }
        }
      }

      override fun onAdClicked(tpAdInfo: TPAdInfo?) {
        splashController.skipSplash(true)
        debugLog(tag = TAG) { "onAdClicked: ${tpAdInfo?.adSourceName} for place: ${place.name}" }

        // Record analytics - just log the click event
        logEventRecord("ad_${adUnitNameLowercase}_click")
      }

      override fun onAdImpression(tpAdInfo: TPAdInfo?) {
        debugLog(tag = TAG) { "onAdImpression: ${tpAdInfo?.adSourceName} for place: ${place.name}" }
        tpAdInfo?.let { info ->
          val adFormat = "native"

          AnalyticsLogEvent.tryToRecordTotalAdsRevenue001(info)
          AnalyticsLogEvent.recordAdImpressionRevenue(info, adFormat)
          AnalyticsLogEvent.recordAdImpression(info, adFormat)
          AnalyticsLogEvent.tenjinEventAdImpressionTradPlus(info)
        }

        logEventRecord("ad_${adUnitNameLowercase}_impress")
      }

      override fun onAdShowFailed(tpAdError: TPAdError?, tpAdInfo: TPAdInfo?) {
        debugLog(tag = TAG) { "onAdShowFailed: ${tpAdInfo?.adSourceName} for place: ${place.name}, error: ${tpAdError?.errorMsg}" }

        // Update state
        adStateFlowMap.getOrPut(place) { MutableStateFlow(TradPlusNativeAdState()) }
          .update { TradPlusNativeAdState(isReady = false) }
      }

      override fun onAdLoadFailed(tpAdError: TPAdError?) {
        debugLog(tag = TAG) { "onAdLoadFailed for place: ${place.name}, code: ${tpAdError?.errorCode}, msg: ${tpAdError?.errorMsg}" }

        // Update state
        adStateFlowMap.getOrPut(place) { MutableStateFlow(TradPlusNativeAdState()) }
          .update { TradPlusNativeAdState(isReady = false) }
      }

      override fun onAdClosed(tpAdInfo: TPAdInfo?) {
        debugLog(tag = TAG) { "onAdClosed: ${tpAdInfo?.adSourceName} for place: ${place.name}" }
      }
    })

    // Set custom parameters if needed
    val customParams = HashMap<String, Any>().apply {
      put("Admob_Adchoices", if (isWithoutMedia) 2 else 1)
    }
    // Add any TradPlus specific parameters here
    tpNative.setCustomParams(customParams)

    // Enter ad scenario for analytics
    tpNative.entryAdScenario("native_${place.named}")

    // Load the ad
    tpNative.loadAd()

    logEventRecord("ad_${adUnitNameLowercase}_load")
  }

  fun destroy(place: NativeAdPlace) {
    debugLog(tag = TAG) { "destroy for place: ${place.name}" }

    // Clear ad flow
    tpBaseAdFlowMap[place]?.update { null }

    // Update state
    adStateFlowMap.getOrPut(place) { MutableStateFlow(TradPlusNativeAdState()) }
      .update { TradPlusNativeAdState(isReady = false) }

    // Destroy TPNative instance
    tpNativeMap[place]?.onDestroy()
    tpNativeMap.remove(place)

    tpNativeAdViewMap[place].let { adView ->
      runCatching {
        adView?.parent?.let { parent ->
          (parent as? ViewGroup)?.removeView(adView)
        }
      }
      tpNativeAdViewMap.remove(place)
    }
  }

  fun destroyAll() {
    debugLog(tag = TAG) { "destroyAll" }

    GlobalScope.launch(Dispatchers.Main.immediate) {
      tpBaseAdFlowMap.keys.forEach(::destroy)
    }
  }
}
