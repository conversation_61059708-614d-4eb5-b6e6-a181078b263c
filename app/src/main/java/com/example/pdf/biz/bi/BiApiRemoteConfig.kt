package com.example.pdf.biz.bi

import android.content.Context
import android.os.Parcelable
import com.example.pdf.biz.remoteconfig.AdConfig1
import com.example.pdf.biz.remoteconfig.AdConfig2
import com.example.pdf.biz.remoteconfig.AdConfig3
import com.example.pdf.coroutine.AppCoroutineScope
import com.example.pdf.crypt.decryptCBC
import com.example.pdf.crypt.requestBodyEncrypt
import com.example.pdf.kermit.debugLog
import com.example.pdf.serialization.toJson
import com.example.pdf.serialization.toObj
import com.example.pdf.biz.remoteconfig.RemoteConfigKey
import com.example.pdf.configureTradPlusSdk
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import kotlin.time.DurationUnit
import kotlin.time.toDuration

@Parcelize
class WConfigMap(
  val configMap: Map<String, String>
) : Parcelable

class BiApiRemoteConfig(
  private val context: Context,
  private val biApi: BiApi,
  private val appCoroutineScope: AppCoroutineScope,
  private val mmkv: MMKV,
//  private val creditStore: CreditStore,
) {
  companion object {
    const val TAG = "BiApiRemoteConfig"

    private const val W_CONFIG_MAP_KEY = "WConfigMap_Key"
  }

  val configMap: Map<String, String>
    get() = mmkv.decodeParcelable(W_CONFIG_MAP_KEY, WConfigMap::class.java, null)
      ?.configMap
      ?: emptyMap()

  private fun updateConfigMap(configMap: Map<String, String>) {
    mmkv.encode(W_CONFIG_MAP_KEY, WConfigMap(configMap))
    debugLog(tag = TAG) { "updateConfigMap: $configMap" }
  }

  fun init(
    onUnstableUpdateConfig: (() -> Unit)? = null,
  ) {
    debugLog(tag = TAG) { "init()" }
    appCoroutineScope.launch(Dispatchers.Default) {
      if (configMap.isEmpty()) {
        repeat(50) { currentTimes ->
          fetchConfig()
          onUnstableUpdateConfig?.invoke()

          when (currentTimes) {
            in 0..12 -> {
              delay(10.toDuration(DurationUnit.SECONDS))
            }

            in 13..17 -> {
              val input = currentTimes

              // 计算系数：(n-12)^2 - n + 13 - [if n=16 then 1 else 0]
              val adjustment = if (input == 16) 1 else 0
              val coefficient = (input - 12) * (input - 12) - input + 13 - adjustment
              val result = coefficient * 5

              delay(result.toDuration(DurationUnit.MINUTES))
            }

            else -> {
              delay(60.toDuration(DurationUnit.MINUTES))
            }
          }
        }
      } else {
        while (true) {
          fetchConfig()
          delay(60.toDuration(DurationUnit.MINUTES))
        }
      }
    }
  }

  private suspend fun fetchConfig() {
    debugLog(tag = TAG) { "fetchConfig()" }

    var header = BiReporter.Companion.header()

    if (header == null) {
      run {
        repeat(7) {
          delay(100L * (it + 1))

          debugLog(tag = TAG) { "repeat getHeader $it" }
          header = BiReporter.Companion.header()
          if (header != null) {
            debugLog(tag = TAG) { "repeat getHeader non null" }
            return@run
          }
        }
      }
    }

    if (header != null) {
      val configMap = try {
        biApi.config(
          headerMap = header,
          body = BiApiConfigRequestBody().toJson().requestBodyEncrypt()
        )
      } catch (e: Exception) {
        e.printStackTrace()
        null
      }
      debugLog(tag = TAG) { "get configMap: ${configMap.isNullOrEmpty().not()}" }

      if (configMap.isNullOrEmpty().not()) {

//        val decryptedConfigMap = encryptedConfigMap.mapValues {
//          it.value.trim().decryptCBC()
//        }

        updateConfigMap(configMap)

        appCoroutineScope.launch(Dispatchers.Default) {
          configureTradPlusSdk(context)
        }
      }
    }
  }


  fun useLegacyAdConfig(): Boolean? {
    val key = RemoteConfigKey.USE_LEGACY_AD_CONFIG

    return configMap[key]?.toBooleanStrictOrNull()
  }

  fun adConfig1(): AdConfig1? {
    val key = RemoteConfigKey.AD_CONFIG_1

    return configMap[key]?.toObj()
  }

  fun adConfig2(): AdConfig2? {
    val key = RemoteConfigKey.AD_CONFIG_2

    return configMap[key]?.toObj()
  }

  fun adConfig3(): AdConfig3? {
    val key = RemoteConfigKey.AD_CONFIG_3

    return configMap[key]?.toObj()
  }

}