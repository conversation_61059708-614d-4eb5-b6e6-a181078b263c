package com.example.pdf.biz

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import com.example.pdf.appContext
import com.example.pdf.biz.bi.BiReporter
import com.example.pdf.biz.remoteconfig.FirebaseRemoteConfig
import com.example.pdf.coroutine.AppCoroutineScope
import com.example.pdf.kdatetime.nowInstant
import com.example.pdf.kermit.debugLog
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

object TimeTickReceiver : BroadcastReceiver(), KoinComponent {

  private val firebaseRemoteConfig: FirebaseRemoteConfig by inject()
  private val appCoroutineScope: AppCoroutineScope by inject()
  private val biReporter: BiReporter by inject()

  override fun onReceive(context: Context?, intent: Intent?) {
    if (intent?.action == Intent.ACTION_TIME_TICK) {
      debugLog(tag = "mono") { "Intent.ACTION_TIME_TICK" }
      handleTimeTick(context ?: appContext)
    }
  }

  fun register(context: Context) {
    context.registerReceiver(this, IntentFilter(Intent.ACTION_TIME_TICK))
  }


  fun handleTimeTick(context: Context) {
    val now = nowInstant()
    firebaseRemoteConfig.tryToUpdateConfig(now)

    with(appCoroutineScope) {
      launch {
        delay((0L..7_000L).random())
        biReporter.handlePendingEvents()
      }
    }
  }
}