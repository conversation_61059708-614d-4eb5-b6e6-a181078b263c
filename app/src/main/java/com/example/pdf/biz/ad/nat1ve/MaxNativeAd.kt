package com.example.pdf.biz.ad.nat1ve

import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.animation.Crossfade
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.zIndex
import androidx.core.view.children
import androidx.lifecycle.Lifecycle
import cafe.adriel.lyricist.LocalStrings
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import com.example.pdf.android.lifecycle.OnLifecycleEvent
import com.example.pdf.biz.ad.AdColors
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.kermit.debugLog
import com.example.pdf.lumo.AppTheme
import org.koin.compose.koinInject

@Composable
fun MaxNativeAd(
  place: NativeAdPlace,
  modifier: Modifier = Modifier,
) {
  val localContext = LocalContext.current

  val nativeAdManager: MaxNativeAdManager = koinInject()

  val nativeAd by nativeAdManager.adFlow(place).collectAsState()

  debugLog(tag = "NATIVE_AD") { "nativeAd: $nativeAd" }

  var adContainer by remember { mutableStateOf<FrameLayout?>(null) }

  OnLifecycleEvent { _, event ->
    when (event) {
      Lifecycle.Event.ON_DESTROY -> {
        debugLog(tag = "NATIVE_AD") { "Lifecycle.Event.ON_DESTROY call destroy() place: ${place.name}" }

        nativeAdManager.destroy(place)
      }

      else -> {}
    }
  }

  LaunchedEffect(nativeAd?.adView) {
    nativeAd?.adView?.let {
      runCatching {
        (it.parent as? ViewGroup)?.removeAllViews()
        adContainer?.removeAllViews()

        if (nativeAdManager.needToBlockSomeClickAreas(nativeAd?.ad ?: return@let)) {
          it.titleTextView.isClickable = false
          it.bodyTextView.isClickable = false
          it.advertiserTextView.isClickable = false
          it.iconImageView.isClickable = false
          it.mediaContentViewGroup.isEnabled = false
          it.postDelayed({
            it.mediaContentViewGroup.children.forEach {
              it.isEnabled = false
            }
          }, 500)
        }

        adContainer?.addView(it)
      }
    }
  }

  LaunchedEffect(Unit) {
    logEventRecord("ad_native_show")

    if (nativeAd == null) {
      debugLog(tag = "NATIVE_AD") { "nativeAdManager.buildAd(place)" }
      nativeAdManager.buildAd(place, localContext)
    }
  }

  Box(modifier = Modifier.background(brush = AdColors.backgroundBrush)) {
    Crossfade(
      targetState = nativeAd != null,
      modifier = modifier
        .padding(8.dp)
        .apply {
          if (nativeAd == null) animateContentSize()
        },
      label = "",
    ) { hasNativeAd ->
      if (hasNativeAd) {
        Surface(shape = RoundedCornerShape(4.dp)) {
          AndroidView(
            factory = { context ->
              FrameLayout(context).apply {
                layoutParams = ViewGroup.LayoutParams(
                  ViewGroup.LayoutParams.WRAP_CONTENT,
                  ViewGroup.LayoutParams.WRAP_CONTENT
                )
              }.apply {
                adContainer = this
              }
            },
            modifier = Modifier.fillMaxWidth()
          )
        }
      } else {
        MaxNativeAdPlaceholder(
          modifier = Modifier
            .fillMaxWidth()
            .height(138.dp)
        )
      }
    }
  }
}

@Composable
private fun MaxNativeAdPlaceholder(
  modifier: Modifier = Modifier
) {
  Box(
    modifier = Modifier
      .zIndex(1f)
      .padding(top = 6.dp, start = 6.dp)
      .background(Color(0xFF999999), RoundedCornerShape(2.dp))
  ) {
    Text(
      LocalStrings.current.adLabel,
      color = Color.White,
      fontSize = 9.sp,
      fontWeight = FontWeight.SemiBold,
      lineHeight = 9.sp,
      modifier = Modifier.padding(1.dp)
    )
  }

  val composition by rememberLottieComposition(
    spec = LottieCompositionSpec.Asset("ad_native.json")
  )

  LottieAnimation(
    composition = composition,
    modifier = modifier,
    iterations = Int.MAX_VALUE,
    contentScale = ContentScale.FillBounds
  )

}


@Preview
@Composable
private fun MaxNativeAdPreview() {
  AppTheme {
    MaxNativeAd(place = NativeAdPlace.Test)
  }
}

@Preview
@Composable
private fun MaxNativeAdPlaceholderPreview() {
  AppTheme {
    MaxNativeAdPlaceholder()
  }
}
