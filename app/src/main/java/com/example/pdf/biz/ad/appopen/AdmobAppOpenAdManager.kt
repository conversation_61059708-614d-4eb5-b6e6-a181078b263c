package com.example.pdf.biz.ad.appopen

import android.app.Activity
import android.content.Context
import com.example.pdf.biz.BizConfig
import com.example.pdf.biz.SplashController
import com.example.pdf.biz.ad.FullscreenAdManager
import com.example.pdf.biz.analytic.AnalyticsLogEvent
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.biz.bi.BiReporter
import com.example.pdf.biz.bi.reportAdEvent
import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import com.example.pdf.coroutine.AppCoroutineScope
import com.example.pdf.flow.EventFlow
import com.example.pdf.flow.send
import com.example.pdf.kdatetime.nowInstant
import com.example.pdf.kermit.debugLog
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.OnPaidEventListener
import com.google.android.gms.ads.appopen.AppOpenAd
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.datetime.Instant
import org.koin.core.annotation.Single
import kotlin.time.Duration.Companion.seconds

private const val AD_CACHE_PERIOD_SECONDS = 55 * 60L // 55 minutes

private const val TAG = "AdmobAppOpenAdManager"

@Single
class AdmobAppOpenAdManager(
  private val splashController: SplashController,
  private val remoteConfig: RealRemoteConfig,
  private val appCoroutineScope: AppCoroutineScope,
  private val fullscreenAdManager: FullscreenAdManager,
  private val biReporter: BiReporter,
) {
  private var appOpenAd: AppOpenAd? = null
  private var isLoadingAd = false
  private var latestLoadAdSuccessInstant: Instant = Instant.fromEpochSeconds(0)

  private val config get() = remoteConfig.adConfig2

  private val adKey
    get() = config.app_open_ad_key.ifEmpty { BizConfig.ADMOB_AD_UNIT_ID_FOR_APP_OPEN }

  val adLoadingStateEventFlow = EventFlow<AppOpenAdLoadingStateEvent>()
  val adShowStateEventFlow = EventFlow<AppOpenAdShowStateEvent>()

  private var sendLoadingTimeOutJob: Job? = null

  fun tryToLoadAd(activity: Activity) {
    debugLog(tag = TAG) { "tryToLoadAd" }

    if (isLoadingAd) return

    if (isAdAvailable()) {
      debugLog(tag = TAG) { "hasAdAvailable" }

//      adLoadingStateEventFlow.send(AdLoadingStateEvent.SkipToLoad)
      debugLog(tag = TAG) { "send(AdLoadingStateEvent.SkipToLoad)" }
    } else {
      debugLog(tag = TAG) { "noAdAvailable" }

      loadAd(activity)
    }
  }

  private fun loadAd(context: Context) {
    sendLoadingTimeOutJob?.cancel()
    sendLoadingTimeOutJob = null
    sendLoadingTimeOutJob = appCoroutineScope.launch(Dispatchers.Default) {
      delay(config.app_open_ad_loading_timeout_seconds.seconds)
      debugLog(tag = TAG) { "send(AdLoadingStateEvent.TimeOut)" }
      adLoadingStateEventFlow.send(AppOpenAdLoadingStateEvent.TimeOut)
    }

    if (isLoadingAd) {
      debugLog(tag = TAG) { "do not loadAd, cuz isLoadingAd" }
      return
    }

    debugLog(tag = TAG) { "loadAd" }

    isLoadingAd = true
    AppOpenAd.load(
      context,
      adKey,
      AdRequest.Builder().build(),
      object : AppOpenAd.AppOpenAdLoadCallback() {
        override fun onAdLoaded(ad: AppOpenAd) {
          debugLog(tag = TAG) { "onAdLoaded" }

          ad.onPaidEventListener = OnPaidEventListener { adValue ->
            val adSourceName = ad.responseInfo.loadedAdapterResponseInfo?.adSourceName
            val adFormat = "app_open"
            val adUnitId = ad.adUnitId

            AnalyticsLogEvent.tryToRecordTotalAdsRevenue001(adValue, adSourceName)
            AnalyticsLogEvent.recordAdImpressionRevenue(
              adValue,
              adSourceName,
              adFormat,
              ""
            )
            AnalyticsLogEvent.recordAdImpression(
              adValue,
              adSourceName,
              adFormat,
              adUnitId
            )
            AnalyticsLogEvent.tenjinEventAdImpressionAdMob(adValue, ad)

          }

          appOpenAd = ad
          isLoadingAd = false
          latestLoadAdSuccessInstant = nowInstant()

          sendLoadingTimeOutJob?.cancel()
          sendLoadingTimeOutJob = null
          adLoadingStateEventFlow.send(AppOpenAdLoadingStateEvent.Loaded)
          debugLog(tag = TAG) { "send(AdLoadingStateEvent.Loaded)" }

          logEventRecord("ad_app_open_load_success")
        }

        override fun onAdFailedToLoad(loadAdError: LoadAdError) {
          debugLog(tag = TAG) { "onAdFailedToLoad" }

          isLoadingAd = false

          sendLoadingTimeOutJob?.cancel()
          sendLoadingTimeOutJob = null
          adLoadingStateEventFlow.send(AppOpenAdLoadingStateEvent.FailedToLoad)
        }
      }
    )

    logEventRecord("ad_app_open_load")
  }

  private fun isAdAvailable(): Boolean {
    return appOpenAd != null && checkAdIsValidAtCachePeriod()
  }

  private fun checkAdIsValidAtCachePeriod(adCachePeriodSeconds: Long = AD_CACHE_PERIOD_SECONDS): Boolean {
    val secondsDifference: Long =
      nowInstant().epochSeconds - latestLoadAdSuccessInstant.epochSeconds
    return secondsDifference < adCachePeriodSeconds
  }

  private fun showAd(activity: Activity) {
    debugLog(tag = TAG) { "showAd" }

    logEventRecord("ad_app_open_show")

    appOpenAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
      override fun onAdDismissedFullScreenContent() {
        appOpenAd = null
        loadAd(activity)
        adShowStateEventFlow.send(AppOpenAdShowStateEvent.Finish)
      }

      override fun onAdFailedToShowFullScreenContent(adError: AdError) {
        debugLog(tag = TAG) { "onAdFailedToShowFullScreenContent" }
        appOpenAd = null
        loadAd(activity)
        adShowStateEventFlow.send(AppOpenAdShowStateEvent.FailedToShow)
      }

      override fun onAdShowedFullScreenContent() {
        fullscreenAdManager.latestShowAdSuccessInstant = nowInstant()
        adShowStateEventFlow.send(AppOpenAdShowStateEvent.Showing)
        debugLog(tag = TAG) { "onAdShowedFullScreenContent" }
      }

      override fun onAdClicked() {
        splashController.skipSplash(true)
        logEventRecord("ad_app_open_click")
      }

      override fun onAdImpression() {
        logEventRecord("ad_app_open_impress")
      }
    }

    appOpenAd?.show(activity)
  }

  suspend fun tryToShowAd(
    activity: Activity,
    immediate: Boolean = false
  ) = withContext(Dispatchers.Main.immediate) {
    debugLog(tag = TAG) { "tryToShowAd" }

    if (fullscreenAdManager.isAdShowTimeInShowInterval()) {
      debugLog(tag = TAG) { "isAdShowTimeInShowInterval" }
      adShowStateEventFlow.send(AppOpenAdShowStateEvent.SkipToShow)
    } else { // over the show interval, need to show ad
      appCoroutineScope.launch(Dispatchers.Default) {
        if (!immediate) {
          biReporter.reportAdEvent(adType = "app_open", adPlace = "splash")
        }
      }

      debugLog(tag = TAG) { "over the show interval, need to show ad" }
      if (isAdAvailable()) { // cache available
        debugLog(tag = TAG) { "cache available" }
        if (!immediate) {
          delay(1_000)
        }
        showAd(activity)
      } else { // cache not available
        debugLog(tag = TAG) { "cache not available" }
        loadAd(activity)
      }
    }
  }
}