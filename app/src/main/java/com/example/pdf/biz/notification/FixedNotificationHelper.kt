package com.example.pdf.biz.notification

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.view.View
import android.widget.RemoteViews
import androidx.core.app.NotificationCompat
import androidx.core.content.getSystemService
import com.example.pdf.R
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.biz.bi.BiRecordCkEvent
import com.example.pdf.biz.bi.BiReporter
import com.example.pdf.coroutine.AppCoroutineScope
import com.example.pdf.kermit.debugLog
import com.example.pdf.lyricist.globalStrings
import com.example.pdf.lyricist.matchStrings
import com.example.pdf.lyricist.runtimeLanguageTagFlow
import com.example.pdf.serialization.toJson
import com.example.pdf.ui.activity.SplashActivity
import com.example.pdf.ui.activity.SplashLaunchType
import com.example.pdf.ui.node.home.HomeNode
import com.example.pdf.ui.node.home.HomeNodeAction
import com.example.pdf.ui.node.home.HomeTab
import kotlinx.coroutines.launch
import org.koin.core.annotation.Single

@Single
class FixedNotificationHelper(
  private val context: Context,
  private val appCoroutineScope: AppCoroutineScope,
  private val biReporter: BiReporter,
) {

  companion object {
    const val NotiServiceId = 0x10001
    private const val NotiChannelId = "tools_channel_id"

    val NOTI_CLICK_EVENT = "fixed_noti_click_event"
  }

  private val androidNotificationManager
    get() = context.getSystemService<NotificationManager>()

  private val clickNotiIntent =
    SplashActivity.createIntent(context, type = SplashLaunchType.NOTIFICATION_CLICK)
      .putExtra(NOTI_CLICK_EVENT, NOTI_CLICK_EVENT)

  private val pendingIntent = PendingIntent.getActivity(
    context,
    NotiServiceId,
    clickNotiIntent,
    pendingIntentDefaultFlags
  )

  fun handleClickEvent(intent: Intent) {
    if (intent.getStringExtra(NOTI_CLICK_EVENT) == NOTI_CLICK_EVENT) {
      logEventRecord("click_notification_bar")
      appCoroutineScope.launch {
        biReporter.reportCkEvent(BiRecordCkEvent(event_name = "fixed_noti_click"))
      }
    }
  }

  fun startNoti(
    service: Service?,
    context: Context = this.context,
    startForeground: Boolean = false,
    forceUpdate: Boolean = false,
  ) {
    if (!forceUpdate) {
      val shown = androidNotificationManager
        ?.activeNotifications
        ?.any { it.id == NotiServiceId } == true

      debugLog("startNoti currentShown: $shown")
      if (shown) return
    }

    val noti = NotificationCompat.Builder(context, NotiChannelId).apply {
      setSmallIcon(R.drawable.ic_docs)
      setContentIntent(pendingIntent)
      setCustomContentView(createFixNotiRemoveView())
      setCustomBigContentView(createFixNotiRemoveView(true))
      setOngoing(true)
      setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
      setVibrate(null)
    }.build()

    createChannelIfNeeded()

    if (service == null) {
      androidNotificationManager?.notify(
        NotiServiceId,
        noti
      )
    }

    if (startForeground) {
      service?.startForeground(NotiServiceId, noti)
      debugLog("startNoti Service:$service started in foreground")
    }

    appCoroutineScope.launch {
      biReporter.reportCkEvent(BiRecordCkEvent(event_name = "fixed_noti_show"))
    }
  }

  fun updateNoti(context: Context) {
    runCatching {
      startNoti(null, context, forceUpdate = true)
    }
  }

  private fun createFixNotiRemoveView(forceUseBlankDec: Boolean = false): RemoteViews {
    val remoteViewsLayoutRes = if (forceUseBlankDec) {
      R.layout.layout_fixed_noti_blank_dec
    } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
      R.layout.layout_fixed_noti
    } else {
      R.layout.layout_fixed_noti_blank_dec
    }

    return RemoteViews(
      context.packageName,
      remoteViewsLayoutRes
    ).apply {
      setOnClickPendingIntent(
        R.id.iv_home, NotificationActionNavigator.createNavigateIntent(
          context = context,
          notificationId = 0x10001_1,
          screenNode = HomeNode(tab = HomeTab.ALL),
          clickCkEventJson = BiRecordCkEvent(
            event_name = "fixed_noti_click",
            params = listOf("all")
          ).toJson(),
          clickEventRecord = "click_fixed_home",
        )
      )
      setOnClickPendingIntent(
        R.id.iv_bookmarks, NotificationActionNavigator.createNavigateIntent(
          context = context,
          notificationId = 0x10001_2,
          screenNode = HomeNode(tab = HomeTab.BOOKMARKS),
          clickCkEventJson = BiRecordCkEvent(
            event_name = "fixed_noti_click",
            params = listOf("bookmarks")
          ).toJson(),
          clickEventRecord = "click_fixed_noti_bookmarks",
        )
      )
      setOnClickPendingIntent(
        R.id.iv_scan, NotificationActionNavigator.createNavigateIntent(
          context = context,
          notificationId = 0x10001_3,
          screenNode = HomeNode(tab = HomeTab.ALL, action = HomeNodeAction.Scan2Pdf),
          clickCkEventJson = BiRecordCkEvent(
            event_name = "fixed_noti_click",
            params = listOf("scan")
          ).toJson(),
          clickEventRecord = "click_fixed_noti_scan",
        )
      )
      setOnClickPendingIntent(
        R.id.iv_tools, NotificationActionNavigator.createNavigateIntent(
          context = context,
          notificationId = 0x10001_4,
          screenNode = HomeNode(tab = HomeTab.TOOLS),
          clickCkEventJson = BiRecordCkEvent(
            event_name = "fixed_noti_click",
            params = listOf("tools")
          ).toJson(),
          clickEventRecord = "click_fixed_noti_tools",
        )
      )

      val strings = matchStrings(runtimeLanguageTagFlow.value)

      setTextViewText(R.id.tv_home, strings.home)
      setTextViewText(R.id.tv_bookmarks, strings.bookmarks)
      setTextViewText(R.id.tv_scan, strings.scan)
      setTextViewText(R.id.tv_tools, strings.convert)

      if (remoteViewsLayoutRes == R.layout.layout_fixed_noti && !forceUseBlankDec) {
        setViewVisibility(R.id.tv_home, View.GONE)
        setViewVisibility(R.id.tv_bookmarks, View.GONE)
        setViewVisibility(R.id.tv_scan, View.GONE)
        setViewVisibility(R.id.tv_tools, View.GONE)
      }
    }
  }


  private var channelCreated = false
  private fun createChannelIfNeeded() {
    if (channelCreated) return

    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
      val serviceChannel = NotificationChannel(
        NotiChannelId,
        context.getString(R.string.app_name),
        NotificationManager.IMPORTANCE_DEFAULT
      ).apply {
        setSound(null, null)
      }

      androidNotificationManager?.createNotificationChannel(serviceChannel).apply {
        channelCreated = true
      }
    }
  }
}
