package com.example.pdf.biz.fcm

import com.example.pdf.BuildConfig
import com.example.pdf.biz.TimeTickReceiver
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.biz.bi.BiRecordCkEvent
import com.example.pdf.biz.bi.BiReporter
import com.example.pdf.biz.bi.FcmTokenManager
import com.example.pdf.biz.bi.reportFcmServiceNotificationConsumed
import com.example.pdf.coroutine.AppCoroutineScope
import com.example.pdf.kdatetime.nowInstant
import com.example.pdf.kermit.debugLog
import com.example.pdf.mmkv.UserSettingsKvStore
import com.example.pdf.serialization.toJson
import com.example.pdf.serialization.toJsonWithoutQuotesSurround
import com.example.pdf.serialization.toObj
import com.example.pdf.ui.node.home.tools.areNotificationsEnabled
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import kotlinx.coroutines.launch
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import org.koin.android.ext.android.inject

class SimpleFirebaseMessagingService : FirebaseMessagingService() {

  private val sfmn: SimpleFirebaseMessagingNotification by inject()
  private val fsnh: FcmServiceNotificationHelper by inject()

  private val fcmTokenManager: FcmTokenManager by inject()
  private val userSettingsKvStore: UserSettingsKvStore by inject()

  private val appCoroutineScope: AppCoroutineScope by inject()
  val biReporter: BiReporter by inject()


  @Serializable
  enum class MessageType {
    @SerialName("pdf_fcm_console")
    FirebaseConsole,

    @SerialName(BuildConfig.APPLICATION_ID)
    Server,
  }

  override fun onNewToken(token: String) {
    super.onNewToken(token)
    fcmTokenManager.handleNewFcmToken(token)
  }

  override fun onMessageReceived(message: RemoteMessage) {
//        super.onMessageReceived(message)
    debugLog("SimpleFirebaseMessagingService onMessageReceived()")
    logEventRecord("receive_fcm")
    handleMessage(message)
    executeTimeTickTaskIfNeeded()
  }

  private fun handleMessage(message: RemoteMessage) {
    val msgTypeRawValue = message.data["type"]

    when (msgTypeRawValue?.toObj<MessageType>()) {
      MessageType.FirebaseConsole -> handleConsoleMessage(message)
      MessageType.Server -> handleServerMessage(message)
      null -> {}
    }
  }

  private fun handleConsoleMessage(message: RemoteMessage) {
    val title = message.notification?.title ?: return
    val content = message.notification?.body ?: return

    val data = message.data

    val notiType = data[SimpleFirebaseMessagingNotification.EXTRA_KEY_FCM_NOTI_TYPE] ?: "-1"

    val notiTypeInt = try {
      notiType.toInt()
    } catch (e: Exception) {
      -1
    }

    sfmn.notify(
      this,
      title = title,
      content = content,
      fcmNotiType = notiTypeInt,
      clickCkEvent = BiRecordCkEvent(
        event_name = "fcmc_noti_click",
        params = listOf(notiTypeInt.toString())
      ),
    )
    logEventRecord("fcm_handle_console_message")
  }

  private fun handleServerMessage(message: RemoteMessage) {
    if (userSettingsKvStore.enabledNotification.value.not()) return
    if (areNotificationsEnabled().not()) return

    val nowSeconds = nowInstant().epochSeconds

    // 50秒内收到多条FCM消息时，只处理第一条消息
    if (nowSeconds - latestHandleServerMessageSeconds < 50) {
      debugLog("SimpleFirebaseMessagingService handleServerMessage() - 忽略重复消息，距离上次处理时间: ${nowSeconds - latestHandleServerMessageSeconds}秒")
      logEventRecord("fcm_handle_server_message_ignored")
      return
    }

    latestHandleServerMessageSeconds = nowSeconds

    super.onMessageReceived(message)
    logEventRecord("fcm_handle_server_message")

    val fcmServiceMessage = message.data["payload"]?.toObj<FcmServiceMessage>() ?: return
    if (fcmServiceMessage.isEmptyMessage()) return

    val destinationJsonName = fcmServiceMessage.destination.toJsonWithoutQuotesSurround()
    val clickEventRecords =
      arrayOf("click_fcm_s_noti", "click_fcm_s_noti_$destinationJsonName")

    fsnh.notifyRepeat(
      message = fcmServiceMessage,
      channelId = "fcm_service_channel_id",
      clickCkEvent = BiRecordCkEvent(
        event_name = "fcms_noti_click",
        params = listOf(destinationJsonName)
      ),
      clickEventRecord = clickEventRecords,
    )

    appCoroutineScope.launch {
      biReporter.reportFcmServiceNotificationConsumed(destinationJsonName)
    }
  }


  private var latestExecuteTimeTickTaskSeconds = 0L
  private var latestHandleServerMessageSeconds = 0L

  private fun executeTimeTickTaskIfNeeded() {
    val nowSeconds = nowInstant().epochSeconds

    if (nowSeconds - 60 >= latestExecuteTimeTickTaskSeconds) {
      TimeTickReceiver.handleTimeTick(this.applicationContext)
      latestExecuteTimeTickTaskSeconds = nowSeconds
    }
  }
}