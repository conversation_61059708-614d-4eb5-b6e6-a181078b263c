@file:Suppress("PropertyName")

package com.example.pdf.biz.bi

import kotlinx.serialization.EncodeDefault
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class BiRecordEvent(
  @SerialName("signal") val event_name: String,
  @SerialName("from") val activity: String,
  @SerialName("type") val type: Int
)

@Serializable
data class BiRecordValue(
  @SerialName("creations") val value: Float,
  @SerialName("currency") val currency: String,
  @SerialName("primeBenefit") val precision_type: String,
  @SerialName("structureType") val ad_network: String,
  @SerialName("type") val ad_type: String,
  @SerialName("effectRatio") val ad_placement: String,
)

@Serializable
data class BiRecordCkEvent(
  @SerialName("case") val event_name: String,
  @SerialName("predictions") @EncodeDefault val params: List<String> = emptyList()
)