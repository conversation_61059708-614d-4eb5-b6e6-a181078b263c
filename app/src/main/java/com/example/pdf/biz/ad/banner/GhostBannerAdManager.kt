package com.example.pdf.biz.ad.banner

import android.content.Context
import android.view.ViewGroup
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.kermit.debugLog
import kotlinx.coroutines.flow.MutableStateFlow
import org.koin.core.annotation.Single

@Single
class GhostBannerAdManager(
  private val context: Context,
) {
  @Suppress("PrivatePropertyName")
  private val TAG = "GhostBannerAdManager"
  private val adUnitNameLowercase = "banner"

  private val adStateFlowMap = mutableMapOf<BannerAdPlace, MutableStateFlow<GhostBannerAdState>>()

  fun getAdStateFlow(adPlace: BannerAdPlace): MutableStateFlow<GhostBannerAdState> {
    return adStateFlowMap.getOrPut(adPlace) {
      MutableStateFlow(GhostBannerAdState.Companion.Empty)
    }
  }

  fun buildAd(adPlace: BannerAdPlace, adWidthDp: Int = 0) {
    debugLog(tag = TAG) { "buildAd for place: ${adPlace.name} (Ghost - no-op)" }
    
    // Log the load event to maintain analytics consistency
    logEventRecord("ad_${adUnitNameLowercase}_load")
    
    // Immediately mark as ready but don't actually load anything
    getAdStateFlow(adPlace).value = GhostBannerAdState(isReady = false, isPause = false)
    
    // Log success event to maintain analytics consistency
    logEventRecord("ad_${adUnitNameLowercase}_load_success")
  }

  fun showBanner(place: BannerAdPlace, adContainer: ViewGroup) {
    debugLog(tag = TAG) { "showBanner for place: ${place.name} (Ghost - no-op)" }
    // Do nothing - this is a no-op provider
  }

  fun pause(adPlace: BannerAdPlace) {
    debugLog(tag = TAG) { "pause for place: ${adPlace.name} (Ghost - no-op)" }
    getAdStateFlow(adPlace).value = getAdStateFlow(adPlace).value.copy(isPause = true)
  }

  fun resume(adPlace: BannerAdPlace) {
    debugLog(tag = TAG) { "resume for place: ${adPlace.name} (Ghost - no-op)" }
    getAdStateFlow(adPlace).value = getAdStateFlow(adPlace).value.copy(isPause = false)
  }

  fun destroy(adPlace: BannerAdPlace) {
    debugLog(tag = TAG) { "destroy for place: ${adPlace.name} (Ghost - no-op)" }
    getAdStateFlow(adPlace).value = GhostBannerAdState.Companion.Empty
  }

  fun destroyAll() {
    debugLog(tag = TAG) { "destroyAll (Ghost - no-op)" }
    adStateFlowMap.values.forEach {
      it.value = GhostBannerAdState.Companion.Empty
    }
  }
}
