package com.example.pdf.biz.ad.nat1ve

import android.content.Context
import com.example.pdf.biz.BizConfig
import com.example.pdf.biz.SplashController
import com.example.pdf.biz.analytic.AnalyticsLogEvent
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import com.example.pdf.coroutine.AppCoroutineScope
import com.example.pdf.kermit.debugLog
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdLoader
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.nativead.NativeAd
import com.google.android.gms.ads.nativead.NativeAdOptions
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.koin.core.annotation.Single

@Single
class AdmobNativeAdManager(
  private val context: Context,
  private val splashController: SplashController,
  private val remoteConfig: RealRemoteConfig,
  private val appCoroutineScope: AppCoroutineScope,
) {
  @Suppress("PrivatePropertyName")
  private val TAG = "NativeAdManager"
  private val adUnitNameLowercase = "native"

  private val adKey
    get() = remoteConfig.adConfig2.native_ad_key.ifEmpty { BizConfig.ADMOB_AD_UNIT_ID_FOR_NATIVE }

  private val adFlowMap = mutableMapOf<NativeAdPlace, MutableStateFlow<NativeAd?>>()
  private val adStateFlowMap = mutableMapOf<NativeAdPlace, MutableStateFlow<AdmobNativeAdState>>()


  fun getAdFlow(place: NativeAdPlace): MutableStateFlow<NativeAd?> {
    return adFlowMap.getOrPut(place) {
      MutableStateFlow(null)
    }
  }


  fun buildAd(place: NativeAdPlace) {
    val adBuilder = AdLoader.Builder(context, adKey)

    adBuilder.forNativeAd { newNativeAd ->
      appCoroutineScope.launch(Dispatchers.Main.immediate) {
        val adFlow = getAdFlow(place)
        val cacheNativeAd = adFlow.first()

        cacheNativeAd?.destroy()

        newNativeAd.setOnPaidEventListener { adValue ->
          val adSourceName =
            newNativeAd.responseInfo?.loadedAdapterResponseInfo?.adSourceName
          val adFormat = "native"
          val adUnitId = adKey

          AnalyticsLogEvent.tryToRecordTotalAdsRevenue001(adValue, adSourceName)
          AnalyticsLogEvent.recordAdImpressionRevenue(
            adValue,
            adSourceName,
            adFormat,
            adFormat + "_" + place.named
          )
          AnalyticsLogEvent.recordAdImpression(adValue, adSourceName, adFormat, adUnitId)
          AnalyticsLogEvent.tenjinEventAdImpressionAdMob(adValue, newNativeAd)
        }
        adFlow.update { newNativeAd }
      }
    }

    adBuilder.withNativeAdOptions(
      NativeAdOptions.Builder().setAdChoicesPlacement(
        NativeAdOptions.ADCHOICES_BOTTOM_RIGHT
      ).build()
    )

    val adLoader = adBuilder.withAdListener(object : AdListener() {
      override fun onAdFailedToLoad(loadAdError: LoadAdError) {
        // todo try once
        debugLog(tag = TAG) { "onAdFailedToLoad" }
      }

      override fun onAdLoaded() {
        logEventRecord("ad_${adUnitNameLowercase}_load_success")
      }

      override fun onAdClicked() {
        debugLog(tag = TAG) { "place:${place.name} onAdClicked()" }
//                logEventRecord("ad_native_click")
        // should use onAdOpened() replace onAdClicked(). cuz sometimes onAdClicked() will not get callback
      }

      override fun onAdOpened() {
        debugLog(tag = TAG) { "place:${place.name} onAdOpened()" }
        splashController.skipSplash(true)
        logEventRecord("ad_${adUnitNameLowercase}_click")
      }

      override fun onAdImpression() {
        logEventRecord("ad_${adUnitNameLowercase}_impress")
      }

    }).build()

    adLoader.loadAd(AdRequest.Builder().build())

    logEventRecord("ad_${adUnitNameLowercase}_load")
  }

  fun destroy(place: NativeAdPlace) {
    appCoroutineScope.launch(Dispatchers.Main.immediate) {
      adFlowMap[place]?.first()?.destroy()
      adFlowMap[place]?.update { null }
    }
  }

  fun destroyAll() {
    appCoroutineScope.launch(Dispatchers.Main.immediate) {
      adFlowMap.keys.forEach(::destroy)
    }
  }
}
