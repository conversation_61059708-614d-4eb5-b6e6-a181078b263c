package com.example.pdf.biz.ad.nat1ve

import androidx.lifecycle.ViewModel
import com.example.pdf.kermit.debugLog
import org.koin.android.annotation.KoinViewModel

@KoinViewModel
class TradPlusNativeAdViewModel(
  private val place: NativeAdPlace,
  private val tradPlusNativeAdManager: TradPlusNativeAdManager
) : ViewModel() {
  override fun onCleared() {
    debugLog(tag = "TRADPLUS_NATIVE_AD") { "viewModel onCleared() call destroy() place: ${place.name}" }
    tradPlusNativeAdManager.destroy(place)
    super.onCleared()
  }
}