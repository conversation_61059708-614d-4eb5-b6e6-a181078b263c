package com.example.pdf.biz.ad.interstitial

import android.app.Activity
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.flow.EventFlow
import com.example.pdf.flow.send
import com.example.pdf.kermit.debugLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Single

@Single
class GhostInterstitialAdManager {
  @Suppress("PrivatePropertyName")
  private val TAG = "GhostInterstitialAdManager"
  private val adUnitNameLowercase = "interstitial"

  private val latestActiveAdPlaceNameFlow = MutableStateFlow<String?>(null)

  val adLoadingStateEventFlow = EventFlow<InterstitialAdLoadingStateEvent>()
  val adShowStateEventFlow = EventFlow<InterstitialAdShowStateEvent>()
  val adShowStateFlow = MutableStateFlow(false)

  fun tryToLoadAd(activity: Activity) {
    debugLog(tag = TAG) { "tryToLoadAd (Ghost - no-op)" }
    
    // Log the load event to maintain analytics consistency
    logEventRecord("ad_${adUnitNameLowercase}_load")
    
    // Immediately send FailedToLoad event since we don't actually load ads
    adLoadingStateEventFlow.send(InterstitialAdLoadingStateEvent.FailedToLoad)
  }

  fun loadAd(activity: Activity) {
    debugLog(tag = TAG) { "loadAd (Ghost - no-op)" }
    
    // Log the load event to maintain analytics consistency
    logEventRecord("ad_${adUnitNameLowercase}_load")
    
    // Immediately send FailedToLoad event since we don't actually load ads
    adLoadingStateEventFlow.send(InterstitialAdLoadingStateEvent.FailedToLoad)
  }

  suspend fun tryToShowAd(
    activity: Activity,
    adPlaceName: String? = null,
    immediate: Boolean = false,
    onReadyShowAd: (() -> Unit)? = null
  ) = withContext(Dispatchers.Main.immediate) {
    debugLog(tag = TAG) { "tryToShowAd (Ghost - no-op)" }

    adPlaceName?.let {
      latestActiveAdPlaceNameFlow.value = adPlaceName
    }

    // Always skip showing ads since this is a no-op provider
    adShowStateEventFlow.send(InterstitialAdShowStateEvent.SkipToShow)
  }

  fun showAd(activity: Activity) {
    debugLog(tag = TAG) { "showAd (Ghost - no-op)" }
    
    // Immediately send SkipToShow event since we don't actually show ads
    adShowStateEventFlow.send(InterstitialAdShowStateEvent.SkipToShow)
  }

  private fun isAdAvailable(): Boolean {
    // Always return false since we never have ads available
    return false
  }

  fun onDestroy() {
    debugLog(tag = TAG) { "onDestroy (Ghost - no-op)" }
    // Reset state
    latestActiveAdPlaceNameFlow.value = null
    adShowStateFlow.value = false
  }
}
