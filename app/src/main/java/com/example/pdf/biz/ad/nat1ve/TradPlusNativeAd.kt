package com.example.pdf.biz.ad.nat1ve

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.compose.animation.Crossfade
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.example.pdf.android.context.findActivity
import com.example.pdf.biz.ad.AdColors
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.databinding.LayoutNativeAdContenWithoutMediaTpBinding
import com.example.pdf.databinding.LayoutNativeAdContentTpBinding
import com.example.pdf.kermit.debugLog
import com.tradplus.ads.base.adapter.nativead.TPNativeAdView
import com.tradplus.ads.base.common.TPImageLoader
import com.tradplus.ads.open.nativead.TPNativeAdRender
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.koin.core.parameter.parametersOf

@Composable
fun TradPlusNativeAd(
  place: NativeAdPlace,
  modifier: Modifier = Modifier,
  isWithoutMedia: Boolean = false,
) {
  val context = LocalContext.current

  @Suppress("UnusedVariable", "unused")
  val viewModel: TradPlusNativeAdViewModel = koinViewModel { parametersOf(place) }
  val nativeAdManager: TradPlusNativeAdManager = koinInject()

  val tpBaseAd by nativeAdManager.getTpBaseAdFlow(place).collectAsState()
  val tpNativeAdState by nativeAdManager.getTpAdStateFlow(place).collectAsState()

  debugLog(tag = "TRADPLUS_NATIVE_AD") { "tpBaseAd: $tpBaseAd" }

  var adContainer by remember { mutableStateOf(context.buildFrameLayout()) }

  LaunchedEffect(tpBaseAd) {
    if (tpBaseAd == null) {
      debugLog(tag = "TRADPLUS_NATIVE_AD") { "nativeAdManager.buildAd(place)" }
      nativeAdManager.buildAd(place, isWithoutMedia)
    } else {
      // Get the TPNative instance from manager
      val tpNative = nativeAdManager.getTpNative(place)
      val nativeAdView = nativeAdManager.getTpNativeAdView(place)
      tpNative?.let { _ ->
        if (nativeAdView == null) {
          debugLog(tag = "TRADPLUS_NATIVE_AD") { "nativeAdView == null" }

          // Show the ad using TradPlus rendering
          val newAdNativeAdView = context.buildFrameLayout()
          tpNative.showAd(newAdNativeAdView, object : TPNativeAdRender() {
            init {
              debugLog(tag = "TRADPLUS_NATIVE_AD") { "Showing TradPlus native ad" }
            }

            override fun createAdLayoutView(): ViewGroup? = null

            override fun renderAdView(tpNativeAdView: TPNativeAdView): ViewGroup {
              val tpNativeAdRender = this
              val layoutViewBindingRoot = if (isWithoutMedia) {
                context.nativeAdContentWithoutMediaBinding().apply {
                  configure(tpNativeAdView, tpNativeAdRender)
                }.root
              } else {
                context.nativeAdContentBinding().apply {
                  configure(tpNativeAdView, tpNativeAdRender)
                }.root
              }

              newAdNativeAdView.addView(layoutViewBindingRoot)

              return layoutViewBindingRoot.apply {
                nativeAdManager.cacheAddView(place, newAdNativeAdView)
                adContainer.removeAllViews()
                adContainer.addView(newAdNativeAdView)
                debugLog(tag = "TRADPLUS_NATIVE_AD") { "add new ad view to container" }
              }
            }
          }, "native_${place.named}")
        } else {
          runCatching {
            (nativeAdView.parent as? ViewGroup)?.removeAllViews()
          }

          adContainer.apply {
            removeAllViews()
            layoutParams = ViewGroup.LayoutParams(
              ViewGroup.LayoutParams.MATCH_PARENT,
              ViewGroup.LayoutParams.WRAP_CONTENT
            )
            addView(nativeAdView.apply {
              layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
              )
            })
          }

          debugLog(tag = "TRADPLUS_NATIVE_AD") { "add cache ad view to container" }
        }
      }
    }
  }

//    LaunchedEffect(Unit) {
//        logEventRecord("ad_native_show")
//
//        if (nativeAd == null) {
//            debugLog(tag = "TRADPLUS_NATIVE_AD") { "nativeAdManager.buildAd(place)" }
//            nativeAdManager.buildAd(place)
//        }
//    }

  DisposableEffect(Unit) {
    logEventRecord("ad_native_show")
    onDispose { }
  }

  val (adHeight, adPadding) = if (isWithoutMedia) {
    72.dp to 0.dp
  } else {
    138.dp to 8.dp
  }

  Box(modifier = modifier.background(color = AdColors.backgroundColor)) {
    Crossfade(
      targetState = tpNativeAdState.isReady,
      modifier = Modifier.padding(adPadding),
    ) { hasNativeAd ->

      if (hasNativeAd) {
        AndroidView(
          factory = { _ -> adContainer },
          modifier = Modifier
            .fillMaxWidth()
            .height(adHeight)
        )
      } else {
        if (isWithoutMedia) {
          NativeAdWithoutMediaPlaceholder(
            modifier = Modifier
              .fillMaxWidth()
              .height(adHeight)
          )
        } else {
          NativeAdPlaceholder(
            modifier = Modifier
              .fillMaxWidth()
              .height(adHeight)
          )
        }
      }
    }
  }
}

private fun Context.buildFrameLayout() = FrameLayout(this).apply {
  layoutParams = ViewGroup.LayoutParams(
    ViewGroup.LayoutParams.MATCH_PARENT,
    ViewGroup.LayoutParams.WRAP_CONTENT
  )
}

private fun Context.nativeAdContentBinding(): LayoutNativeAdContentTpBinding {
  val layoutInflater = this.findActivity().layoutInflater

  return LayoutNativeAdContentTpBinding.inflate(layoutInflater)
}


private fun Context.nativeAdContentWithoutMediaBinding(): LayoutNativeAdContenWithoutMediaTpBinding {
  val layoutInflater = this.findActivity().layoutInflater

  return LayoutNativeAdContenWithoutMediaTpBinding.inflate(layoutInflater)
}

private fun LayoutNativeAdContentTpBinding.configure(
  tpNativeAdView: TPNativeAdView,
  tpNativeAdRender: TPNativeAdRender
) {
  val nativeAdView = this.nativeAd

  val mediaContainer = adMedia

  // Handle main media content
  if (tpNativeAdView.mediaView != null) {
    // Use MediaView if available
    mediaContainer.removeAllViews()
    mediaContainer.addView(tpNativeAdView.mediaView)
    tpNativeAdRender.clickViews.add(tpNativeAdView.mediaView)
    debugLog(tag = "TRADPLUS_NATIVE_AD") { "mediaContainer.addView(mediaView)" }
  } else if (tpNativeAdView.mainImage != null) {
    // Use drawable if available
    val imageView = ImageView(root.context)
    imageView.setImageDrawable(tpNativeAdView.mainImage)
    imageView.scaleType = ImageView.ScaleType.CENTER_CROP
    mediaContainer.removeAllViews()
    mediaContainer.addView(imageView)
    debugLog(tag = "TRADPLUS_NATIVE_AD") { "mediaContainer.addView(imageView via drawable)" }
  } else if (tpNativeAdView.mainImageUrl != null) {
    // Load image from URL
    val imageView = ImageView(root.context)
    imageView.scaleType = ImageView.ScaleType.CENTER_CROP
    TPImageLoader.getInstance().loadImage(imageView, tpNativeAdView.mainImageUrl)
    mediaContainer.removeAllViews()
    mediaContainer.addView(imageView)
    debugLog(tag = "TRADPLUS_NATIVE_AD") { "mediaContainer.addView(imageView via mainImageUrl)" }
  }

  // Handle app icon
  val iconView = adAppIcon
  iconView.visibility = View.VISIBLE
  if (tpNativeAdView.iconImage != null) {
    iconView.setImageDrawable(tpNativeAdView.iconImage)
  } else if (tpNativeAdView.iconImageUrl != null) {
    TPImageLoader.getInstance()
      .loadImage(iconView, tpNativeAdView.iconImageUrl)
  } else if (tpNativeAdView.iconView != null) {
    val params = iconView.layoutParams
    val parent = iconView.parent as? ViewGroup
    parent?.let {
      val index = it.indexOfChild(iconView)
      it.removeView(iconView)
      it.addView(tpNativeAdView.iconView as View, index, params)
    }
  } else {
    iconView.visibility = View.GONE
  }

  // Handle title/headline
  val titleView = adHeadline
  if (tpNativeAdView.title != null) {
    titleView.text = tpNativeAdView.title
  }

  // Handle body text
  val bodyView = adBody
  if (tpNativeAdView.subTitle != null) {
    bodyView.text = tpNativeAdView.subTitle
  }

  // Handle advertiser
  val advertiserView = adAdvertiser
  if (tpNativeAdView.advertiserName != null) {
    advertiserView.visibility = View.VISIBLE
    advertiserView.text = tpNativeAdView.advertiserName
  } else {
    advertiserView.visibility = View.GONE
  }

  // Handle CTA button
  val ctaView = adCallToAction
  if (tpNativeAdView.callToAction != null) {
    ctaView.text = tpNativeAdView.callToAction
  }

  // Set clickable views
  // Note: For media container, we add it to clickViews manually above
  tpNativeAdRender.setIconView(iconView, true)
  tpNativeAdRender.setTitleView(titleView, true)
  tpNativeAdRender.setSubTitleView(bodyView, true)
  tpNativeAdRender.setCallToActionView(ctaView, true)
}

private fun LayoutNativeAdContenWithoutMediaTpBinding.configure(
  tpNativeAdView: TPNativeAdView,
  tpNativeAdRender: TPNativeAdRender
) {
  val nativeAdView = this.nativeAd

  // Handle app icon
  val iconView = adAppIcon
  iconView.visibility = View.VISIBLE
  if (tpNativeAdView.iconImage != null) {
    iconView.setImageDrawable(tpNativeAdView.iconImage)
  } else if (tpNativeAdView.iconImageUrl != null) {
    TPImageLoader.getInstance()
      .loadImage(iconView, tpNativeAdView.iconImageUrl)
  } else if (tpNativeAdView.iconView != null) {
    val params = iconView.layoutParams
    val parent = iconView.parent as? ViewGroup
    parent?.let {
      val index = it.indexOfChild(iconView)
      it.removeView(iconView)
      it.addView(tpNativeAdView.iconView as View, index, params)
    }
  } else {
    iconView.visibility = View.GONE
  }

  // Handle title/headline
  val titleView = adHeadline
  if (tpNativeAdView.title != null) {
    titleView.text = tpNativeAdView.title
  }

  // Handle body text
  val bodyView = adBody
  if (tpNativeAdView.subTitle != null) {
    bodyView.text = tpNativeAdView.subTitle
  }

  // Handle advertiser
  val advertiserView = adAdvertiser
  if (tpNativeAdView.advertiserName != null) {
    advertiserView.visibility = View.VISIBLE
    advertiserView.text = tpNativeAdView.advertiserName
  } else {
    advertiserView.visibility = View.GONE
  }

  // Handle CTA button
  val ctaView = adCallToAction
  if (tpNativeAdView.callToAction != null) {
    ctaView.text = tpNativeAdView.callToAction
  }

  // Set clickable views
  // Note: For media container, we add it to clickViews manually above
  tpNativeAdRender.setIconView(iconView, true)
  tpNativeAdRender.setTitleView(titleView, true)
  tpNativeAdRender.setSubTitleView(bodyView, true)
  tpNativeAdRender.setCallToActionView(ctaView, true)
}