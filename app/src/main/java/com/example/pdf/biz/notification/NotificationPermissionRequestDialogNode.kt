package com.example.pdf.biz.notification

import android.os.Build
import androidx.activity.ComponentActivity
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import com.example.pdf.R
import com.example.pdf.android.context.findActivity
import com.example.pdf.android.context.openNotificationSettings
import com.example.pdf.guia.DialogNode
import com.example.pdf.kermit.debugLog
import com.example.pdf.lumo.AppTheme
import com.example.pdf.ui.feature.bottomsheet.permission.PermissionBottomSheet
import cafe.adriel.lyricist.LocalStrings
import com.holix.android.bottomsheetdialog.compose.BottomSheetBehaviorProperties
import com.holix.android.bottomsheetdialog.compose.BottomSheetDialog
import com.holix.android.bottomsheetdialog.compose.BottomSheetDialogProperties
import com.roudikk.guia.core.Dialog
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import kotlinx.parcelize.Parcelize
import org.koin.compose.koinInject

@Parcelize
class NotificationPermissionRequestDialogNode(
  private val useSystemPermissionDialog: Boolean = false,
  private val autoIncrementRequestCounter: Boolean = true,
) : DialogNode("noti_permission_request_dialog") {
  @Composable
  override fun Content(navigator: Navigator, dialog: Dialog?) {
    debugLog(tag = "NotificationPermissionRequestDialog") { "Node content()" }
    NotificationPermissionRequestDialog(navigator::pop, useSystemPermissionDialog, autoIncrementRequestCounter)
  }
}


@Composable
fun NotificationPermissionRequestDialog(
  onDismiss: () -> Unit,
  useSystemPermissionDialog: Boolean,
  autoIncrementRequestCounter: Boolean,
) {
  DisposableEffect(Unit) {
    debugLog(tag = "NotificationPermissionRequestDialog") { "dialog()" }

    onDispose {}
  }

  val context = LocalContext.current
  val notificationPermissionRequester: NotificationPermissionRequester = koinInject()

  val onPop = {
    if (autoIncrementRequestCounter) {
      notificationPermissionRequester.requestCounterIncrement()
    }
    onDismiss()
  }

  BottomSheetDialog(
    onPop,
    properties = BottomSheetDialogProperties(
      dismissOnClickOutside = false,
      behaviorProperties = BottomSheetBehaviorProperties(
      )
    )
  ) {
    PermissionBottomSheet(
      iconPainter = painterResource(R.drawable.img_permission_notification),
      title = LocalStrings.current.allowNotification,
      description = LocalStrings.current.notificationPermissionMessage,
      buttonText = LocalStrings.current.grant,
      onButtonClick = {
        if (useSystemPermissionDialog) {
          if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            notificationPermissionRequester.showSystemRequester(context.findActivity(), false)
          }
        } else {
          if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val findActivity = context.findActivity() as? ComponentActivity
            findActivity?.let { activity ->
              activity.openNotificationSettings {
                notificationPermissionRequester.handleActivityResult(activity)
              }
            }
          }
        }
        onPop()
      }
    )
  }
}

@Preview
@Composable
private fun NotificationPermissionRequestDialogPreview() {
  AppTheme {
    NotificationPermissionRequestDialog({}, false, false)
  }
}