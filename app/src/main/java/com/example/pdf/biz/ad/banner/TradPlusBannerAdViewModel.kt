package com.example.pdf.biz.ad.banner

import androidx.lifecycle.ViewModel
import org.koin.android.annotation.KoinViewModel
import org.koin.core.annotation.InjectedParam

@KoinViewModel
class TradPlusBannerAdViewModel(
    @InjectedParam private val place: BannerAdPlace,
    private val tradPlusBannerAdManager: TradPlusBannerAdManager
) : ViewModel() {
  override fun onCleared() {
    tradPlusBannerAdManager.destroy(place)
    super.onCleared()
  }
}