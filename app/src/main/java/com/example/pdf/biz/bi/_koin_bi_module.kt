package com.example.pdf.biz.bi

import com.example.pdf.network.NetworkConf
import com.tencent.mmkv.MMKV
import de.jensklingenberg.ktorfit.Ktorfit
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.module


@Suppress("ObjectPropertyName")
val _koin_bi_module = module {
  singleOf(::BiDao)
  single(KTORFIT_BI) {
    buildBiKtorfit(NetworkConf.baseUrl.replace("sapi/", ""))
  }
  single {
    get<Ktorfit>(KTORFIT_BI).createBiApi()
  }
  singleOf(::BiReporter)
  singleOf(::BiPrefs)
  singleOf(::BiApiRemoteConfig)
  single { MMKV.defaultMMKV() }
}