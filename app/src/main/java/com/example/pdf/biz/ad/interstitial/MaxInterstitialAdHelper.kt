@file:Suppress("ObjectPropertyName")

package com.example.pdf.biz.ad.interstitial

import android.app.Activity
import android.content.Context
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdListener
import com.applovin.mediation.MaxAdRevenueListener
import com.applovin.mediation.MaxError
import com.applovin.mediation.ads.MaxInterstitialAd
import com.example.pdf.BuildConfig
import com.example.pdf.biz.BizConfig
import com.example.pdf.biz.SplashController
import com.example.pdf.biz.ad.FullscreenAdManager
import com.example.pdf.biz.ad.interstitial.ui.MaxInterstitialAdLoadingDialogNode
import com.example.pdf.biz.analytic.logEventApplovinMaxAdRevenue
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.biz.remoteconfig.AdConfig1
import com.example.pdf.biz.remoteconfig.InterAdConfig
import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import com.example.pdf.coroutine.AppCoroutineScope
import com.example.pdf.guia.GlobalNavigator
import com.example.pdf.kdatetime.nowInstant
import com.example.pdf.kermit.debugLog
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.currentKey
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.push
import com.roudikk.guia.extensions.removeAll
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.datetime.Instant
import org.koin.core.annotation.Single
import kotlin.time.DurationUnit
import kotlin.time.toDuration

private const val TAG = "MaxInterstitialAdHelper"

private val _adCacheDuration = 55.toDuration(DurationUnit.MINUTES)

sealed interface MaxInterstitialAdShowEvent {
  data object TryToShowing : MaxInterstitialAdShowEvent
  data object Showing : MaxInterstitialAdShowEvent
  data object Skip : MaxInterstitialAdShowEvent
  data object Hidden : MaxInterstitialAdShowEvent
}

@Single
class MaxInterstitialAdHelper(
  private val context: Context,
  private val splashController: SplashController,
  private val appCoroutineScope: AppCoroutineScope,
  private val remoteConfig: RealRemoteConfig,
  private val fullscreenAdManager: FullscreenAdManager
) {
  private val config: AdConfig1
    get() = remoteConfig.adConfig1

  private val adKey: String
    get() {
      return if (InterAdKeyController.useNextAdKey(config.next_inter_ad_key_active_interval_minutes)) {
        debugLog(tag = TAG) { "adKey use key2" }
        config.inter_ad_key2.ifEmpty { BizConfig.APPLOVIN_MAX_INTERSTITIAL_AD_2 }
      } else {
        debugLog(tag = TAG) { "adKey use key1" }
        config.inter_ad_key.ifEmpty { BizConfig.APPLOVIN_MAX_INTERSTITIAL_AD }
      }
    }

  private val interstitialAdConfig: InterAdConfig
    get() = config.let {
      InterAdConfig(
        showIntervalSeconds = it.inter_ad_show_interval_seconds,
        loadingTimeoutSeconds = it.inter_ad_loading_timeout_seconds
      )
    }

  private var _interstitialAd: MaxInterstitialAd? = null

  private val _lastCacheAdInstant = MutableStateFlow(Instant.fromEpochSeconds(0))
  private val _isLoadingAdFlow = MutableStateFlow(false)
  private val _currentActiveAdFrom = MutableStateFlow("")

  private val _interstitialAdEventChannel = MutableSharedFlow<MaxInterstitialAdShowEvent>(0)
  val interstitialAdEventFlow: SharedFlow<MaxInterstitialAdShowEvent> get() = _interstitialAdEventChannel

  private val _interstitialAdShowState = MutableStateFlow(false)
  val interstitialAdShowState get() = _interstitialAdShowState

  private val maxInterstitialAdListener = object : MaxAdListener, MaxAdRevenueListener {
    override fun onAdLoaded(ad: MaxAd) {
      debugLog("$TAG onAdLoaded")

      val now = nowInstant()

      _isLoadingAdFlow.update { false }
      _lastCacheAdInstant.update { now }
    }

    override fun onAdDisplayed(ad: MaxAd) {
      debugLog("$TAG onAdDisplayed")

      val now = nowInstant()

      splashController.skipSplash(true)

      fullscreenAdManager.latestShowAdSuccessInstant = now
      _interstitialAdShowState.update { true }

      appCoroutineScope.launch(Dispatchers.Main) {
        debugLog("ad_inter_impress")
        logEventRecord("ad_inter_impress")
        _interstitialAdEventChannel.emit(MaxInterstitialAdShowEvent.Showing)
      }
    }

    override fun onAdHidden(ad: MaxAd) {
      debugLog("$TAG onAdHidden")

      appCoroutineScope.launch(Dispatchers.Main) {
        _interstitialAdEventChannel.emit(MaxInterstitialAdShowEvent.Hidden)
        _interstitialAdShowState.update { false }
        debugLog("ad_inter_close")
        logEventRecord("ad_inter_close")

        _currentActiveAdFrom.update { "" }
        loadInterstitialAd()
      }
    }

    override fun onAdClicked(ad: MaxAd) {
      debugLog("$TAG onAdClicked")
      splashController.skipSplash(true)

      debugLog("ad_inter_click")
      logEventRecord("ad_inter_click")
    }

    override fun onAdLoadFailed(adUnitId: String, error: MaxError) {
      debugLog("$TAG onAdLoadFailed")

      appCoroutineScope.launch(Dispatchers.Main) {
        debugLog("ad_inter_load_failed")
        logEventRecord("ad_inter_load_failed")

        _currentActiveAdFrom.update { "" }
        _isLoadingAdFlow.update { false }
      }

    }

    override fun onAdDisplayFailed(ad: MaxAd, error: MaxError) {
      appCoroutineScope.launch(Dispatchers.Main) {
        debugLog("ad_inter_display_failed")
        logEventRecord("ad_inter_display_failed")
      }
    }

    override fun onAdRevenuePaid(ad: MaxAd) {
      debugLog("$TAG onAdRevenuePaid")

      if (!BuildConfig.DEBUG) {
        logEventApplovinMaxAdRevenue(ad)
      }
    }
  }


  fun initIfNeed() {
    if (_interstitialAd?.adUnitId != adKey) {
      _interstitialAd?.destroy()
      _interstitialAd = null
      _interstitialAd = MaxInterstitialAd(adKey).apply {
        setListener(maxInterstitialAdListener)
        setRevenueListener(maxInterstitialAdListener)
      }
      loadInterstitialAd(skipInitIfNeeded = true)
    }
  }

  private fun loadInterstitialAd(skipInitIfNeeded: Boolean = false) {
    if (skipInitIfNeeded.not()) {
      if (_interstitialAd?.adUnitId != adKey) {
        _interstitialAd?.destroy()
        _interstitialAd = null
        _interstitialAd = MaxInterstitialAd(adKey).apply {
          setListener(maxInterstitialAdListener)
          setRevenueListener(maxInterstitialAdListener)
        }
      }
    }

    _interstitialAd?.let {
      debugLog("$TAG _interstitialAd?.let{}")
      it.loadAd()
      _isLoadingAdFlow.update { true }
    }
  }

  private fun showInterstitialAd(activity: Activity) {
    _interstitialAd?.showAd(activity)
  }

  fun tryToLoadAd() {
    appCoroutineScope.launch(Dispatchers.Main) {
      val now = nowInstant()

      if (
        _interstitialAd?.isReady == true
        && now - _adCacheDuration < _lastCacheAdInstant.first()
      ) { // has available ad cache
        debugLog("loadAd has available ad cache")
      } else { // need load new ad
        val isLoadingAd = _isLoadingAdFlow.first()

        if (!isLoadingAd) {
          loadInterstitialAd()
        }
      }
    }
  }

  private var collectOnAdShowOrHiddenJob: Job? = null
  fun tryToShowAd(
    activity: Activity,
    from: String? = null,
    onAdShowingOrSkip: (Navigator.() -> Unit)? = null, // dialog use this
    onAdHiddenOrSkip: (Navigator.() -> Unit)? = null, // screen use this
  ) {
    debugLog("$TAG tryToShowAd from: $from")

    appCoroutineScope.launch(Dispatchers.Main.immediate) {
      collectOnAdShowOrHiddenJob?.cancel()
      collectOnAdShowOrHiddenJob = null

      GlobalNavigator.transaction { push(MaxInterstitialAdLoadingDialogNode()) }
      _interstitialAdEventChannel.emit(MaxInterstitialAdShowEvent.TryToShowing)

      val now = nowInstant()
      if (
        _interstitialAd?.isReady == true
        && now - _adCacheDuration < _lastCacheAdInstant.first()
      ) { // has available ad cache

        if (fullscreenAdManager.isAdShowTimeInShowInterval().not()) {
          delay(2_000)
          debugLog("ad_inter_show")
          logEventRecord("ad_inter_show")
          showInterstitialAd(activity)
        } else {
          tryToPopAdLoadingDialog()
          GlobalNavigator.transaction {
            onAdShowingOrSkip?.invoke(this)
            onAdHiddenOrSkip?.invoke(this)
          }

          debugLog("do not need show InterstitialAd. from: $from")
          _interstitialAdEventChannel.emit(MaxInterstitialAdShowEvent.Skip)
        }

      } else {
        tryToPopAdLoadingDialog()
        GlobalNavigator.transaction {
          onAdShowingOrSkip?.invoke(this)
          onAdHiddenOrSkip?.invoke(this)
        }

        _interstitialAdEventChannel.emit(MaxInterstitialAdShowEvent.Skip)

        val isLoadingAd = _isLoadingAdFlow.first()

        if (!isLoadingAd) {
          loadInterstitialAd()
        }
      }

      collectOnAdShowOrHiddenJob = appCoroutineScope.launch(Dispatchers.Default) {
        _interstitialAdEventChannel.take(2).collect {
          when (it) {
            MaxInterstitialAdShowEvent.Showing -> {
              tryToPopAdLoadingDialog()
              if (onAdShowingOrSkip != null) {
                GlobalNavigator.transaction {
                  onAdShowingOrSkip()
                }
              }
            }

            MaxInterstitialAdShowEvent.Hidden -> {
              tryToPopAdLoadingDialog()
              if (onAdHiddenOrSkip != null) {
                GlobalNavigator.transaction {
                  onAdHiddenOrSkip()
                }
              }
            }

            else -> {}
          }
        }
      }
    }
  }

  private suspend inline fun tryToPopAdLoadingDialog() {
    GlobalNavigator.transaction {
      if (currentKey is MaxInterstitialAdLoadingDialogNode) {
        pop()

        removeAll { it is MaxInterstitialAdLoadingDialogNode }
      }
    }
  }

  fun registerAutoNavUp(coroutineScope: CoroutineScope) {
    interstitialAdEventFlow.onEach { event ->
      if (event in arrayOf(MaxInterstitialAdShowEvent.Skip, MaxInterstitialAdShowEvent.Showing)) {
        GlobalNavigator.transaction {
          pop()
        }
      }
    }.launchIn(coroutineScope)
  }

  suspend fun instantLoadTimeoutDelay() {
    delay(interstitialAdConfig.loadingTimeoutSeconds.toDuration(DurationUnit.SECONDS))
  }
}