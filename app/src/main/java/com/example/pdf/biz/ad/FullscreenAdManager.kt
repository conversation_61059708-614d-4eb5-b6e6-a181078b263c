package com.example.pdf.biz.ad

import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import com.example.pdf.kdatetime.nowInstant
import com.tencent.mmkv.MMKV
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import org.koin.core.annotation.Single

@Single
class FullscreenAdManager(
  private val remoteConfig: RealRemoteConfig,
  private val mmkv: MMKV
) {
  companion object {
    private const val TAG = "FullscreenAdManager"
    private const val KEY_LATEST_SHOW_AD_SUCCESS_INSTANT = "${TAG}_latest_show_ad_success_instant"
  }

  private val adShowIntervalSeconds: Int
    get() {
      return if (remoteConfig.useLegacyAdConfig) {
        remoteConfig.adConfig3.fullscreen_ad_show_interval_seconds
      } else {
        remoteConfig.adConfig2.fullscreen_ad_show_interval_seconds
      }
    }

  var latestShowAdSuccessInstant: Instant
    get() = mmkv.decodeLong(KEY_LATEST_SHOW_AD_SUCCESS_INSTANT, 0L).let(Instant::fromEpochSeconds)
    set(value) {
      mmkv.encode(KEY_LATEST_SHOW_AD_SUCCESS_INSTANT, value.epochSeconds)
    }

  fun isAdShowTimeInShowInterval(): Boolean {
    return nowInstant().epochSeconds - adShowIntervalSeconds < latestShowAdSuccessInstant.epochSeconds
  }
}