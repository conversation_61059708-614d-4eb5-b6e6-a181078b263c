package com.example.pdf.biz.ad.interstitial

sealed interface InterstitialAdLoadingStateEvent {
  data object TimeOut : InterstitialAdLoadingStateEvent
  data object Loaded : InterstitialAdLoadingStateEvent
  data object FailedToLoad : InterstitialAdLoadingStateEvent
}

sealed interface InterstitialAdShowStateEvent {
  data object Finish : InterstitialAdShowStateEvent
  data object Showing : InterstitialAdShowStateEvent
  data object FailedToShow : InterstitialAdShowStateEvent
  data object SkipToShow : InterstitialAdShowStateEvent
}