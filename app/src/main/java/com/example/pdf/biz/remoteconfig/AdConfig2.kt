@file:Suppress("PropertyName")

package com.example.pdf.biz.remoteconfig

import com.example.pdf.biz.BizConfig
import kotlinx.serialization.EncodeDefault
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.Serializable

@OptIn(ExperimentalSerializationApi::class)
@Serializable
data class AdConfig2(
  @EncodeDefault val app_open_ad_key: String = BizConfig.ADMOB_AD_UNIT_ID_FOR_APP_OPEN,
  @EncodeDefault val banner_ad_key: String = BizConfig.ADMOB_AD_UNIT_ID_FOR_BANNER,
  @EncodeDefault val native_ad_key: String = BizConfig.ADMOB_AD_UNIT_ID_FOR_NATIVE,
  @EncodeDefault val inter_ad_key: String = BizConfig.ADMOB_AD_UNIT_ID_FOR_INTERSTITIAL,
  @EncodeDefault val inter_ad_key2: String = BizConfig.ADMOB_AD_UNIT_ID_FOR_INTERSTITIAL_2,
  @EncodeDefault val rewarded_ad_key: String = BizConfig.ADMOB_AD_UNIT_ID_FOR_REWARDED,

  @EncodeDefault val app_open_ad_loading_timeout_seconds: Int = 8,
  @EncodeDefault val inter_ad_loading_timeout_seconds: Int = 5,

  @EncodeDefault val fullscreen_ad_show_interval_seconds: Int = 60,

  @EncodeDefault val next_inter_ad_key_active_interval_minutes: Int = -1,
) {
  companion object {
    val Default = AdConfig2()

    //language=json
    val json = """
  {
    "app_open_ad_key": "ca-app-pub-6506371764244204/9265712668",
    "banner_ad_key": "ca-app-pub-6506371764244204/1423544373",
    "native_ad_key": "ca-app-pub-6506371764244204/9828648001",
    "inter_ad_key": "ca-app-pub-6506371764244204/8168253416",
    "inter_ad_key2": "ca-app-pub-6506371764244204/8168253416",
    "rewarded_ad_key": "ca-app-pub-6506371764244204/1578794335",
    "app_open_ad_loading_timeout_seconds": 8,
    "inter_ad_loading_timeout_seconds": 5,
    "fullscreen_ad_show_interval_seconds": 60,
    "next_inter_ad_key_active_interval_minutes": -1
  }
""".trimIndent()
  }
}