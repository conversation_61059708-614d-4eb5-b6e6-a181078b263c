package com.example.pdf.biz.ad.banner

import android.content.Context
import android.view.View
import android.view.ViewGroup
import com.example.pdf.biz.SplashController
import com.example.pdf.biz.analytic.AnalyticsLogEvent
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import com.example.pdf.coroutine.AppCoroutineScope
import com.example.pdf.kermit.debugLog
import com.tradplus.ads.base.bean.TPAdError
import com.tradplus.ads.base.bean.TPAdInfo
import com.tradplus.ads.open.banner.BannerAdListener
import com.tradplus.ads.open.banner.TPBanner
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Single

@Single
class TradPlusBannerAdManager(
  private val context: Context,
  private val appCoroutineScope: AppCoroutineScope,
  private val splashController: SplashController,
  private val remoteConfig: RealRemoteConfig,
) {
  @Suppress("PrivatePropertyName")
  private val TAG = "TradPlusBannerAdManager"
  private val adUnitNameLowercase = "banner"

  private val adKey
    get() = remoteConfig.adConfig3.banner_ad_key

  private val tpBannerFlowMap = mutableMapOf<BannerAdPlace, MutableStateFlow<TPBanner?>>()
  private val adStateFlowMap =
    mutableMapOf<BannerAdPlace, MutableStateFlow<TradPlusBannerAdState>>()

  fun getBannerFlow(adPlace: BannerAdPlace): MutableStateFlow<TPBanner?> {
    return tpBannerFlowMap.getOrPut(adPlace) {
      MutableStateFlow(null)
    }
  }

  fun getAdStateFlow(adPlace: BannerAdPlace): MutableStateFlow<TradPlusBannerAdState> {
    return adStateFlowMap.getOrPut(adPlace) {
      MutableStateFlow(TradPlusBannerAdState.Companion.Empty)
    }
  }

  fun buildAd(adPlace: BannerAdPlace) {
    debugLog(tag = TAG) { "buildAd for place: ${adPlace.name}" }

    val tpBanner = TPBanner(context)

    tpBanner.setAdListener(object : BannerAdListener() {
      override fun onAdLoaded(tpAdInfo: TPAdInfo?) {
        debugLog(tag = TAG) { "onAdLoaded: ${tpAdInfo?.adSourceName} for place: ${adPlace.name}" }

        appCoroutineScope.launch(Dispatchers.Main.immediate) {
//                    val bannerFlow = getBannerFlow(adPlace)
//                    val cacheTPBanner = bannerFlow.first()

//                    cacheTPBanner?.let {
//                        debugLog(tag = TAG) { "Destroying previous banner for place: ${adPlace.name}" }
//                        it.onDestroy()
//                    }

//                    bannerFlow.update { tpBanner }
          getAdStateFlow(adPlace).update { it.copy(isReady = true, isPause = false) }

          logEventRecord("ad_${adUnitNameLowercase}_load_success")
        }
      }

      override fun onAdClicked(tpAdInfo: TPAdInfo?) {
        debugLog(tag = TAG) { "onAdClicked: ${tpAdInfo?.adSourceName} for place: ${adPlace.name}" }
        splashController.skipSplash(true)
        logEventRecord("ad_${adUnitNameLowercase}_click")
      }

      override fun onAdImpression(tpAdInfo: TPAdInfo?) {
        debugLog(tag = TAG) { "onAdImpression: ${tpAdInfo?.adSourceName} for place: ${adPlace.name}" }

        tpAdInfo?.let { info ->
          val adFormat = "banner"

          AnalyticsLogEvent.tryToRecordTotalAdsRevenue001(info)
          AnalyticsLogEvent.recordAdImpressionRevenue(info, adFormat)
          AnalyticsLogEvent.recordAdImpression(info, adFormat)
          AnalyticsLogEvent.tenjinEventAdImpressionTradPlus(info)
        }

        logEventRecord("ad_${adUnitNameLowercase}_impress")
      }

      override fun onAdLoadFailed(tpAdError: TPAdError?) {
        debugLog(tag = TAG) { "onAdLoadFailed for place: ${adPlace.name}, code: ${tpAdError?.errorCode}, msg: ${tpAdError?.errorMsg}" }

        getAdStateFlow(adPlace).update { it.copy(isReady = false) }
      }

      override fun onAdShowFailed(tpAdError: TPAdError?, tpAdInfo: TPAdInfo?) {
        debugLog(tag = TAG) { "onAdShowFailed: ${tpAdInfo?.adSourceName} for place: ${adPlace.name}, error: ${tpAdError?.errorMsg}" }

        getAdStateFlow(adPlace).update { it.copy(isReady = false) }
      }

      override fun onAdClosed(tpAdInfo: TPAdInfo?) {
        debugLog(tag = TAG) { "onAdClosed: ${tpAdInfo?.adSourceName} for place: ${adPlace.name}" }
      }
    })

    val customParams = HashMap<String, Any>().apply {
      put("Admob_Adchoices", 1)
    }

    tpBanner.apply {
      setCustomParams(customParams)
      entryAdScenario("banner_${adPlace.named}")
      setAutoDestroy(false)
      loadAd(adKey)
    }

    getBannerFlow(adPlace).update { tpBanner }

    logEventRecord("ad_${adUnitNameLowercase}_load")
  }

  suspend fun pause(place: BannerAdPlace, delayMillis: Long = 200) =
    withContext(Dispatchers.Main.immediate) {
      delay(delayMillis)
      debugLog(tag = TAG) { "place:$place pause()" }

      val bannerState = getAdStateFlow(place).first()
      if (bannerState.isPause == false) {
        tpBannerFlowMap[place]?.first()?.let { tpBanner ->
          tpBanner.visibility = View.GONE
          debugLog(tag = TAG) { "place:$place invoke tpBanner pause()" }
          getAdStateFlow(place).update { it.copy(isPause = true) }
        }
      }
    }

  suspend fun resume(place: BannerAdPlace) = withContext(Dispatchers.Main.immediate) {
    debugLog(tag = TAG) { "place:$place resume()" }

    val bannerState = getAdStateFlow(place).first()
    if (bannerState.isPause == true) {
      tpBannerFlowMap[place]?.first()?.let { tpBanner ->
        tpBanner.visibility = View.VISIBLE
        debugLog(tag = TAG) { "place:$place invoke tpBanner resume()" }
        getAdStateFlow(place).update { it.copy(isPause = false) }
      }
    }
  }

  fun showBanner(place: BannerAdPlace, adContainer: ViewGroup) {
    debugLog(tag = TAG) { "showBanner for place: ${place.name}" }

    appCoroutineScope.launch(Dispatchers.Main.immediate) {
      tpBannerFlowMap[place]?.first()
        .apply {
          debugLog(tag = TAG) { "showBanner tpBanner: ${this.hashCode()}" }
        }
        ?.let { tpBanner ->
          runCatching {
            (tpBanner.parent as? ViewGroup)?.removeAllViews()
            adContainer.removeAllViews()
            adContainer.addView(tpBanner)
          }.onFailure { exception ->
            debugLog(tag = TAG) { "Failed to show banner: ${exception.message}" }
          }
        }
    }
  }

  fun destroyAll() {
    tpBannerFlowMap.keys.forEach(::destroy)
    adStateFlowMap.values.forEach {
      it.update { TradPlusBannerAdState.Companion.Empty }
    }
  }

  fun destroy(place: BannerAdPlace) {
    debugLog(tag = TAG) { "place:$place destroy()" }

    appCoroutineScope.launch(Dispatchers.Main.immediate) {
      tpBannerFlowMap[place]?.first()?.let { tpBanner ->
        debugLog(tag = TAG) { "place:$place invoke tpBanner destroy()" }
        tpBanner.onDestroy()
        getAdStateFlow(place).update { TradPlusBannerAdState.Companion.Empty }
      }
      tpBannerFlowMap[place]?.update { null }
    }
  }
}