package com.example.pdf.biz.ad.nat1ve

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.animation.Crossfade
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.viewinterop.AndroidViewBinding
import androidx.compose.ui.zIndex
import androidx.lifecycle.Lifecycle
import cafe.adriel.lyricist.LocalStrings
import com.airbnb.lottie.AsyncUpdates
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import com.example.pdf.android.context.findActivity
import com.example.pdf.android.lifecycle.OnLifecycleEvent
import com.example.pdf.android.toast.showToast
import com.example.pdf.biz.ad.AdColors
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.databinding.LayoutNativeAdContenWithoutMediaGBinding
import com.example.pdf.databinding.LayoutNativeAdContentGBinding
import com.example.pdf.kermit.debugLog
import com.example.pdf.lumo.AppTheme
import com.google.android.gms.ads.nativead.NativeAd
import org.koin.compose.koinInject

@Composable
fun AdmobNativeAd(
  place: NativeAdPlace,
  modifier: Modifier = Modifier,
) {
  val localContext = LocalContext.current

  val nativeAdManager: AdmobNativeAdManager = koinInject()

  val nativeAd by nativeAdManager.getAdFlow(place).collectAsState()

  debugLog(tag = "NATIVE_AD") { "nativeAd: $nativeAd" }

  var adContainer by remember { mutableStateOf<FrameLayout?>(null) }
  val nativeAdContentBinding by remember { mutableStateOf(localContext.nativeAdContentBinding()) }

  // todo: 对于 native ad 的创建/缓存/销毁，由于 Dialog 不会响应 OnLifecycleEvent，
  // todo: 所以目前使用多个 DialogPlace 对 Dialog 中的 native ad 进行 创建/缓存/销毁 的区分
  // todo: 如果需要使用同一个（唯一一个） DialogPlace 进行 创建/缓存/销毁 操作的话
  // todo: 可以从 destination style 入手 （比如改为 BottomSheetStyle，但依旧让“Dialog”展示在屏幕中间而不是底部
  // todo: 使用 BottomSheetStyle 的话，大概率不需要 DisposableEffect onDispose
  // todo: 简单自定义 BottomSheetStyle 方案： https://github.com/raamcosta/compose-destinations/issues/130

  if (!place.isDialogPlace()) {
    OnLifecycleEvent { _, event ->
      when (event) {
        Lifecycle.Event.ON_DESTROY -> {
          debugLog(tag = "NATIVE_AD") { "Lifecycle.Event.ON_DESTROY call destroy() place: ${place.name}" }

          nativeAdManager.destroy(place)
        }

        else -> {}
      }
    }
  } else {
    DisposableEffect(Unit) {
      onDispose {
        debugLog(tag = "NATIVE_AD") { "DisposableEffect onDispose call destroy() place: ${place.name}" }
        nativeAdManager.destroy(place)
      }
    }
  }


  LaunchedEffect(nativeAd) {
    nativeAd?.let {
      debugLog(tag = "NATIVE_AD") { "nativeAdContentBinding.configure(it)" }
      nativeAdContentBinding.configure(it)
      adContainer?.removeAllViews()
      adContainer?.addView(nativeAdContentBinding.root)
    }
  }

  LaunchedEffect(Unit) {
    logEventRecord("ad_native_show")

    if (nativeAd == null) {
      debugLog(tag = "NATIVE_AD") { "nativeAdManager.buildAd(place)" }
      nativeAdManager.buildAd(place)
    }
  }

  Box(modifier = Modifier.background(color = AdColors.backgroundColor)) {
    Crossfade(
      targetState = nativeAd != null,
      modifier = modifier
        .padding(8.dp)
        .apply {
          if (nativeAd == null) animateContentSize()
        },
      label = "",
    ) { hasNativeAd ->
      if (hasNativeAd) {
        Spacer(Modifier.height(138.dp))

        AndroidView(
          factory = { context ->
            FrameLayout(context).apply {
              layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
              )
            }.apply {
              adContainer = this
            }
          },
        )
      } else {
        NativeAdPlaceholder(
          modifier = Modifier
            .fillMaxWidth()
            .height(138.dp)
        )
      }
    }
  }
}


@Composable
fun AdmobNativeAdWithoutMedia(
  place: NativeAdPlace,
  modifier: Modifier = Modifier,
) {
  val localContext = LocalContext.current

  val nativeAdManager: AdmobNativeAdManager = koinInject()

  val nativeAd by nativeAdManager.getAdFlow(place).collectAsState()

  debugLog(tag = "NATIVE_AD") { "nativeAd: $nativeAd" }

  var adContainer by remember { mutableStateOf<FrameLayout?>(null) }
  val nativeAdContentBinding by remember { mutableStateOf(localContext.nativeAdContentWithoutMediaBinding()) }

  // todo: 对于 native ad 的创建/缓存/销毁，由于 Dialog 不会响应 OnLifecycleEvent，
  // todo: 所以目前使用多个 DialogPlace 对 Dialog 中的 native ad 进行 创建/缓存/销毁 的区分
  // todo: 如果需要使用同一个（唯一一个） DialogPlace 进行 创建/缓存/销毁 操作的话
  // todo: 可以从 destination style 入手 （比如改为 BottomSheetStyle，但依旧让“Dialog”展示在屏幕中间而不是底部
  // todo: 使用 BottomSheetStyle 的话，大概率不需要 DisposableEffect onDispose
  // todo: 简单自定义 BottomSheetStyle 方案： https://github.com/raamcosta/compose-destinations/issues/130

  if (!place.isDialogPlace()) {
    OnLifecycleEvent { _, event ->
      when (event) {
        Lifecycle.Event.ON_DESTROY -> {
          debugLog(tag = "NATIVE_AD") { "Lifecycle.Event.ON_DESTROY call destroy() place: ${place.name}" }

          nativeAdManager.destroy(place)
        }

        else -> {}
      }
    }
  } else {
    DisposableEffect(Unit) {
      onDispose {
        debugLog(tag = "NATIVE_AD") { "DisposableEffect onDispose call destroy() place: ${place.name}" }
        nativeAdManager.destroy(place)
      }
    }
  }


  LaunchedEffect(nativeAd) {
    nativeAd?.let {
      debugLog(tag = "NATIVE_AD") { "nativeAdContentBinding.configure(it)" }
      nativeAdContentBinding.configure(it)
      adContainer?.removeAllViews()
      adContainer?.addView(nativeAdContentBinding.root)
    }
  }

  LaunchedEffect(Unit) {
    logEventRecord("ad_native_show")

    if (nativeAd == null) {
      debugLog(tag = "NATIVE_AD") { "nativeAdManager.buildAd(place)" }
      nativeAdManager.buildAd(place)
    }
  }

  Box(modifier = modifier.background(color = AdColors.backgroundColor)) {
    Crossfade(
      targetState = nativeAd != null,
      modifier = Modifier
        .apply {
          if (nativeAd == null) animateContentSize()
        },
    ) { hasNativeAd ->
      if (hasNativeAd) {
        AndroidView(
          factory = { context ->
            FrameLayout(context).apply {
              layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
              )
            }.apply {
              adContainer = this
            }
          },
          modifier = Modifier
            .fillMaxWidth()
            .height(72.dp)
        )
      } else {
        NativeAdWithoutMediaPlaceholder(
          modifier = Modifier
            .fillMaxWidth()
            .height(72.dp)
        )
      }
    }
  }
}

private fun Context.nativeAdContentBinding(): LayoutNativeAdContentGBinding {
  val layoutInflater = this.findActivity().layoutInflater

  return LayoutNativeAdContentGBinding.inflate(layoutInflater)
}

private fun Context.nativeAdContentWithoutMediaBinding(): LayoutNativeAdContenWithoutMediaGBinding {
  val layoutInflater = this.findActivity().layoutInflater

  return LayoutNativeAdContenWithoutMediaGBinding.inflate(layoutInflater)
}

private fun LayoutNativeAdContentGBinding.configure(nativeAd: NativeAd) {
  val nativeAdView = this.nativeAd

  nativeAdView.mediaView = this.adMedia
  nativeAdView.headlineView = this.adHeadline
  nativeAdView.bodyView = this.adBody
  nativeAdView.callToActionView = this.adCallToAction
  nativeAdView.iconView = this.adAppIcon
  nativeAdView.advertiserView = this.adAdvertiser

  this.adHeadline.text = nativeAd.headline
  nativeAd.mediaContent?.let { this.adMedia.mediaContent = it }
  if (nativeAd.body == null) {
    debugLog(tag = "NATIVE_AD") { "this.adBody.visibility = View.INVISIBLE" }

    this.adBody.visibility = View.INVISIBLE
  } else {
    this.adBody.visibility = View.VISIBLE
    this.adBody.text = nativeAd.body
  }

  if (nativeAd.callToAction == null) {
    debugLog(tag = "NATIVE_AD") { "this.adCallToAction.visibility = View.INVISIBLE" }

    this.adCallToAction.visibility = View.INVISIBLE
  } else {
    this.adCallToAction.visibility = View.VISIBLE
    this.adCallToAction.text = nativeAd.callToAction
  }

  if (nativeAd.icon == null) {
    debugLog(tag = "NATIVE_AD") { "this.adAppIcon.visibility = View.INVISIBLE" }

    this.adAppIcon.visibility = View.INVISIBLE
  } else {
    this.adAppIcon.setImageDrawable(nativeAd.icon?.drawable)
    this.adAppIcon.visibility = View.VISIBLE
  }

  if (nativeAd.advertiser == null) {
    debugLog(tag = "NATIVE_AD") { "this.adAdvertiser.visibility = View.INVISIBLE" }

    this.adAdvertiser.visibility = View.INVISIBLE
  } else {
    this.adAdvertiser.text = nativeAd.advertiser
    this.adAdvertiser.visibility = View.VISIBLE
  }

  nativeAdView.setNativeAd(nativeAd)
}

private fun LayoutNativeAdContenWithoutMediaGBinding.configure(nativeAd: NativeAd) {
  val nativeAdView = this.nativeAd

  nativeAdView.mediaView = null
  nativeAdView.headlineView = this.adHeadline
  nativeAdView.bodyView = this.adBody
  nativeAdView.callToActionView = this.adCallToAction
  nativeAdView.iconView = this.adAppIcon
  nativeAdView.advertiserView = this.adAdvertiser

  this.adHeadline.text = nativeAd.headline
//  nativeAd.mediaContent?.let { this.adMedia.mediaContent = it }
  if (nativeAd.body == null) {
    debugLog(tag = "NATIVE_AD") { "this.adBody.visibility = View.INVISIBLE" }

    this.adBody.visibility = View.INVISIBLE
  } else {
    this.adBody.visibility = View.VISIBLE
    this.adBody.text = nativeAd.body
  }

  if (nativeAd.callToAction == null) {
    debugLog(tag = "NATIVE_AD") { "this.adCallToAction.visibility = View.INVISIBLE" }

    this.adCallToAction.visibility = View.INVISIBLE
  } else {
    this.adCallToAction.visibility = View.VISIBLE
    this.adCallToAction.text = nativeAd.callToAction
  }

  if (nativeAd.icon == null) {
    debugLog(tag = "NATIVE_AD") { "this.adAppIcon.visibility = View.INVISIBLE" }

    this.adAppIcon.visibility = View.INVISIBLE
  } else {
    this.adAppIcon.setImageDrawable(nativeAd.icon?.drawable)
    this.adAppIcon.visibility = View.VISIBLE
  }

  if (nativeAd.advertiser == null) {
    debugLog(tag = "NATIVE_AD") { "this.adAdvertiser.visibility = View.INVISIBLE" }

    this.adAdvertiser.visibility = View.INVISIBLE
  } else {
    this.adAdvertiser.text = nativeAd.advertiser
    this.adAdvertiser.visibility = View.VISIBLE
  }

  nativeAdView.setNativeAd(nativeAd)
}

@Preview
@Composable
private fun AdmobNativeAdPreview() {
  AppTheme {
    AdmobNativeAd(place = NativeAdPlace.Test)
  }
}
