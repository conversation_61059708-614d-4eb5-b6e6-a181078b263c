package com.example.pdf.biz.ad.nat1ve

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import org.koin.compose.koinInject

@Composable
fun NativeAd(
  adPlace: NativeAdPlace,
  modifier: Modifier = Modifier,
  remoteConfig: RealRemoteConfig = koinInject()
) {
  val useLegacyAdConfig = remoteConfig.rememberUseLegacyAdConfig()

  if (useLegacyAdConfig) {
    TradPlusNativeAd(
      place = adPlace,
      modifier = modifier,
      isWithoutMedia = true
    )
  } else {
    AdmobNativeAdWithoutMedia(
      place = adPlace,
      modifier = modifier
    )
  }
}