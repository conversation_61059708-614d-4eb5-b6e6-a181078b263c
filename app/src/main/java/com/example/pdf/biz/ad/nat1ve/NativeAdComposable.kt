package com.example.pdf.biz.ad.nat1ve

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import org.koin.compose.koinInject

@Composable
fun NativeAd(
  adPlace: NativeAdPlace,
  modifier: Modifier = Modifier,
  remoteConfig: RealRemoteConfig = koinInject()
) {
  val useGhostAdProvider = remoteConfig.rememberUseGhostAdProvider()
  val useLegacyAdConfig = remoteConfig.rememberUseLegacyAdConfig()

  when {
    useGhostAdProvider -> {
      GhostNativeAd(
        place = adPlace,
        modifier = modifier,
        isWithoutMedia = true
      )
    }
    useLegacyAdConfig -> {
      TradPlusNativeAd(
        place = adPlace,
        modifier = modifier,
        isWithoutMedia = true
      )
    }
    else -> {
      AdmobNativeAdWithoutMedia(
        place = adPlace,
        modifier = modifier
      )
    }
  }
}