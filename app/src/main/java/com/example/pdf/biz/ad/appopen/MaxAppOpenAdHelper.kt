@file:Suppress("ObjectPropertyName")

package com.example.pdf.biz.ad.appopen

import android.content.Context
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdListener
import com.applovin.mediation.MaxAdRevenueListener
import com.applovin.mediation.MaxError
import com.applovin.mediation.ads.MaxAppOpenAd
import com.example.pdf.activityStack
import com.example.pdf.biz.BizConfig
import com.example.pdf.biz.SplashController
import com.example.pdf.biz.ad.FullscreenAdManager
import com.example.pdf.biz.analytic.logEventApplovinMaxAdRevenue
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.biz.remoteconfig.AppOpenAdConfig
import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import com.example.pdf.coroutine.AppCoroutineScope
import com.example.pdf.flow.EventFlow
import com.example.pdf.flow.send
import com.example.pdf.flow.sendBlock
import com.example.pdf.kdatetime.nowInstant
import com.example.pdf.kermit.debugLog
import com.example.pdf.top
import com.example.pdf.ui.activity.SplashActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.datetime.Instant
import org.koin.core.annotation.Single
import java.util.UUID
import kotlin.time.DurationUnit
import kotlin.time.toDuration

data class CloseSplashEvent(val eventId: String = UUID.randomUUID().toString())

@Suppress("ObjectPropertyName")
internal val _closeSplashEventFlow: EventFlow<CloseSplashEvent> = EventFlow()

val closeSplashEventFlow: SharedFlow<CloseSplashEvent> get() = _closeSplashEventFlow

private const val TAG = "MaxAppOpenAdHelper"


sealed interface TryToShowExecuteResult {
  data object DoNotShow : TryToShowExecuteResult
  data object ReadyToShow : TryToShowExecuteResult
  data object Showing : TryToShowExecuteResult
  data object ShowFinish : TryToShowExecuteResult
  data object Error : TryToShowExecuteResult
}

@Single
class MaxAppOpenAdHelper(
  private val context: Context,
  private val splashController: SplashController,
  private val appCoroutineScope: AppCoroutineScope,
  private val remoteConfig: RealRemoteConfig,
  private val fullscreenAdManager: FullscreenAdManager
) {
  private val adKey
    get() = remoteConfig.adConfig1.app_open_ad_key.ifEmpty { BizConfig.APPLOVIN_MAX_APP_OPEN_AD }

  private val appOpenAdConfig: AppOpenAdConfig
    get() = remoteConfig.adConfig1.let {
      AppOpenAdConfig(
        it.app_open_ad_show_interval_seconds,
        it.app_open_ad_loading_timeout_seconds
      )
    }

  private val _adCacheDuration = 55.toDuration(DurationUnit.MINUTES)

  private var _appOpenAd: MaxAppOpenAd? = null

  private val _lastCacheAdInstant = MutableStateFlow(Instant.fromEpochSeconds(0))
  private val _isLoadingAdFlow = MutableStateFlow(false)

  val tryToShowExecuteResultEventFlow = EventFlow<TryToShowExecuteResult?>()

  private val maxAdOpenAdListener = object : MaxAdListener, MaxAdRevenueListener {
    override fun onAdLoaded(p0: MaxAd) {
      debugLog("$TAG maxAdOpenAdListener onAdLoaded")
      _isLoadingAdFlow.update { false }
      _closeSplashEventFlow.sendBlock {
        CloseSplashEvent().apply {
          debugLog("loadAd send closeSplashEvent when onAdLoaded(): $this")
        }
      }
      _lastCacheAdInstant.update { nowInstant() }
    }

    override fun onAdDisplayed(p0: MaxAd) {
      debugLog("$TAG maxAdOpenAdListener onAdDisplayed")

      fullscreenAdManager.latestShowAdSuccessInstant = nowInstant()

      tryToShowExecuteResultEventFlow.send(TryToShowExecuteResult.Showing)
      splashController.skipSplash(true)

      debugLog("ad_app_open_impress")
      logEventRecord("ad_app_open_impress")
    }

    override fun onAdHidden(p0: MaxAd) {
      debugLog("$TAG maxAdOpenAdListener onAdHidden")

      debugLog("ad_app_open_close")
      logEventRecord("ad_app_open_close")

      tryToShowExecuteResultEventFlow.send(TryToShowExecuteResult.ShowFinish)

      loadAppOpenAd()
    }

    override fun onAdClicked(p0: MaxAd) {
      debugLog("ad_app_open_click")
      logEventRecord("ad_app_open_click")
      splashController.skipSplash(true)
    }

    override fun onAdLoadFailed(p0: String, p1: MaxError) {
      debugLog("$TAG maxAdOpenAdListener onAdLoadFailed: $p1")

      debugLog("ad_app_open_load_failed")
      logEventRecord("ad_app_open_load_failed")

      _isLoadingAdFlow.update { false }
    }

    override fun onAdDisplayFailed(p0: MaxAd, p1: MaxError) {
      debugLog("$TAG maxAdOpenAdListener onAdDisplayFailed")

      debugLog("ad_app_open_display_failed")
      logEventRecord("ad_app_open_display_failed")
      tryToShowExecuteResultEventFlow.send(TryToShowExecuteResult.Error)

    }

    override fun onAdRevenuePaid(ad: MaxAd) {
      logEventApplovinMaxAdRevenue(ad)
    }

  }

  fun initIfNeed() {
    if (_appOpenAd?.adUnitId != adKey) {
      _appOpenAd?.destroy()
      _appOpenAd = null
      _appOpenAd = MaxAppOpenAd(adKey).apply {
        setListener(maxAdOpenAdListener)
        setRevenueListener(maxAdOpenAdListener)
      }
      loadAppOpenAd(skipInitIfNeeded = true)
    }
  }

  private fun loadAppOpenAd(skipInitIfNeeded: Boolean = false) {
    if (skipInitIfNeeded.not()) {
      if (_appOpenAd?.adUnitId != adKey) {
        _appOpenAd?.destroy()
        _appOpenAd = null
        _appOpenAd = MaxAppOpenAd(adKey).apply {
          setListener(maxAdOpenAdListener)
          setRevenueListener(maxAdOpenAdListener)
        }
      }
    }

    _appOpenAd?.let {
      debugLog("$TAG loadAppOpenAd()")
      it.loadAd()
      _isLoadingAdFlow.update { true }
    }
  }

  private fun showAppOpenAd() {
    _appOpenAd?.showAd()
    debugLog("$TAG _appOpenAd?.showAd()")
  }

  fun tryToLoadAd() {
    debugLog("$TAG tryToLoadAd()")

    appCoroutineScope.launch(Dispatchers.Main.immediate) {
      val now = nowInstant()

      if (
        _appOpenAd?.isReady == true
        && now - _adCacheDuration < _lastCacheAdInstant.first()
      ) { // has available ad cache
        debugLog("$TAG tryToLoadAd() has available ad cache")

        _closeSplashEventFlow.sendBlock {
          CloseSplashEvent().apply {
            debugLog("loadAd send closeSplashEvent when has ad cache: $this")
          }
        }
      } else {
        val isLoadingAd = _isLoadingAdFlow.first()
        debugLog("$TAG tryToLoadAd() isLoadingAd: $isLoadingAd")

        if (!isLoadingAd) {
          loadAppOpenAd()
        }

        launch {
          instantLoadTimeoutDelay()
          if (_appOpenAd?.isReady != true) {
            _closeSplashEventFlow.sendBlock {
              CloseSplashEvent().apply {
                debugLog("loadAd send closeSplashEvent when timeout: $this")
              }
            }
          }
        }
      }
    }
  }

  fun tryToShowAd() {
    debugLog("$TAG tryToShowAd()")
    appCoroutineScope.launch(Dispatchers.Main.immediate) {
      debugLog("ad_app_open_show")
      logEventRecord("ad_app_open_show")

      val now = nowInstant()

      if (
        _appOpenAd?.isReady == true
        && fullscreenAdManager.isAdShowTimeInShowInterval().not()
        && now - _adCacheDuration < _lastCacheAdInstant.first()
      ) {
        debugLog("$TAG cache available invoke showAppOpenAd()")
        tryToShowExecuteResultEventFlow.send(TryToShowExecuteResult.ReadyToShow)
        delay(2_000)

        if (activityStack.top() is SplashActivity) {
          showAppOpenAd()
        }
      } else {
        tryToShowExecuteResultEventFlow.send(TryToShowExecuteResult.DoNotShow)

        val isLoadingAd = _isLoadingAdFlow.first()
        debugLog("$TAG cache unavailable isLoadingAd: $isLoadingAd")
        if (!isLoadingAd) {
          loadAppOpenAd()
        }
      }
    }
  }

  suspend fun instantLoadTimeoutDelay() {
    delay(appOpenAdConfig.loadingTimeoutSeconds.toDuration(DurationUnit.SECONDS))
  }
}
