package com.example.pdf.biz.ad.appopen

import android.app.Activity
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.flow.EventFlow
import com.example.pdf.flow.send
import com.example.pdf.kermit.debugLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Single

@Single
class GhostAppOpenAdManager {
  @Suppress("PrivatePropertyName")
  private val TAG = "GhostAppOpenAdManager"
  private val adUnitNameLowercase = "app_open"

  val adLoadingStateEventFlow = EventFlow<AppOpenAdLoadingStateEvent>()
  val adShowStateEventFlow = EventFlow<AppOpenAdShowStateEvent>()

  fun tryToLoadAd(activity: Activity) {
    debugLog(tag = TAG) { "tryToLoadAd (Ghost - no-op)" }
    
    // Log the load event to maintain analytics consistency
    logEventRecord("ad_${adUnitNameLowercase}_load")
    
    // Immediately send FailedToLoad event since we don't actually load ads
    adLoadingStateEventFlow.send(AppOpenAdLoadingStateEvent.FailedToLoad)
  }

  fun loadAd(activity: Activity) {
    debugLog(tag = TAG) { "loadAd (Ghost - no-op)" }
    
    // Log the load event to maintain analytics consistency
    logEventRecord("ad_${adUnitNameLowercase}_load")
    
    // Immediately send FailedToLoad event since we don't actually load ads
    adLoadingStateEventFlow.send(AppOpenAdLoadingStateEvent.FailedToLoad)
  }

  suspend fun tryToShowAd(
    activity: Activity,
    immediate: Boolean = false
  ) = withContext(Dispatchers.Main.immediate) {
    debugLog(tag = TAG) { "tryToShowAd (Ghost - no-op)" }

    // Always skip showing ads since this is a no-op provider
    adShowStateEventFlow.send(AppOpenAdShowStateEvent.SkipToShow)
  }

  fun showAd(activity: Activity) {
    debugLog(tag = TAG) { "showAd (Ghost - no-op)" }
    
    // Immediately send SkipToShow event since we don't actually show ads
    adShowStateEventFlow.send(AppOpenAdShowStateEvent.SkipToShow)
  }

  private fun isAdAvailable(): Boolean {
    // Always return false since we never have ads available
    return false
  }

  fun onDestroy() {
    debugLog(tag = TAG) { "onDestroy (Ghost - no-op)" }
    // Nothing to clean up for Ghost provider
  }
}
