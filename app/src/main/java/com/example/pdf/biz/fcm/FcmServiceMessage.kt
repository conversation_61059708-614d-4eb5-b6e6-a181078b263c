package com.example.pdf.biz.fcm

import androidx.annotation.DrawableRes
import androidx.compose.ui.graphics.Color
import com.example.pdf.R
import com.example.pdf.guia.ScreenNode
import com.example.pdf.ui.node.home.HomeNode
import com.example.pdf.ui.node.home.HomeNodeAction
import com.example.pdf.ui.node.home.HomeTab
import com.example.pdf.ui.node.pdf_picker.AddSignatureToPdfPickerNode
import com.example.pdf.ui.node.pdf_picker.AnnotatePdfPickerNode
import com.example.pdf.ui.node.pdf_picker.LockPdfPickerNode
import com.example.pdf.ui.node.pdf_picker.PdfPickerListType
import kotlinx.serialization.EncodeDefault
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlin.text.toString

private fun String.hexToLong(): Long {
  return this.removePrefix("0x").removePrefix("0X").toULong(16).toLong()
}

private fun ULong.toHexString(prefix: Boolean = true): String {
  val hexValue = this.toString(16).uppercase()
  return if (prefix) "0x$hexValue" else hexValue
}

@Suppress("PropertyName")
@Serializable
data class FcmServiceMessage(
  @EncodeDefault val id: Int = 0,
  @EncodeDefault val title: String = "",
  @EncodeDefault val title_color: String = defaultTitleColor.value.toHexString(),
  @EncodeDefault val title_color_darkmode: String = defaultTitleColorDarkMode.value.toHexString(),
  @EncodeDefault val content: String = "",
  @EncodeDefault val content_color: String = defaultContentColor.value.toHexString(),
  @EncodeDefault val content_color_darkmode: String = defaultContentColorDarkMode.value.toHexString(),
  @EncodeDefault val destination: ClickActionDestination = ClickActionDestination.All,
  @EncodeDefault val type: String = "",
  @EncodeDefault val image_index: Int = 0,
  @EncodeDefault val additional_push_count: Int = 0,
) {
  val destinationNode: ScreenNode
    get() = when (destination) {
      ClickActionDestination.All -> HomeNode(tab = HomeTab.ALL)
      ClickActionDestination.Tools -> HomeNode(tab = HomeTab.TOOLS)
      ClickActionDestination.Bookmarks -> HomeNode(tab = HomeTab.BOOKMARKS)
      ClickActionDestination.ScanToPdf -> HomeNode(tab = HomeTab.ALL, action = HomeNodeAction.Scan2Pdf)
      ClickActionDestination.AnnotatePdf -> AnnotatePdfPickerNode()
      ClickActionDestination.LockPdf -> LockPdfPickerNode()
      ClickActionDestination.AddSignatureToPdf -> AddSignatureToPdfPickerNode()
    }

  @get:DrawableRes
  val imgRes: Int
    get() = when (image_index) {
      0 -> R.drawable.img_fcm_service_message_0
      1 -> R.drawable.img_fcm_service_message_1
      2 -> R.drawable.img_fcm_service_message_2
      3 -> R.drawable.img_fcm_service_message_3
      4 -> R.drawable.img_fcm_service_message_4
      5 -> R.drawable.img_fcm_service_message_5
      6 -> R.drawable.img_fcm_service_message_6
      7 -> R.drawable.img_fcm_service_message_7
      else -> R.drawable.img_fcm_service_message_0
    }

  val titleColor: Color
    get() = try {
      Color(title_color.hexToLong())
    } catch (e: Exception) {
      defaultTitleColor
    }

  val titleColorDarkMode: Color
    get() = try {
      Color(title_color_darkmode.hexToLong())
    } catch (e: Exception) {
      defaultTitleColorDarkMode
    }

  val contentColor: Color
    get() = try {
      Color(content_color.hexToLong())
    } catch (e: Exception) {
      defaultContentColor
    }

  val contentColorDarkMode: Color
    get() = try {
      Color(content_color_darkmode.hexToLong())
    } catch (e: Exception) {
      defaultContentColorDarkMode
    }

  fun isEmptyMessage(): Boolean {
    return title.trim().isEmpty() && content.trim().isEmpty()
  }


  //signature
  @Serializable
  enum class ClickActionDestination {
    @SerialName("all")
    All,

    @SerialName("tool")
    Tools,

    @SerialName("bookmark")
    Bookmarks,

    @SerialName("scan")
    ScanToPdf,

    @SerialName("annotate")
    AnnotatePdf,

    @SerialName("lock")
    LockPdf,

    @SerialName("signature")
    AddSignatureToPdf,
  }

  companion object {
    val defaultTitleColor = Color.Black
    val defaultTitleColorDarkMode = Color.White
    val defaultContentColor = Color.Gray
    val defaultContentColorDarkMode = Color.LightGray
  }
}


