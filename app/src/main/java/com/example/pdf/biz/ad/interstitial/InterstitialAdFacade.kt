package com.example.pdf.biz.ad.interstitial

import android.app.Activity
import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import com.example.pdf.flow.EventFlow
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.merge
import org.koin.core.annotation.Single

@Single
class InterstitialAdFacade(
  private val admobInterstitialAdManager: AdmobInterstitialAdManager,
  private val tradPlusInterstitialAdManager: TradPlusInterstitialAdManager,
  private val remoteConfig: RealRemoteConfig,
) {

  fun tryToLoadAd(activity: Activity) {
    when (remoteConfig.useLegacyAdConfig) {
      true -> tradPlusInterstitialAdManager.tryToLoadAd(activity)
      false -> admobInterstitialAdManager.tryToLoadAd(activity)
    }
  }

  val adLoadingStateEventFlow: EventFlow<InterstitialAdLoadingStateEvent>
    get() = when (remoteConfig.useLegacyAdConfig) {
      true -> tradPlusInterstitialAdManager.adLoadingStateEventFlow
      false -> admobInterstitialAdManager.adLoadingStateEventFlow
    }

  val adShowStateEventFlow: EventFlow<InterstitialAdShowStateEvent>
    get() = when (remoteConfig.useLegacyAdConfig) {
      true -> tradPlusInterstitialAdManager.adShowStateEventFlow
      false -> admobInterstitialAdManager.adShowStateEventFlow
    }

  val adShowStateFlow: MutableStateFlow<Boolean>
    get() = when (remoteConfig.useLegacyAdConfig) {
      true -> tradPlusInterstitialAdManager.adShowStateFlow
      false -> admobInterstitialAdManager.adShowStateFlow
    }

  val mergedAdLoadingStateEventFlow: Flow<InterstitialAdLoadingStateEvent>
    get() = merge(
      admobInterstitialAdManager.adLoadingStateEventFlow,
      tradPlusInterstitialAdManager.adLoadingStateEventFlow
    )

  val mergedAdShowStateEventFlow: Flow<InterstitialAdShowStateEvent>
    get() = merge(
      admobInterstitialAdManager.adShowStateEventFlow,
      tradPlusInterstitialAdManager.adShowStateEventFlow
    )

  fun onDestroy() {
    tradPlusInterstitialAdManager.onDestroy()
  }
}