package com.example.pdf.biz.ad.interstitial

import android.app.Activity
import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import com.example.pdf.flow.EventFlow
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.merge
import org.koin.core.annotation.Single

@Single
class InterstitialAdFacade(
  private val admobInterstitialAdManager: AdmobInterstitialAdManager,
  private val tradPlusInterstitialAdManager: TradPlusInterstitialAdManager,
  private val ghostInterstitialAdManager: GhostInterstitialAdManager,
  private val remoteConfig: RealRemoteConfig,
) {

  fun tryToLoadAd(activity: Activity) {
    when {
      remoteConfig.useGhostAdProvider -> ghostInterstitialAdManager.tryToLoadAd(activity)
      remoteConfig.useLegacyAdConfig -> tradPlusInterstitialAdManager.tryToLoadAd(activity)
      else -> admobInterstitialAdManager.tryToLoadAd(activity)
    }
  }

  suspend fun tryToShowAd(
    activity: Activity,
    adPlaceName: String? = null,
    immediate: Boolean = false,
    onReadyShowAd: (() -> Unit)? = null
  ) {
    when {
      remoteConfig.useGhostAdProvider -> ghostInterstitialAdManager.tryToShowAd(activity, adPlaceName, immediate, onReadyShowAd)
      remoteConfig.useLegacyAdConfig -> tradPlusInterstitialAdManager.tryToShowAd(activity, adPlaceName, immediate, onReadyShowAd)
      else -> admobInterstitialAdManager.tryToShowAd(activity, adPlaceName, immediate, onReadyShowAd)
    }
  }

  val adLoadingStateEventFlow: EventFlow<InterstitialAdLoadingStateEvent>
    get() = when {
      remoteConfig.useGhostAdProvider -> ghostInterstitialAdManager.adLoadingStateEventFlow
      remoteConfig.useLegacyAdConfig -> tradPlusInterstitialAdManager.adLoadingStateEventFlow
      else -> admobInterstitialAdManager.adLoadingStateEventFlow
    }

  val adShowStateEventFlow: EventFlow<InterstitialAdShowStateEvent>
    get() = when {
      remoteConfig.useGhostAdProvider -> ghostInterstitialAdManager.adShowStateEventFlow
      remoteConfig.useLegacyAdConfig -> tradPlusInterstitialAdManager.adShowStateEventFlow
      else -> admobInterstitialAdManager.adShowStateEventFlow
    }

  val adShowStateFlow: MutableStateFlow<Boolean>
    get() = when {
      remoteConfig.useGhostAdProvider -> ghostInterstitialAdManager.adShowStateFlow
      remoteConfig.useLegacyAdConfig -> tradPlusInterstitialAdManager.adShowStateFlow
      else -> admobInterstitialAdManager.adShowStateFlow
    }

  val mergedAdLoadingStateEventFlow: Flow<InterstitialAdLoadingStateEvent>
    get() = merge(
      admobInterstitialAdManager.adLoadingStateEventFlow,
      tradPlusInterstitialAdManager.adLoadingStateEventFlow,
      ghostInterstitialAdManager.adLoadingStateEventFlow
    )

  val mergedAdShowStateEventFlow: Flow<InterstitialAdShowStateEvent>
    get() = merge(
      admobInterstitialAdManager.adShowStateEventFlow,
      tradPlusInterstitialAdManager.adShowStateEventFlow,
      ghostInterstitialAdManager.adShowStateEventFlow
    )

  fun onDestroy() {
    tradPlusInterstitialAdManager.onDestroy()
    ghostInterstitialAdManager.onDestroy()
  }
}