package com.example.pdf.biz.analytic

import android.content.Context
import com.applovin.mediation.MaxAd
import com.example.pdf.appContext
import com.example.pdf.biz.BizConfig
import com.example.pdf.coroutine.AppCoroutineScope
import com.example.pdf.kdatetime.nowInstant
import com.example.pdf.kdatetime.todayStartInstant
import com.example.pdf.kermit.debugLog
import com.google.android.gms.ads.AdValue
import com.tenjin.android.TenjinSDK
import com.tradplus.ads.base.bean.TPAdInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.datetime.TimeZone
import org.koin.core.annotation.Single

@Single
class TenjinHelper(
  private val analyticPrefs: AnalyticPrefs,
  private val appCoroutineScope: AppCoroutineScope,
) {

  private var _tenjin: TenjinSDK? = null

  private fun getTenjin(): TenjinSDK? {
    return if (_tenjin == null) {
      val tenjinInstance = TenjinSDK.getInstance(appContext, BizConfig.TENJIN_SDK_KEY).apply {
        setCacheEventSetting(true)
      }
      _tenjin = tenjinInstance
      tenjinInstance
    } else {
      _tenjin
    }
  }

  fun init(context: Context) {
    appCoroutineScope.launch(Dispatchers.Main.immediate) {
      val tz = TimeZone.UTC

      val latestTenjinInitSuccessInstant =
        analyticPrefs.latestTenjinInitSuccessInstant

      val nowInstant = nowInstant()

      if (latestTenjinInitSuccessInstant
          .todayStartInstant(tz)
          .epochSeconds == nowInstant.todayStartInstant(tz).epochSeconds
      ) {
        // 和上次初始化相比还在同一天内, 不需要触发初始化
        debugLog { "$TAG do not need to do anything" }
      } else {
        initInternal(context)
        analyticPrefs.storeTenjinInitSuccessInstant(nowInstant)
      }
    }
  }

  private fun initInternal(context: Context): TenjinSDK? {
    debugLog { "$TAG init" }

    return TenjinSDK.getInstance(
      context.applicationContext ?: appContext,
      BizConfig.TENJIN_SDK_KEY
    )?.apply {
      setCacheEventSetting(true)
      setAppStore(TenjinSDK.AppStoreType.googleplay)
      connect()

      debugLog { "test_tenjin connect()" }
      logEventRecord("tenjin_init")
    }
  }

  fun eventAdImpressionApplovin(maxAd: MaxAd) {
    getTenjin()?.eventAdImpressionAppLovin(maxAd)
  }

  fun eventAdImpressionAdMob(adValue: AdValue, ad: Any?) {
    getTenjin()?.eventAdImpressionAdMob(adValue, ad)
  }

  fun eventAdImpressionTradPlus(tpAdInfo: TPAdInfo) {
    getTenjin()?.eventAdImpressionTradPlus(tpAdInfo)
  }

  companion object {
    private const val TAG = "TenjinHelper"
  }
}