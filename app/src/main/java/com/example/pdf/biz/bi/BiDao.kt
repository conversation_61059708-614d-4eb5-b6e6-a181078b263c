package com.example.pdf.biz.bi

import com.tencent.mmkv.MMKV

class BiDao(
  private val mmkv: MMKV
) {

  companion object {
    private const val TAG = "BiDao"
    private const val BI_EVENT_CACHE_ENTITIES_KEY = TAG + "_bi_event_cache_entities_key"
  }


  fun fetchAll(): List<BiEventCacheEntity> =
    mmkv.decodeParcelable(BI_EVENT_CACHE_ENTITIES_KEY, BiEventCacheEntities::class.java)
      ?.list
      ?: emptyList()

  fun deleteById(id: String) {
    val list = fetchAll().toMutableList()
    val entity = list.find { it.id == id } ?: return

    if (list.remove(entity)) {
      storeBiEventCacheEntities(list)
    }
  }

  fun deleteByPath(path: String) {
    val list = fetchAll().toMutableList()
    val entities = list.filter { it.path == path }

    if (entities.isNotEmpty()) {
      entities.forEach {
        list.remove(it)
      }

      storeBiEventCacheEntities(list)
    }
  }

  fun addPending(event: BiEventCacheEntity) {
    try {
      val list = fetchAll().toMutableList()

      list.add(event)
      storeBiEventCacheEntities(list)
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  private fun storeBiEventCacheEntities(list: List<BiEventCacheEntity>) {
    mmkv.encode(BI_EVENT_CACHE_ENTITIES_KEY, BiEventCacheEntities(list))
  }

}
