@file:Suppress("PropertyName")

package com.example.pdf.biz.remoteconfig

import com.example.pdf.biz.BizConfig
import kotlinx.serialization.EncodeDefault
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.Serializable


@OptIn(ExperimentalSerializationApi::class)
@Serializable
data class AdConfig1(
  @EncodeDefault val sdk_key: String = BizConfig.APPLOVIN_MAX_SDK_KEY,
  @EncodeDefault val app_open_ad_key: String = BizConfig.APPLOVIN_MAX_APP_OPEN_AD,
  @EncodeDefault val banner_ad_key: String = BizConfig.APPLOVIN_MAX_BANNER_AD,
  @EncodeDefault val native_ad_key: String = BizConfig.APPLOVIN_MAX_NATIVE_AD,
  @EncodeDefault val inter_ad_key: String = BizConfig.APPLOVIN_MAX_INTERSTITIAL_AD,
  @EncodeDefault val inter_ad_key2: String = BizConfig.APPLOVIN_MAX_INTERSTITIAL_AD_2,
  @EncodeDefault val rewarded_ad_key: String = BizConfig.APPLOVIN_MAX_REWARDED_AD,

  @EncodeDefault val app_open_ad_show_interval_seconds: Int = 120,
  @EncodeDefault val app_open_ad_loading_timeout_seconds: Int = 8,

  @EncodeDefault val inter_ad_show_interval_seconds: Int = 60,
  @EncodeDefault val inter_ad_loading_timeout_seconds: Int = 5,

  @EncodeDefault val fullscreen_ad_show_interval_seconds: Int = 60,

  @EncodeDefault val next_inter_ad_key_active_interval_minutes: Int = -1,
) {
  companion object {
    val Default = AdConfig1()

    //language=json
    val json = """
  {
    "sdk_key": "",
    "app_open_ad_key": "9888090c2070b1ab",
    "banner_ad_key": "0c4396e612edd55b",
    "native_ad_key": "facdc1ed1b321cd0",
    "inter_ad_key": "5924a83edbdf5884",
    "inter_ad_key2": "dc7aaa142ae932c7",
    "rewarded_ad_key": "326a89541328d05d",
    "app_open_ad_loading_timeout_seconds": 8,
    "inter_ad_loading_timeout_seconds": 5,
    "fullscreen_ad_show_interval_seconds": 60,
    "next_inter_ad_key_active_interval_minutes": -1
  }
""".trimIndent()
  }
}