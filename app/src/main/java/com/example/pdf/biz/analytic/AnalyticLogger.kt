package com.example.pdf.biz.analytic

import android.annotation.SuppressLint
import android.os.Bundle
import com.example.pdf.BuildConfig
import com.example.pdf.biz.bi.BiReporter
import com.example.pdf.biz.bi.reportAdOnPaid
import com.example.pdf.coroutine.AppCoroutineScope
import com.example.pdf.kermit.debugLog
import com.google.android.gms.ads.AdValue
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.analytics
import com.google.firebase.crashlytics.crashlytics
import com.google.firebase.Firebase
import com.tradplus.ads.base.bean.TPAdInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

@SuppressLint("StaticFieldLeak")
object AnalyticsLogEvent : KoinComponent {

  private const val TAG = "AnalyticsLogEvent"

  private val analyticPrefs: AnalyticPrefs by inject()
  val appCoroutineScope: AppCoroutineScope by inject()
  private val tenjinHelper: TenjinHelper by inject()
  val biReporter: BiReporter by inject()

  fun record(eventName: String, args: Bundle? = Bundle()) {
    checkEventNameIllegal(eventName)

    if (BuildConfig.DEBUG) {
      debugLog { "$TAG eventName:{$eventName}" }
      return
    }

    appCoroutineScope.launch {
      Firebase.analytics.logEvent(eventName, args)
    }
  }

  fun revenueRecord(eventName: String, args: Bundle? = Bundle()) {
    checkEventNameIllegal(eventName)

    if (BuildConfig.DEBUG) {
      debugLog { "$TAG eventName:{$eventName}" }
      return
    }

    appCoroutineScope.launch {
      Firebase.analytics.logEvent(eventName, args)
    }
  }

  fun tryToRecordTotalAdsRevenue001Applovin(
    adFormat: String,
    adValue: Double,
    adNetwork: String,
    adUnitId: String,
  ) {
    appCoroutineScope.launch(Dispatchers.Main.immediate) {
      val total = analyticPrefs.totalAdRevenue

      val newTotal = total + adValue

      if (newTotal >= 0.01) {
        logEventAdRevenueRecord("Total_Ads_Revenue_001") {
          putDouble(FirebaseAnalytics.Param.VALUE, newTotal)
          putString(FirebaseAnalytics.Param.CURRENCY, "USD")
          putString("adNetwork", adNetwork)
          putString("adFormat", adFormat)
        }

        analyticPrefs.storeTotalAdRevenue(0.0)
      } else {
        analyticPrefs.storeTotalAdRevenue(newTotal)
      }
    }
  }

  fun tryToRecordTotalAdsRevenue001(
    adValue: AdValue?,
    adSourceName: String?,
  ) {
    appCoroutineScope.launch {
      val valueMicros = adValue?.valueMicros ?: return@launch

      val revenue = valueMicros / (1_000_000).toDouble()

      val total = analyticPrefs.totalAdRevenue
      val newTotal = total + revenue

      if (newTotal >= 0.01) {
        logEventAdRevenueRecord("Total_Ads_Revenue_001") {
          putDouble(FirebaseAnalytics.Param.VALUE, newTotal)
          putString(FirebaseAnalytics.Param.CURRENCY, "USD")
          putString("adNetwork", adSourceName ?: "")
          putString("precisionType", adValue.precisionType.toString())
        }

        analyticPrefs.storeTotalAdRevenue(0.0)
      } else {
        analyticPrefs.storeTotalAdRevenue(newTotal)
      }
    }
  }

  fun tryToRecordTotalAdsRevenue001(
    tpAdInfo: TPAdInfo,
  ) {
    appCoroutineScope.launch {
      val ecpm = tpAdInfo.ecpm?.toDouble() ?: return@launch

      val revenue = ecpm / 1_000

      val total = analyticPrefs.totalAdRevenue
      val newTotal = total + revenue

      if (newTotal >= 0.01) {
        logEventAdRevenueRecord("Total_Ads_Revenue_001") {
          putDouble(FirebaseAnalytics.Param.VALUE, newTotal)
          putString(FirebaseAnalytics.Param.CURRENCY, "USD")
          putString("adNetwork", tpAdInfo.adSourceName)
          putString("precisionType", tpAdInfo.ecpmPrecision)
        }

        analyticPrefs.storeTotalAdRevenue(0.0)
      } else {
        analyticPrefs.storeTotalAdRevenue(newTotal)
      }
    }
  }

  fun recordAdImpressionRevenue(
    adValue: AdValue?,
    adSourceName: String?,
    adFormat: String,
    adPlacement: String,
  ) {
    val valueMicros = adValue?.valueMicros ?: return

    val revenue = valueMicros / (1_000_000).toDouble()

    logEventAdRevenueRecord("Ad_Impression_Revenue") {
      putDouble(FirebaseAnalytics.Param.VALUE, revenue)
      putString(FirebaseAnalytics.Param.CURRENCY, "USD")
      putString("adNetwork", adSourceName ?: "")
      putString("precisionType", adValue.precisionType.toString())
    }

  }

  fun recordAdImpressionRevenue(
    tpAdInfo: TPAdInfo?,
    adFormat: String,
  ) {
    val ecpm = tpAdInfo?.ecpm?.toDouble() ?: return

    val revenue = ecpm / 1_000

    logEventAdRevenueRecord("Ad_Impression_Revenue") {
      putDouble(FirebaseAnalytics.Param.VALUE, revenue)
      putString(FirebaseAnalytics.Param.CURRENCY, "USD")
      putString("adNetwork", tpAdInfo.adSourceName)
      putString("adFormat", adFormat)
    }

  }

  fun recordAdImpression(
    adValue: AdValue?,
    adSourceName: String?,
    adFormat: String,
    adUnit: String,
  ) {
    val valueMicros = adValue?.valueMicros ?: return

    val revenue = valueMicros / (1_000_000).toDouble()

    logEventAdRevenueRecord(FirebaseAnalytics.Event.AD_IMPRESSION) {
      putString(FirebaseAnalytics.Param.AD_SOURCE, adSourceName)
      putString(FirebaseAnalytics.Param.AD_FORMAT, adFormat)
      putString(FirebaseAnalytics.Param.AD_UNIT_NAME, adUnit)
      putDouble(FirebaseAnalytics.Param.VALUE, revenue)
      putString(FirebaseAnalytics.Param.CURRENCY, "USD")
    }

    biLogEventAdOnPaid(
      value = revenue.toFloat(),
      currency = "USD",
      precisionType = adValue.precisionType.toString(),
      adNetwork = adSourceName ?: "",
      adType = adFormat,
    )
  }


  fun recordAdImpression(
    tpAdInfo: TPAdInfo?,
    adFormat: String,
  ) {
    val ecpm = tpAdInfo?.ecpm?.toDouble() ?: return

    val revenue = ecpm / 1_000

    logEventAdRevenueRecord(FirebaseAnalytics.Event.AD_IMPRESSION) {
      putString(FirebaseAnalytics.Param.AD_PLATFORM, "TradPlus")
      putString(FirebaseAnalytics.Param.AD_SOURCE, tpAdInfo.adSourceName)
      putString(FirebaseAnalytics.Param.AD_FORMAT, adFormat)
      putString(FirebaseAnalytics.Param.AD_UNIT_NAME, tpAdInfo.tpAdUnitId)
      putDouble(FirebaseAnalytics.Param.VALUE, revenue)
      putString(FirebaseAnalytics.Param.CURRENCY, "USD")
    }

    biLogEventAdOnPaid(
      value = revenue.toFloat(),
      currency = "USD",
      precisionType = tpAdInfo.ecpmPrecision,
      adNetwork = tpAdInfo.adSourceName ?: "",
      adType = adFormat,
    )
  }

  fun tenjinEventAdImpressionAdMob(adValue: AdValue, ad: Any?) {
    appCoroutineScope.launch {
      tenjinHelper.eventAdImpressionAdMob(adValue, ad)
    }
  }

  fun tenjinEventAdImpressionTradPlus(tpAdInfo: TPAdInfo) {
    appCoroutineScope.launch {
      tenjinHelper.eventAdImpressionTradPlus(tpAdInfo)
    }
  }

//  fun tenjinEventAdImpressionApplovin(ad: MaxAd) {
//    appCoroutineScope.launch {
//      tenjinHelper.eventAdImpressionApplovin(ad)
//    }
//  }

  @Suppress("NOTHING_TO_INLINE")
  inline fun biLogEventAdOnPaid(
    value: Float,
    currency: String,
    precisionType: String,
    adNetwork: String,
    adType: String,
    adPlacement: String = "",
  ) {
    appCoroutineScope.launch(Dispatchers.Default) {
      biReporter.reportAdOnPaid(value, currency, precisionType, adNetwork, adType, adPlacement)
    }
  }

  private fun checkEventNameIllegal(eventName: String) {
    if (eventName.length > 40) {
      if (BuildConfig.DEBUG) {
        throw IllegalArgumentException("Event name:\"$eventName\" can be up to 40 characters long.")
      } else {
        Firebase.crashlytics.recordException(IllegalArgumentException("Event name:\"$eventName\" can be up to 40 characters long."))
      }
    }
  }
}

fun logEventRecord(eventName: String, argsBlock: (Bundle.() -> Unit)? = null) {
  AnalyticsLogEvent.record(
    eventName = eventName,
    args = Bundle().apply {
      argsBlock?.invoke(this)
    }
  )
}

fun logEventAdRevenueRecord(eventName: String, argsBlock: Bundle.() -> Unit) {
  AnalyticsLogEvent.revenueRecord(eventName, Bundle().apply(argsBlock))
}

//@Suppress("NOTHING_TO_INLINE")
//inline fun logEventApplovinMaxAdRevenue(ad: MaxAd) {
//  logEventAdRevenueRecord("Ad_Impression_Revenue") {
//    putDouble(FirebaseAnalytics.Param.VALUE, ad.revenue)
//    putString(FirebaseAnalytics.Param.CURRENCY, "USD")
//    putString("adNetwork", ad.networkName)
//    putString("adFormat", ad.format.label)
//  }
//
//  AnalyticsLogEvent.tryToRecordTotalAdsRevenue001Applovin(
//    adFormat = ad.format.label,
//    adValue = ad.revenue,
//    adNetwork = ad.networkName,
//    adUnitId = ad.adUnitId
//  )
//
//  logEventAdRevenueRecord(FirebaseAnalytics.Event.AD_IMPRESSION) {
//    putString(FirebaseAnalytics.Param.AD_PLATFORM, "appLovin")
//    putString(FirebaseAnalytics.Param.AD_SOURCE, ad.networkName)
//    putString(FirebaseAnalytics.Param.AD_FORMAT, ad.format.displayName)
//    putString(FirebaseAnalytics.Param.AD_UNIT_NAME, ad.adUnitId)
//    putDouble(FirebaseAnalytics.Param.VALUE, ad.revenue)
//    putString(FirebaseAnalytics.Param.CURRENCY, "USD") // All
//  }
//
//  AnalyticsLogEvent.tenjinEventAdImpressionApplovin(ad)
//
//  AnalyticsLogEvent.biLogEventAdOnPaid(
//    value = ad.revenue.toFloat(),
//    currency = "USD",
//    precisionType = ad.revenuePrecision,
//    adNetwork = ad.networkName ?: "",
//    adType = ad.format.label ?: "",
//  )
//}