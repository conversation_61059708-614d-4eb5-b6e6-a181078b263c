package com.example.pdf.biz.fcm

import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import android.util.Log
import android.widget.RemoteViews
import androidx.annotation.AnyRes
import androidx.annotation.DrawableRes
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.core.app.NotificationCompat
import androidx.core.content.getSystemService
import com.example.pdf.R
import com.example.pdf.lyricist.globalStrings
import com.example.pdf.android.DeviceInfo
import com.example.pdf.android.toast.showToast
import com.example.pdf.biz.NotificationSwipeReceiver
import com.example.pdf.biz.bi.BiRecordCkEvent
import com.example.pdf.biz.bi.BiReporter
import com.example.pdf.biz.bi.reportFcmServiceNotificationConsumed
import com.example.pdf.biz.notification.NotificationActionNavigator
import com.example.pdf.coroutine.AppCoroutineScope
import com.example.pdf.kermit.debugLog
import com.example.pdf.serialization.toJson
import com.example.pdf.serialization.toJsonWithoutQuotesSurround
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Single
import org.koin.core.context.GlobalContext

private var fcmServiceNotificationJob: Job? = null

fun cancelFcmServiceNotificationJob() {
  fcmServiceNotificationJob?.cancel()
  fcmServiceNotificationJob = null
  debugLog(tag = "cancelFcmServiceNotificationJob") { "FcmService notification job cancelled" }
}

fun isFcmServiceNotification(notificationId: Int): Boolean {
  return notificationId in FcmServiceNotificationHelper.NOTIFICATION_ID_STARTING..(FcmServiceNotificationHelper.NOTIFICATION_ID_STARTING + 99)
}

private var tested = false
fun testFcmServiceNotification(context: Context) {
  if (tested) return
  val koin = GlobalContext.get()

  val fsnh: FcmServiceNotificationHelper = koin.get()
  val biReporter: BiReporter = koin.get()

  val appCoroutineScope: AppCoroutineScope = koin.get()

  val message = FcmServiceMessage(
    id = 1,
    title = "Test Title Test Title Test Title ",
    content = "Test Content Test Content Test Content ",
    destination = FcmServiceMessage.ClickActionDestination.LockPdf,
    additional_push_count = 5,
    title_color_darkmode = "0xFF883333",
    content_color_darkmode = "0xFF883333",
  )

  fsnh.notifyRepeat(
    message = message,
    channelId = "test_channel_id",
    clickCkEvent = BiRecordCkEvent(
      event_name = "fcms_noti_click",
      params = listOf(message.destination.toJsonWithoutQuotesSurround())
    ),
  )

  appCoroutineScope.launch {
    biReporter.reportFcmServiceNotificationConsumed(message.destination.toJsonWithoutQuotesSurround())
  }

  tested = true
}

@Single
class FcmServiceNotificationHelper(
  private val context: Context,
  private val appCoroutineScope: AppCoroutineScope,
) {

  companion object {
    private const val TAG = "FcmNotificationHelper"
    const val NOTIFICATION_ID_STARTING = 800
  }

  private val isApiGreaterThanOrEqual31 = Build.VERSION.SDK_INT >= Build.VERSION_CODES.S

  private val notificationManager
    get() = context.getSystemService<NotificationManager>()

  fun notifyRepeat(
    message: FcmServiceMessage,
    channelId: String,
    clickCkEvent: BiRecordCkEvent?,
    vararg clickEventRecord: String?,
  ) {
    notifyRepeat(
      message = message,
      channelId = channelId,
      clickCkEventJson = clickCkEvent?.toJson(),
      clickEventRecord = clickEventRecord
    )
  }

  fun notifyRepeat(
    message: FcmServiceMessage,
    channelId: String,
    clickCkEventJson: String?,
    vararg clickEventRecord: String?,
  ) {
    if (!context.checkNotificationResIdAvailable()) return

    cancelFcmServiceNotificationJob()
    fcmServiceNotificationJob = appCoroutineScope.launch(Dispatchers.Default) {
      repeat(1 + message.additional_push_count) {
        withContext(Dispatchers.Main.immediate) {
          notifyOnce(
            message = message,
            channelId = channelId,
            clickCkEventJson = clickCkEventJson,
            clickEventRecord = clickEventRecord
          )
        }

        delay(4000)
      }
    }

  }

  fun notifyOnce(
    message: FcmServiceMessage,
    channelId: String,
    clickCkEvent: BiRecordCkEvent?,
    vararg clickEventRecord: String?,
  ) {
    notifyOnce(
      message = message,
      channelId = channelId,
      clickCkEventJson = clickCkEvent?.toJson(),
      clickEventRecord = clickEventRecord
    )
  }

  fun notifyOnce(
    message: FcmServiceMessage,
    channelId: String,
    clickCkEventJson: String?,
    vararg clickEventRecord: String?,
  ) {
    if (!context.checkNotificationResIdAvailable()) return

    val notificationId = NOTIFICATION_ID_STARTING + message.id

    val navIntent = NotificationActionNavigator.createNavigateIntent(
      context = context,
      notificationId = notificationId,
      screenNode = message.destinationNode,
      clickCkEventJson = clickCkEventJson,
      clickEventRecord = clickEventRecord
    )

    val dismissIntent = NotificationSwipeReceiver.createPendingIntent(
      context = context,
      notificationId = notificationId
    )

    val foldView = if (isApiGreaterThanOrEqual31) buildNotificationRemoteViews(
      title = message.title,
      titleColor = message.titleColor,
      titleColorDarkMode = message.titleColorDarkMode,
      content = message.content,
      contentColor = message.contentColor,
      contentColorDarkMode = message.contentColorDarkMode,
      imageRes = message.imgRes,
      layoutType = RemoteViewsLayoutType.Fold,
    ) else null

    val expandView: RemoteViews = buildNotificationRemoteViews(
      title = message.title,
      titleColor = message.titleColor,
      titleColorDarkMode = message.titleColorDarkMode,
      content = message.content,
      contentColor = message.contentColor,
      contentColorDarkMode = message.contentColorDarkMode,
      imageRes = message.imgRes,
      layoutType = RemoteViewsLayoutType.Expand,
    )

    val handsUpView: RemoteViews = buildNotificationRemoteViews(
      title = message.title,
      titleColor = message.titleColor,
      titleColorDarkMode = message.titleColorDarkMode,
      content = message.content,
      contentColor = message.contentColor,
      contentColorDarkMode = message.contentColorDarkMode,
      imageRes = message.imgRes,
      layoutType = RemoteViewsLayoutType.HandsUp,
    )


    val notificationBuilder = NotificationCompat.Builder(
      context, channelId
    ).apply {
      setSmallIcon(R.drawable.ic_docs)

      if (foldView == null) {
        setCustomContentView(expandView)
        setCustomHeadsUpContentView(handsUpView)
      } else {
        setCustomContentView(foldView)
        setCustomHeadsUpContentView(handsUpView)
        setCustomBigContentView(expandView)
      }

      setContentIntent(navIntent)
      setDeleteIntent(dismissIntent)
      setAutoCancel(true)
      setVibrate(longArrayOf(0))

      setGroup(TAG)

      priority = NotificationCompat.PRIORITY_HIGH
    }

    createNotificationChannel(context, channelId)

    notificationManager?.notify(notificationId, notificationBuilder.build())

    debugLog(tag = TAG) { "notify() route:${message.destinationNode.tag()} notiId:$notificationId" }
  }

  private fun createNotificationChannel(
    context: Context,
    channelId: String
  ) {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
      val serviceChannel = NotificationChannel(
        channelId,
        context.getString(R.string.app_name),
        NotificationManager.IMPORTANCE_HIGH
      ).apply {
        setSound(null, null)
      }

      notificationManager?.createNotificationChannel(serviceChannel)
    }
  }

  private fun buildNotificationRemoteViews(
    title: String,
    titleColor: Color,
    titleColorDarkMode: Color,
    content: String,
    contentColor: Color,
    contentColorDarkMode: Color,
    @DrawableRes imageRes: Int?,
    layoutType: RemoteViewsLayoutType,
  ): RemoteViews {
    val layoutRes = when (layoutType) {
      RemoteViewsLayoutType.Fold -> R.layout.layout_fcm_service_noti
      RemoteViewsLayoutType.Expand -> R.layout.layout_fcm_service_noti_expand
      RemoteViewsLayoutType.HandsUp -> R.layout.layout_fcm_service_noti_handsup
    }

    val mainViews = RemoteViews(context.packageName, layoutRes).apply {
      setTextViewText(R.id.tv_title, title)
      setTextViewText(R.id.tv_content, content)
      setTextViewText(R.id.btn_action, globalStrings.tryNow)
      imageRes?.let { setImageViewResource(R.id.iv_img, imageRes) }

      if (DeviceInfo.isDarkMode(context)) {
        setTextColor(R.id.tv_title, titleColorDarkMode.toArgb())
        setTextColor(R.id.tv_content, contentColorDarkMode.toArgb())
      } else {
        setTextColor(R.id.tv_title, titleColor.toArgb())
        setTextColor(R.id.tv_content, contentColor.toArgb())
      }
    }

    return if (!isApiGreaterThanOrEqual31) {
      RemoteViews(context.packageName, R.layout.layout_fcm_service_noti_with_dec_container).apply {
        addView(R.id.fl_container, mainViews)
      }
    } else {
      mainViews
    }
  }

  private enum class RemoteViewsLayoutType {
    Fold, Expand, HandsUp
  }
}

internal fun Context.checkNotificationResIdAvailable(): Boolean {
  // Only check resource availability for specific Android versions (M to N)
  // where notification icon resource issues were common
  if (Build.VERSION.SDK_INT !in Build.VERSION_CODES.M..Build.VERSION_CODES.N) {
    return true
  }

  return isResourceIdAvailable(R.drawable.ic_docs)
}

private fun Context.isResourceIdAvailable(@AnyRes resId: Int): Boolean {
  return try {
    resources.getResourceName(resId).isNotEmpty()
  } catch (e: Exception) {
    // Log error instead of printing stack trace
    Log.e("ResourceCheck", "Failed to check resource availability: ${e.message}")
    false
  }
}
