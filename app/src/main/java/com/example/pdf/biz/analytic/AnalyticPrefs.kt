package com.example.pdf.biz.analytic

import com.tencent.mmkv.MMKV
import kotlinx.datetime.Instant
import org.koin.core.annotation.Single

@Suppress("PrivatePropertyName")
@Single
class AnalyticPrefs(
  private val mmkv: MMKV
) {
  companion object {
    private const val TAG = "AnalyticPrefs"
  }

  //------------------------------------------------------------------------------------------------
  private val LATEST_TENJIN_INIT_SUCCESS_INSTANT_KEY = TAG + "_latestTenjinInitSuccessInstant"

  val latestTenjinInitSuccessInstant
    get() = mmkv.decodeLong(LATEST_TENJIN_INIT_SUCCESS_INSTANT_KEY, 0L)
      .let(Instant::fromEpochSeconds)

  fun storeTenjinInitSuccessInstant(instant: Instant) {
    mmkv.encode(LATEST_TENJIN_INIT_SUCCESS_INSTANT_KEY, instant.epochSeconds)
  }
  //------------------------------------------------------------------------------------------------

  //------------------------------------------------------------------------------------------------
  private val TOTAL_AD_REVENUE_KEY = TAG + "_totalAdRevenue"

  val totalAdRevenue get() = mmkv.decodeDouble(TOTAL_AD_REVENUE_KEY, 0.0)

  fun storeTotalAdRevenue(newTotalRevenue: Double) {
    mmkv.encode(TOTAL_AD_REVENUE_KEY, newTotalRevenue)
  }
  //------------------------------------------------------------------------------------------------

}