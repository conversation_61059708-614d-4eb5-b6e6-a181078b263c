package com.example.pdf.biz.bi

import androidx.compose.ui.text.intl.Locale
import com.example.pdf.BuildConfig
import kotlinx.serialization.EncodeDefault
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@OptIn(ExperimentalSerializationApi::class)
@Suppress("PropertyName")
@Serializable
data class BiApiConfigRequestBody(
  @EncodeDefault @SerialName("language") val language: String = Locale.current.toLanguageTag(),
  @EncodeDefault @SerialName("transitionCode") val version_code: Int = BuildConfig.VERSION_CODE
)
