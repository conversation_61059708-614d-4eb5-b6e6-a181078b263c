package com.example.pdf.biz.bi

import com.example.pdf.crypt.requestBodyEncrypt
import com.example.pdf.serialization.toJson
import de.jensklingenberg.ktorfit.Response
import de.jensklingenberg.ktorfit.http.Body
import de.jensklingenberg.ktorfit.http.HeaderMap
import de.jensklingenberg.ktorfit.http.POST
import de.jensklingenberg.ktorfit.http.Path

interface BiApi {

  @POST("api/{path}")
  suspend fun report(
    @Path("path") path: String,
    @HeaderMap headerMap: Map<String, String>,
    @Body body: String
  ): Response<Unit>

  @POST("api/getRecognition")
  suspend fun config(
    @HeaderMap headerMap: Map<String, String>,
    @Body body: String
  ): Map<String, String>

  @POST("api/terminateFcmToken")
  suspend fun updateFcmToken(
    @HeaderMap headerMap: Map<String, String>,
    @Body body: String
  ): Response<Unit>

}

suspend fun BiApi.updateFcmToken(
  request: UpdateFcmTokenRequest
): Response<Unit>? {
  val header = BiReporter.header() ?: return null
  return updateFcmToken(header, request.toJson().requestBodyEncrypt())
}