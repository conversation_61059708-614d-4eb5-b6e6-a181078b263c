package com.example.pdf.biz.ad.banner

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import com.example.pdf.android.lifecycle.OnLifecycleEvent
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.kermit.debugLog
import kotlinx.coroutines.launch
import org.koin.compose.koinInject

@Composable
fun GhostBannerAd(
  adPlace: BannerAdPlace,
  modifier: Modifier = Modifier,
) {
  val context = LocalContext.current
  val scope = rememberCoroutineScope()
  val bannerAdManager: GhostBannerAdManager = koinInject()

  OnLifecycleEvent { _, event ->
    debugLog(tag = "GHOST_BANNER_AD") { "OnLifecycleEvent() event:${event.name} adPlace:${adPlace.name}" }
    when (event) {
      Lifecycle.Event.ON_START -> scope.launch {
        bannerAdManager.resume(adPlace)
      }

      Lifecycle.Event.ON_RESUME -> scope.launch {
        bannerAdManager.resume(adPlace)
      }

      Lifecycle.Event.ON_PAUSE -> scope.launch {
        bannerAdManager.pause(adPlace)
      }

      Lifecycle.Event.ON_DESTROY -> bannerAdManager.destroy(adPlace)
      else -> {}
    }
  }

  DisposableEffect(Unit) {
    logEventRecord("ad_banner_show")

    debugLog(tag = "GHOST_BANNER_AD") { "bannerAdManager.buildAd(adPlace) (Ghost - no-op)" }
    bannerAdManager.buildAd(adPlace)

    onDispose {
      // Nothing to dispose for Ghost provider
    }
  }

  // Display nothing - this is a no-op ad provider
  // We maintain the same height as other banner ads to preserve layout consistency
  Box(
    modifier = modifier.height(84.dp)
  ) {
    // Completely empty - no ad content displayed
  }
}
