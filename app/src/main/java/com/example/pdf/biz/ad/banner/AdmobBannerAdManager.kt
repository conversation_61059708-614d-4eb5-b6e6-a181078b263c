package com.example.pdf.biz.ad.banner

import android.content.Context
import com.example.pdf.biz.BizConfig
import com.example.pdf.biz.SplashController
import com.example.pdf.biz.analytic.AnalyticsLogEvent
import com.example.pdf.biz.analytic.logEventRecord
import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import com.example.pdf.kermit.debugLog
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.AdSize
import com.google.android.gms.ads.AdView
import com.google.android.gms.ads.OnPaidEventListener
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Single
import kotlin.collections.forEach

@Single
class AdmobBannerAdManager(
  private val context: Context,
  private val splashController: SplashController,
  private val remoteConfig: RealRemoteConfig,
) {
  @Suppress("PrivatePropertyName")
  private val TAG = "BannerAdManager"
  private val adUnitNameLowercase = "banner"

  private val adKey
    get() = remoteConfig.adConfig2.banner_ad_key.ifEmpty { BizConfig.ADMOB_AD_UNIT_ID_FOR_BANNER }

  private val adFlowMap = mutableMapOf<BannerAdPlace, MutableStateFlow<AdView?>>()
  private val adStateFlowMap = mutableMapOf<BannerAdPlace, MutableStateFlow<AdmobBannerAdState>>()

  fun getAdViewFlow(adPlace: BannerAdPlace): MutableStateFlow<AdView?> {
    return adFlowMap.getOrPut(adPlace) {
      MutableStateFlow(null)
    }
  }

  fun getAdStateFlow(adPlace: BannerAdPlace): MutableStateFlow<AdmobBannerAdState> {
    return adStateFlowMap.getOrPut(adPlace) {
      MutableStateFlow(AdmobBannerAdState.Companion.Empty)
    }
  }

  fun buildAd(adPlace: BannerAdPlace, adWidthDp: Int) {
    val adSize = AdSize.getCurrentOrientationAnchoredAdaptiveBannerAdSize(
      context,
      adWidthDp
    )

    AdView(context).apply {
      adUnitId = adKey
      setAdSize(adSize)
      adListener = object : AdListener() {
        override fun onAdLoaded() {
          debugLog(tag = "BannerAd ${adPlace.name}") { "onAdLoaded" }

          getAdViewFlow(adPlace).update { this@apply }
          getAdStateFlow(adPlace).update { it.copy(isPause = false) }

          logEventRecord("ad_${adUnitNameLowercase}_load_success")

          debugLog(tag = "BannerAd ${adPlace.name}") { "getAdViewFlow(adPlace).update" }
        }

        override fun onAdImpression() {
          debugLog(tag = "BannerAd ${adPlace.name}") { "onAdImpression" }
          logEventRecord("ad_${adUnitNameLowercase}_impress")
        }

        override fun onAdClicked() {
          debugLog(tag = TAG) { "place:${adPlace.name} onAdClicked()" }
          splashController.skipSplash(true)
          logEventRecord("ad_${adUnitNameLowercase}_click")
        }

        override fun onAdOpened() {
          debugLog(tag = TAG) { "place:${adPlace.name} onAdOpened()" }
        }
      }

      onPaidEventListener = OnPaidEventListener { adValue ->
        val adSourceName = this.responseInfo?.loadedAdapterResponseInfo?.adSourceName
        val adFormat = "banner"
        val adUnitId = this.adUnitId

        AnalyticsLogEvent.tryToRecordTotalAdsRevenue001(adValue, adSourceName)
        AnalyticsLogEvent.recordAdImpressionRevenue(
          adValue,
          adSourceName,
          adFormat,
          adFormat + "_" + adPlace.named
        )
        AnalyticsLogEvent.recordAdImpression(adValue, adSourceName, adFormat, adUnitId)

        AnalyticsLogEvent.tenjinEventAdImpressionAdMob(adValue, this)
      }

      debugLog(tag = "BannerAd ${adPlace.name}") { "loadAd" }

      loadAd(AdRequest.Builder().build())

      logEventRecord("ad_${adUnitNameLowercase}_load")
    }
  }

  suspend fun pause(place: BannerAdPlace) = withContext(Dispatchers.Main) {
    debugLog(tag = TAG) { "place:$place pause()" }

    val bannerAdState = getAdStateFlow(place).first()
    if (bannerAdState.isPause == false) {
      adFlowMap[place]?.first()?.pause()?.let {
        debugLog(tag = TAG) { "place:$place invoke adView pause()" }
        getAdStateFlow(place).update { it.copy(isPause = true) }
      }
    }
  }

  suspend fun resume(place: BannerAdPlace) = withContext(Dispatchers.Main) {
    debugLog(tag = TAG) { "place:$place resume()" }

    val bannerAdState = getAdStateFlow(place).first()
    if (bannerAdState.isPause == true) {
      adFlowMap[place]?.first()?.resume()?.let {
        debugLog(tag = TAG) { "place:$place invoke adView resume()" }
        getAdStateFlow(place).update { it.copy(isPause = false) }
      }
    }
  }

  fun destroyAll() {
    adFlowMap.keys.forEach(::destroy)
    adStateFlowMap.values.forEach {
      it.update { AdmobBannerAdState.Companion.Empty }
    }
  }

  fun destroy(place: BannerAdPlace) {
    debugLog(tag = TAG) { "place:$place destroy()" }

    GlobalScope.launch(Dispatchers.Main.immediate) {
      adFlowMap[place]?.first()?.destroy()?.let {
        debugLog(tag = TAG) { "place:$place invoke adView destroy()" }
        getAdStateFlow(place).update { AdmobBannerAdState.Companion.Empty }
      }
      adFlowMap[place]?.update { null }
    }
  }
}
