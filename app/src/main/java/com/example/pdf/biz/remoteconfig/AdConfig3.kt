@file:Suppress("PropertyName")

package com.example.pdf.biz.remoteconfig

import com.example.pdf.biz.BizConfig
import kotlinx.serialization.EncodeDefault
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.Serializable

@OptIn(ExperimentalSerializationApi::class)
@Serializable
data class AdConfig3(
  @EncodeDefault val app_id: String = BizConfig.TRAD_PLUS_APP_ID,
  @EncodeDefault val app_open_ad_key: String = BizConfig.TRAD_PLUS_APP_OPEN_AD_UNIT_ID,
  @EncodeDefault val banner_ad_key: String = BizConfig.TRAD_PLUS_BANNER_AD_UNIT_ID,
  @EncodeDefault val native_ad_key: String = BizConfig.TRAD_PLUS_NATIVE_AD_UNIT_ID,
  @EncodeDefault val inter_ad_key: String = BizConfig.TRAD_PLUS_INTERSTITIAL_AD_UNIT_ID,
  @EncodeDefault val inter_ad_key2: String = BizConfig.TRAD_PLUS_INTERSTITIAL_AD_UNIT_ID_2,
  @EncodeDefault val rewarded_ad_key: String = BizConfig.TRAD_PLUS_REWARD_AD_UNIT_ID,

  @EncodeDefault val app_open_ad_loading_timeout_seconds: Int = 8,
  @EncodeDefault val inter_ad_loading_timeout_seconds: Int = 5,

  @EncodeDefault val fullscreen_ad_show_interval_seconds: Int = 60,

  @EncodeDefault val next_inter_ad_key_active_interval_minutes: Int = -1,
) {
  companion object {
    val Default = AdConfig3()

    //language=json
    val json = """
  {
    "app_id": "B67BADCA01DB8DF0EDA17B6D63230013",
    "app_open_ad_key": "A1C4D3B43895B9090E6037FE214DC935",
    "banner_ad_key": "F54D1B174D26B2D5CCAD910369B42322",
    "native_ad_key": "4161DD59896FA5F42B44F5BD4AB9E6DB",
    "inter_ad_key": "DB519CD5FF8B597D9B69FA34E1EACD4E",
    "inter_ad_key2": "81A1A2867D4E8BBF2AB700E9DFEE8DDB",
    "rewarded_ad_key": "LALALA",
    "app_open_ad_loading_timeout_seconds": 8,
    "inter_ad_loading_timeout_seconds": 5,
    "fullscreen_ad_show_interval_seconds": 60,
    "next_inter_ad_key_active_interval_minutes": -1
  }
""".trimIndent()
  }
}