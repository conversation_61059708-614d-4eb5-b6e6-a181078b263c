package com.example.pdf.biz.ad.banner

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import com.example.pdf.android.lifecycle.OnLifecycleEvent
import com.example.pdf.biz.ad.AdColors
import org.koin.androidx.compose.koinViewModel
import org.orbitmvi.orbit.compose.collectAsState

@Composable
fun MaxBannerAd(
  place: BannerAdPlace,
  modifier: Modifier = Modifier,
) {
  val context = LocalContext.current

  val viewModel: MaxBannerAdViewModel = koinViewModel(key = place.named)

  val viewState by viewModel.collectAsState()

  OnLifecycleEvent { _, event ->
    when (event) {
      Lifecycle.Event.ON_START -> {
        viewState.adView?.apply {
          startAutoRefresh()
        }
        viewModel.onConfigureAdView(context)
      }

      Lifecycle.Event.ON_STOP -> {
        viewState.adView?.apply {
          stopAutoRefresh()
        }
      }

      else -> {}
    }
  }

  val adViewModifier = Modifier
    .fillMaxWidth()
    .height(50.dp)
    .background(Color.White)

  Box(
    modifier = modifier
      .background(brush = AdColors.backgroundBrush)
      .fillMaxWidth(),
    contentAlignment = Alignment.Center
  ) {
    if (viewState.loaded && viewState.adView != null) {
      Box(
        modifier = Modifier.padding(vertical = 6.dp, horizontal = 32.dp),
      ) {
        viewState.adView?.let { adView ->
          AndroidView(
            factory = { adView },
            modifier = adViewModifier
          )
        }
      }
    } else {
      val composition by rememberLottieComposition(
        spec = LottieCompositionSpec.Asset("ad_banner.json"),
      )
      Box(Modifier.padding(vertical = 6.dp).padding(horizontal = 62.dp)) {
        LottieAnimation(
          composition = composition,
          modifier = Modifier
            .fillMaxWidth()
            .height(50.dp),
          iterations = Int.MAX_VALUE,
          contentScale = ContentScale.FillBounds
        )
      }

    }
  }
}
