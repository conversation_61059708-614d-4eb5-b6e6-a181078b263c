package com.example.pdf.biz.ad.interstitial

import com.example.pdf.appInitInstant
import com.example.pdf.biz.PrefStore
import com.example.pdf.kdatetime.nowInstant
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import kotlin.time.DurationUnit
import kotlin.time.toDuration

object InterAdKeyController : KoinComponent {
  private const val TAG = "InterAdKeyController"

  private val prefStore: PrefStore by inject()

  @Suppress("LocalVariableName")
  fun useNextAdKey(next_inter_ad_key_active_interval_minutes: Int): Boolean {
    return when {
      next_inter_ad_key_active_interval_minutes < 0 -> false
      next_inter_ad_key_active_interval_minutes == 0 -> true
      else -> {
        val firstTimeLaunchAppInstant =
          prefStore.firstTimeLaunchAppInstant() ?: appInitInstant

        val nowInstant = nowInstant()

        return firstTimeLaunchAppInstant + next_inter_ad_key_active_interval_minutes.toDuration(DurationUnit.MINUTES) < nowInstant
      }
    }
  }

}