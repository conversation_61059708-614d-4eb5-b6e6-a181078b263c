package com.example.pdf.biz.ad.banner

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import org.koin.compose.koinInject

@Composable
fun BannerAd(
  adPlace: BannerAdPlace,
  modifier: Modifier = Modifier
) {
  val remoteConfig: RealRemoteConfig = koinInject()

  val useGhostAdProvider = remoteConfig.rememberUseGhostAdProvider()
  val useLegacyAdConfig = remoteConfig.rememberUseLegacyAdConfig()

  when {
    useGhostAdProvider -> {
      GhostBannerAd(
        adPlace = adPlace,
        modifier = modifier
      )
    }
    useLegacyAdConfig -> {
      TradPlusBannerAd(
        adPlace = adPlace,
        modifier = modifier
      )
    }
    else -> {
      AdmobBannerAd(
        adPlace = adPlace,
        modifier = modifier
      )
    }
  }
}