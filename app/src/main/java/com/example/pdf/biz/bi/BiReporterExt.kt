package com.example.pdf.biz.bi

import com.example.pdf.biz.PrefStore
import com.example.pdf.kermit.debugLog
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.newSingleThreadContext
import kotlinx.coroutines.withContext
import org.koin.core.context.GlobalContext

@OptIn(ExperimentalCoroutinesApi::class, DelicateCoroutinesApi::class)
private val biReporterDispatcher = newSingleThreadContext("biReporterDispatcher")

suspend fun BiReporter.reportInstallIfNeeded() = withContext(biReporterDispatcher) {
  val defPrefs: PrefStore = GlobalContext.get().get()
  val mmkv: MMKV = GlobalContext.get().get()

  val hasReportInstallKey = "${TAG}_hasReportInstall"
  val hasReportInstall = mmkv.decodeBool(
    hasReportInstallKey,
    defPrefs.firstTimeLaunchAppInstant() != null
  )

  if (hasReportInstall) return@withContext

  val event = BiRecordEvent(
    event_name = "install",
    activity = "",
    type = 1
  )

  reportEvent(event)

  mmkv.encode(hasReportInstallKey, true)
}

suspend fun BiReporter.reportPageOnStartEvent(
  pageName: String
) = withContext(biReporterDispatcher) {
  val event = BiRecordEvent(
    event_name = "on_start",
    activity = pageName,
    type = 1
  )

  reportEvent(event)
}

@Suppress("LocalVariableName")
suspend fun BiReporter.reportAdOnPaid(
  value: Float,
  currency: String,
  precisionType: String,
  adNetwork: String,
  adType: String,
  adPlacement: String,
) {
  val _value = BiRecordValue(
    value = value,
    currency = currency,
    precision_type = precisionType,
    ad_network = adNetwork,
    ad_type = adType,
    ad_placement = adPlacement
  )

  recordValue(_value)
}

suspend fun BiReporter.reportFcmServiceNotificationConsumed(
  vararg extraParam: String
) = withContext(biReporterDispatcher) {
  val event = BiRecordCkEvent(
    event_name = "fcms_noti_consumed",
    params = extraParam.toList()
  )

  reportCkEvent(event)
}

suspend fun BiReporter.reportAdEvent(
  eventName: String = "ad_event",
  adType: String = "",
  adPlace: String = "",
) = withContext(biReporterDispatcher) {
  val event = BiRecordCkEvent(
    event_name = eventName,
    params = listOf(adType, adPlace)
  )

  reportCkEvent(event)
}