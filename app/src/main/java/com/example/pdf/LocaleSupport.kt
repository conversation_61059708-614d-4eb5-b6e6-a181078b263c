package com.example.pdf

import java.util.Locale
import kotlin.collections.forEach
import kotlin.text.isEmpty
import kotlin.text.isNotEmpty

@Suppress("MemberVisibilityCanBePrivate", "HasPlatformType")
object LocaleSupport {
  val En = Locale.ENGLISH
  val Ar = Locale("ar")
  val De = Locale.GERMAN
  val Fr = Locale.FRENCH
  val Ko = Locale.KOREAN
  val Ms = Locale("ms")
  val Pt = Locale("pt")
  val Ja = Locale.JAPANESE
  val Zh_TW = Locale.TAIWAN
  val Th = Locale("th")
  val Tr = Locale("tr")
  val Es = Locale("es")
  val Zh_HK = Locale("zh", "HK")
  val It = Locale.ITALIAN
  val Id = Locale("id")
  val Vi = Locale("vi")

  val localeList = listOf<Locale>(
    <PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>h,
    <PERSON>r,
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON>d,
    Vi,
    Zh_HK,
    Zh_TW,
  )

  val latinsExcludeEn = listOf<Locale>(
    <PERSON>,
    <PERSON>,
    <PERSON>,
    Pt,
    Tr,
    Es,
    It,
    Id,
  )

  val languageMatchList: List<String>
    get() {
      val matches = mutableListOf<String>()

      localeList.forEach {
        if (it.language.isNotEmpty() && it.country.isEmpty()) {
          matches.add(it.language)
        }
      }

      return matches
    }

  fun compatibleLanguage(locale: Locale): Locale? {
    return if (locale.language in languageMatchList) {
      Locale(locale.language)
    } else if (locale in localeList) {
      locale
    } else {
      null
    }
  }
}

fun Locale.localeLanguage(): String {
  return buildString {
    append(getDisplayLanguage(this@localeLanguage))
    val localeRegion = getDisplayCountry(this@localeLanguage)
    if (localeRegion.trim().isNotEmpty()) {
      append(" - $localeRegion")
    }
  }
}

fun Locale.localeEmoji(): String {
  return when (this) {
    LocaleSupport.En -> "🇺🇸"
    LocaleSupport.Ar -> "🇸🇦"
    LocaleSupport.De -> "🇩🇪"
    LocaleSupport.Fr -> "🇫🇷"
    LocaleSupport.Ko -> "🇰🇷"
    LocaleSupport.Ms -> "🇲🇾"
    LocaleSupport.Pt -> "🇧🇷"
    LocaleSupport.Ja -> "🇯🇵"
    LocaleSupport.Zh_TW -> "🇹🇼"
    LocaleSupport.Th -> "🇹🇭"
    LocaleSupport.Tr -> "🇹🇷"
    LocaleSupport.Es -> "🇪🇸"
    LocaleSupport.Zh_HK -> "🇭🇰"
    LocaleSupport.It -> "🇮🇹"
    LocaleSupport.Id -> "🇮🇩"
    LocaleSupport.Vi -> "🇻🇳"
    else -> "🌐"
  }
}
