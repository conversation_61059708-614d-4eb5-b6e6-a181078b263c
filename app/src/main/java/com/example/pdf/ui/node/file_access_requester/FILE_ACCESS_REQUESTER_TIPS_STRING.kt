package com.example.pdf.ui.node.file_access_requester

import android.os.Build
import androidx.compose.runtime.Composable
import cafe.adriel.lyricist.LocalStrings

@Composable
fun getFileAccessRequesterTipsString(): String {
  val strings = LocalStrings.current
  return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
    strings.fileAccessAllFilesPermissionMessage
  } else {
    strings.fileAccessStoragePermissionMessage
  }
}

@Composable
fun getFileAccessRequesterTipsShortString(): String {
  val strings = LocalStrings.current
  return strings.fileAccessPermissionRequired
}
