package com.example.pdf.ui.feature.document_node

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import com.example.pdf.biz.rating.RatingHelper
import com.example.pdf.guia.ScreenNode
import com.example.pdf.guia.UseStatusBarDarkIcons
import com.example.pdf.mmkv.RecentDocumentEntity
import com.example.pdf.mmkv.UserSettingsKvStore
import com.example.pdf.ui.node.document.ms_office.excel.ExcelNode
import com.example.pdf.ui.node.document.ms_office.powerpoint.PowerPointNode
import com.example.pdf.ui.node.document.ms_office.word.WordNode
import com.example.pdf.ui.node.document.pdf.PdfViewerNode
import com.roudikk.guia.core.Navigator
import kotlinx.coroutines.flow.first
import org.koin.compose.koinInject
import java.io.File

abstract class DocumentNode(
  tag: String,
) : ScreenNode("document_${tag}") {

  abstract val document: File

  @Composable
  override fun Content(navigator: Navigator) {
    UseStatusBarDarkIcons()

    LaunchedEffectUpdateRecentDocuments()
  }

  @Composable
  private fun LaunchedEffectUpdateRecentDocuments() {
    val userSettingsKvStore: UserSettingsKvStore = koinInject()

    LaunchedEffect(Unit) {
      val recentDocuments = userSettingsKvStore.recentDocumentsFlow.first()
      val newRecentDocuments =
        recentDocuments.upsert(
          RecentDocumentEntity(document.absolutePath, System.currentTimeMillis())
        )

      userSettingsKvStore.recentDocumentsFlow.value = newRecentDocuments

      RatingHelper.setCanRatting(true)
    }
  }

}

fun DocumentNode(
  document: File
): DocumentNode? {
  return when (document.extension.lowercase()) {
    "pdf" -> PdfViewerNode(document)
    "xls", "xlsx" -> ExcelNode(document)
    "doc", "docx" -> WordNode(document)
    "ppt", "pptx" -> PowerPointNode(document)
    else -> null
  }
}