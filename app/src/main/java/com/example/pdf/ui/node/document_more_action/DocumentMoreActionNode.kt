package com.example.pdf.ui.node.document_more_action

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.PreviewComposable
import com.example.pdf.R
import com.example.pdf.android.share.shareFile
import com.example.pdf.appActivity
import com.example.pdf.guia.BottomSheetNode
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.OrangeLight
import com.example.pdf.lumo.components.Text
import com.example.pdf.mvi_ui_model.koinUiModel
import com.example.pdf.ui.feature.bottomsheet.BottomSheetSurface
import com.example.pdf.ui.feature.document_item.DocumentItem
import com.example.pdf.ui.node.document_details.DocumentDetailsNode
import com.example.pdf.ui.node.document_rename.DocumentRenameNode
import com.example.pdf.ui.node.documents_delete.DocumentsDeleteDialog
import com.roudikk.guia.core.BottomSheet
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.push
import kotlinx.parcelize.Parcelize
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState
import java.io.File

@Parcelize
class DocumentMoreActionNode(
  private val document: File
) : BottomSheetNode("document_more_action") {
  @Composable
  override fun Content(navigator: Navigator, bottomSheet: BottomSheet?) {
    val uiModel: DocumentMoreActionUiModel = koinUiModel { parametersOf(document) }
    val uiState by uiModel.collectAsState()

    if (uiState.showDeleteConfirmationDialog) {
      DocumentsDeleteDialog(
        documents = listOf(document),
        onConfirm = uiModel::onDeleteConfirm,
        onCancel = uiModel::onDismissDeleteConfirmationDialog
      )
    }

    DocumentMoreActionContent(
      document = document,
      isBookmarked = uiState.isBookmarked,
      onBookmarkClick = uiModel::onBookmarkClick,
      onRenameClick = { navigator.push(DocumentRenameNode(document)) },
      onDetailClick = { navigator.push(DocumentDetailsNode(document)) },
      onShareClick = { appActivity?.shareFile(File(document.absolutePath)) },
      onDeleteClick = uiModel::onShowDeleteConfirmationDialog
    )
  }
}

@Composable
private fun DocumentMoreActionContent(
  document: File,
  isBookmarked: Boolean,
  onBookmarkClick: (File, Boolean) -> Unit,
  onRenameClick: () -> Unit,
  onDetailClick: () -> Unit,
  onShareClick: () -> Unit,
  onDeleteClick: () -> Unit,
) {
  BottomSheetSurface(
    color = OrangeLight
  ) {
    DocumentItem(
      file = document,
      containerColor = Color.Transparent,
      isBookmarked = isBookmarked,
      isShowMoreIcon = false,
      isShowHorizontalDivider = false,
      useMarqueeWithFileName = true,
      onBookmarkClick = onBookmarkClick,
      modifier = Modifier.padding(horizontal = 8.dp)
    )

    Spacer(Modifier.height(8.dp))

    Column(
      modifier = Modifier
        .background(
          color = AppTheme.colors.surface, shape = RoundedCornerShape(
            topStart = 28.dp,
            topEnd = 28.dp,
            bottomStart = 0.dp,
            bottomEnd = 0.dp
          )
        )
        .clip(
          RoundedCornerShape(
            topStart = 28.dp,
            topEnd = 28.dp,
            bottomStart = 0.dp,
            bottomEnd = 0.dp
          )
        )
    ) {
      Spacer(Modifier.height(16.dp))

      DocumentDetailActionItem(
        text = LocalStrings.current.rename,
        iconPainter = painterResource(R.drawable.ic_input_c),
        onClick = { onRenameClick() }
      )
      DocumentDetailActionItem(
        text = LocalStrings.current.detail,
        iconPainter = painterResource(R.drawable.ic_info_c),
        onClick = { onDetailClick() }
      )
      DocumentDetailActionItem(
        text = LocalStrings.current.share,
        iconPainter = painterResource(R.drawable.ic_share_c),
        onClick = { onShareClick() }
      )
      DocumentDetailActionItem(
        text = LocalStrings.current.delete,
        iconPainter = painterResource(R.drawable.ic_delete_c),
        onClick = { onDeleteClick() }
      )

      Spacer(Modifier.height(8.dp))
    }
  }
}

@Composable
private fun DocumentDetailActionItem(
  text: String,
  iconPainter: Painter,
  onClick: () -> Unit,
  modifier: Modifier = Modifier
) {
  Box(modifier.clickable(onClick = onClick)) {
    Row(
      verticalAlignment = Alignment.CenterVertically,
      modifier = Modifier.padding(horizontal = 24.dp)
    ) {
      Image(painter = iconPainter, contentDescription = null, modifier = Modifier.size(24.dp))

      Text(
        text = text,
        style = AppTheme.typography.body1.copy(fontWeight = FontWeight.Medium),
        color = AppTheme.colors.text.copy(.9f),
        modifier = Modifier
          .weight(1f)
          .padding(vertical = 20.dp)
          .padding(horizontal = 16.dp)
      )
    }
  }
}


@Preview
@Composable
private fun DocumentMoreActionContentPreview() {
  PreviewComposable {
    DocumentMoreActionContent(
      document = File.createTempFile("lala", ".docx"),
      isBookmarked = true,
      onBookmarkClick = { _, _ -> },
      onRenameClick = {},
      onDetailClick = {},
      onShareClick = {},
      onDeleteClick = {}
    )
  }
}