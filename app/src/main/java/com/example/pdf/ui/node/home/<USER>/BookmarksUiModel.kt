package com.example.pdf.ui.node.home.bookmarks

import com.example.pdf.android.file.DocumentFilesManager
import com.example.pdf.android.file.DocumentRepository
import com.example.pdf.android.file.DocumentType
import com.example.pdf.android.toast.showToast
import com.example.pdf.kermit.debugLog
import com.example.pdf.mmkv.RecentDocumentEntity
import com.example.pdf.mmkv.UserSettingsKvStore
import com.example.pdf.mvi_ui_model.UiModel
import com.example.pdf.ui.node.home.HomeTab
import com.example.pdf.ui.node.home.all.DocumentsUiState
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.koin.android.annotation.KoinViewModel
import java.io.File

@KoinViewModel
class BookmarksUiModel(
  private val documentRepository: DocumentRepository,
  private val documentFilesManager: DocumentFilesManager,
  private val userSettingsKvStore: UserSettingsKvStore
) : UiModel<DocumentsUiState, Nothing>(
  DocumentsUiState(
    selectDocumentType = userSettingsKvStore.documentTypeTabStateFlow.value,
    homeTab = HomeTab.BOOKMARKS
  )
) {

  init {
    registerBookmarkedDocumentsFlow()
//    registerBookmarkedDocumentPathsFlow()
    registerDocumentsSortedFlow()
    registerDocumentTypeTabStateFlow()
  }

  private fun registerBookmarkedDocumentsFlow() = intent {
    documentRepository.documentsFlow.combine(userSettingsKvStore.bookmarkedDocumentPathsFlow) { allDocuments, bookmarkedPaths ->
      reduce { state.copy(bookmarkedFilePaths = bookmarkedPaths) }
      // First, filter all documents to only include those that are bookmarked
      val bookmarkedDocuments = allDocuments.filter { document ->
        bookmarkedPaths.contains(document.absolutePath)
      }
      // Then, apply the type filter and sorting as before
      val filteredAndSortedDocuments = bookmarkedDocuments.let {
        documentFilesManager.filterByDocumentType(it, state.selectDocumentType)
      }.let {
        documentFilesManager.sortDocumentFiles(it, userSettingsKvStore.documentsSortedFlow.value)
      }
      // Return the final list for the state update
      filteredAndSortedDocuments
    }.onEach { filteredDocuments ->
      // Update the state with the filtered and sorted bookmarked documents
      reduce { state.copy(displayFiles = filteredDocuments) }
    }.launchIn(uiModelScope)
  }

//  private fun registerBookmarkedDocumentPathsFlow() = intent {
//    userSettingsKvStore.bookmarkedDocumentPathsFlow.onEach { bookmarkedPaths ->
//      reduce { state.copy(bookmarkedFilePaths = bookmarkedPaths) }
//    }.launchIn(uiModelScope)
//  }

  private fun registerDocumentsSortedFlow() = intent {
    userSettingsKvStore.documentsSortedFlow.onEach { sortType ->
      // 获取当前的文档列表和书签列表
      val allDocuments = documentRepository.documentsFlow.first()
      val currentBookmarkedPaths = state.bookmarkedFilePaths

      // 1. 先筛选出已收藏的文档
      val bookmarkedDocuments = allDocuments.filter { document ->
        currentBookmarkedPaths.contains(document.absolutePath)
      }

      // 2. 对已收藏的文档进行类型筛选
      val filteredDocuments = documentFilesManager.filterByDocumentType(
        bookmarkedDocuments, // 使用已收藏的文档列表
        state.selectDocumentType
      )

      // 3. 对已筛选的收藏文档进行排序
      val sortedDocuments = documentFilesManager.sortDocumentFiles(
        filteredDocuments, // 使用已筛选的收藏文档列表
        sortType // 使用新的排序类型
      )

      // 更新显示的文档列表
      reduce { state.copy(displayFiles = sortedDocuments) }
    }.launchIn(uiModelScope)
  }

  private fun registerDocumentTypeTabStateFlow() = intent {
    userSettingsKvStore.documentTypeTabStateFlow.onEach { documentType ->
      onHandleDocumentTypeChange(documentType)
    }.launchIn(uiModelScope)
  }

  private fun onHandleDocumentTypeChange(documentType: DocumentType) = intent {
    debugLog(tag = "BookmarksUiModel") { "Changed document type to $documentType" } // Renamed tag for clarity

    // 1. 获取所有文档和当前的书签路径
    val allDocuments = documentRepository.documentsFlow.first()
    val currentBookmarkedPaths = state.bookmarkedFilePaths // Get current bookmarks

    // 2. 筛选出已收藏的文档
    val bookmarkedDocuments = allDocuments.filter { document ->
      currentBookmarkedPaths.contains(document.absolutePath)
    }

    // 3. 对已收藏的文档应用新的类型筛选
    val filteredDocuments = documentFilesManager.filterByDocumentType(
      bookmarkedDocuments, // Use only bookmarked documents
      documentType         // Apply the new type filter
    )

    // 4. 对已筛选的收藏文档应用当前的排序规则
    val sortedDocuments = documentFilesManager.sortDocumentFiles(
      filteredDocuments, // Use the type-filtered bookmarked documents
      userSettingsKvStore.documentsSortedFlow.value // Use the current sort setting
    )

    // 5. 更新状态
    reduce {
      state.copy(
        displayFiles = sortedDocuments, // Update the displayed files
        selectDocumentType = documentType // Update the selected document type
      )
    }
  }

  fun onDocumentTypeChange(documentType: DocumentType) = intent {
    userSettingsKvStore.documentTypeTabStateFlow.value = documentType
  }

  fun onBookMarkDocument(file: File, bookmarked: Boolean) = intent {
    val needHandleDocumentPath = file.absolutePath
    val bookmarkedPaths = userSettingsKvStore.bookmarkedDocumentPathsFlow.value

    val updatedBookmarkedDocumentPaths = if (bookmarked) {
      bookmarkedPaths + needHandleDocumentPath
    } else {
      bookmarkedPaths - needHandleDocumentPath
    }

    userSettingsKvStore.bookmarkedDocumentPathsFlow.value = updatedBookmarkedDocumentPaths

//    reduce { state.copy(bookmarkedFilePaths = updatedBookmarkedDocumentPaths) }
  }

}