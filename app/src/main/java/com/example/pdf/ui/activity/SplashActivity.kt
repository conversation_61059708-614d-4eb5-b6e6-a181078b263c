package com.example.pdf.ui.activity

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Parcelable
import android.view.ViewGroup
import android.widget.FrameLayout
import kotlinx.parcelize.Parcelize
import androidx.activity.compose.BackHandler
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalWindowInfo
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.zIndex
import androidx.core.content.IntentCompat
import com.example.pdf.BaseActivity
import com.example.pdf.biz.ad.appopen.AppOpenAdFacade
import com.example.pdf.biz.notification.FixedNotificationHelper
import com.example.pdf.biz.notification.NotificationPermissionRequestDialog
import com.example.pdf.biz.notification.NotificationPermissionRequester
import com.example.pdf.kdatetime.nowInstant
import com.example.pdf.kermit.debugLog
import com.example.pdf.lumo.AppTheme
import com.example.pdf.mvi_ui_model.koinUiModel
import com.example.pdf.ui.activity.splash.SplashSideEffect
import com.example.pdf.ui.activity.splash.SplashViewModel
import com.example.pdf.ui.node.splash.SplashContent
import kotlinx.coroutines.delay
import kotlinx.datetime.Instant
import org.koin.android.ext.android.inject
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectSideEffect
import kotlin.time.Duration.Companion.minutes

private var splashDisplayCount = 0
private var lastNotificationRequestTime: Instant = Instant.fromEpochSeconds(0)
private val NOTIFICATION_REQUEST_INTERVAL = 60.minutes

@SuppressLint("StaticFieldLeak")
var splashActivity: SplashActivity? = null

@Suppress("ClassName")
@Parcelize
sealed interface SplashLaunchType : Parcelable {
  @Parcelize
  data object COLD_START : SplashLaunchType

  @Parcelize
  data object WARM_START : SplashLaunchType

  @Parcelize
  data object NOTIFICATION_CLICK : SplashLaunchType
}

/**
 * Splash screen activity that shows app logo and loading indicator
 * before navigating to the main app
 */
@SuppressLint("CustomSplashScreen")
class SplashActivity : BaseActivity() {

  companion object {
    private const val TAG = "SplashActivity"
    private const val ARG_LAUNCH_TYPE = "${TAG}_launch_type"

    fun createIntent(
      context: Context,
      type: SplashLaunchType = SplashLaunchType.COLD_START,
    ): Intent {
      return Intent(context, SplashActivity::class.java).apply {
        putExtra(ARG_LAUNCH_TYPE, type)
      }
    }
  }


  val launchType
    get() = runCatching {
      IntentCompat.getParcelableExtra(
        intent,
        ARG_LAUNCH_TYPE,
        SplashLaunchType::class.java
      )
    }.getOrNull() ?: SplashLaunchType.COLD_START

  val fixedNotificationHelper: FixedNotificationHelper by inject()
  val notificationPermissionRequester: NotificationPermissionRequester by inject()
  val appOpenAdFacade: AppOpenAdFacade by inject()

  var splashAdContainer: ViewGroup? = null
    private set

  init {
    notificationPermissionRequester.registerPermissionResult(this)
  }

  override fun onCreate(savedInstanceState: Bundle?) {
    splashActivity = this
    intent?.let(fixedNotificationHelper::handleClickEvent)
    super.onCreate(savedInstanceState)
    enableEdgeToEdge()
//    setFullScreen()

    setContent {
      SplashAdContainer(
        modifier = Modifier
          .fillMaxSize()
          .zIndex(1f)
          .systemBarsPadding(),
        onCreatedContainer = { container ->
          splashAdContainer = container
        }
      )

      AppTheme {
        val uiModel: SplashViewModel = koinUiModel { parametersOf(launchType) }

        uiModel.collectSideEffect {
          when (it) {
            SplashSideEffect.NavUp -> finish()
            SplashSideEffect.ShowAdmobAppOpenAd -> {
              appOpenAdFacade.tryToShowAd(this, immediate = true)
            }
          }
        }

        var showNotiRequester by remember { mutableStateOf(false) }
        var useSystemPermissionDialog by remember { mutableStateOf(false) }

        val windowInfo = LocalWindowInfo.current
        LaunchedEffect(windowInfo.isWindowFocused) {
          delay(100)
          if (windowInfo.isWindowFocused) {
            notificationPermissionRequester.tryToRequestIfNeeded(this@SplashActivity) {
              showNotiRequester = true
              useSystemPermissionDialog = it
            }
          }

          delay(250)
          if (windowInfo.isWindowFocused)
            uiModel.requestConsentAndConfigureAds(
              activity = this@SplashActivity,
            )
        }

        LaunchedEffect(Unit) {
          val now = nowInstant()
          if (splashDisplayCount > 0 && (now - lastNotificationRequestTime >= NOTIFICATION_REQUEST_INTERVAL)) {
            notificationPermissionRequester.tryToShowDefaultFixedNotification(this@SplashActivity)

            lastNotificationRequestTime = now
          }
          splashDisplayCount++
        }

        if (showNotiRequester) {
          NotificationPermissionRequestDialog(
            onDismiss = { showNotiRequester = false },
            useSystemPermissionDialog = useSystemPermissionDialog,
            autoIncrementRequestCounter = true,
          )
        }

        BackHandler { }

        SplashScreen()
      }
    }
  }

  @Composable
  private fun SplashAdContainer(
    modifier: Modifier = Modifier,
    onCreatedContainer: (FrameLayout) -> Unit = {},
  ) {
    AndroidView(
      factory = { context ->
        FrameLayout(context).apply {
          layoutParams = FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.MATCH_PARENT
          )

          onCreatedContainer(this)
        }
      },
      modifier = modifier
    )
  }

  override fun onDestroy() {
    splashActivity = null
    super.onDestroy()
    debugLog(tag = TAG) { "onDestroy()" }
  }
}

@Composable
private fun SplashScreen() {
  SplashContent()
}
