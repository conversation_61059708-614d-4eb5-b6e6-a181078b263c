package com.example.pdf.ui.node.img2pdf.list

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.IconButton
import com.example.pdf.lumo.components.IconButtonVariant
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.card.CardDefaults
import com.example.pdf.mmkv.UserSettingsKvStore
import org.koin.compose.koinInject

@Composable
fun Img2PdfTips(
  modifier: Modifier,
  outSidePaddingValues: PaddingValues = PaddingValues()
) {
  val userSettingsKvStore: UserSettingsKvStore = koinInject()
  val displayTips by userSettingsKvStore.displayImg2PdfReorderableTipsFlow.collectAsState()

  Box(modifier.padding(if (displayTips) outSidePaddingValues else PaddingValues())) {
    if (displayTips) {
      Img2PdfTipsContent(
        onDismiss = { userSettingsKvStore.displayImg2PdfReorderableTipsFlow.value = false }
      )
    }
  }
}

@Composable
private fun Img2PdfTipsContent(
  onDismiss: () -> Unit,
  modifier: Modifier = Modifier
) {
  Row(
    modifier = modifier
      .background(AppTheme.colors.primary.copy(alpha = .1f), CardDefaults.Shape)
      .clip(CardDefaults.Shape)
      .border(1.2.dp, AppTheme.colors.primary.copy(.6f), CardDefaults.Shape),
    verticalAlignment = Alignment.CenterVertically
  ) {
    Text(
      text = LocalStrings.current.reorderTip,
      modifier = Modifier.padding(start = 16.dp),
      color = AppTheme.colors.primary.copy(.8f)
    )

    Spacer(Modifier.weight(1f))

    IconButton(
      onClick = onDismiss,
      variant = IconButtonVariant.Ghost,
      modifier = Modifier.padding(end = 4.dp)
    ) {
      Icon(
        imageVector = Icons.Rounded.Close, contentDescription = LocalStrings.current.dismissTips,
        tint = AppTheme.colors.primary.copy(.8f)
      )
    }
  }
}


@Preview
@Composable
private fun Img2PdfTipsContentPreview() {
  AppTheme {
    Box(Modifier.background(AppTheme.colors.background)) {
      Img2PdfTipsContent(onDismiss = {})
    }
  }
}