package com.example.pdf.ui.node.image_picker

import com.example.pdf.mvi_ui_model.UiModel
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.koin.android.annotation.KoinViewModel

@KoinViewModel
class ImagePickerUiModel(
  private val imageAlbumManager: ImageAlbumManager
) : UiModel<ImagePickerUiState, Nothing>(ImagePickerUiState()) {

  init {
    // 观察StateFlow变化，参考DocumentRepository模式
    observeAlbumsFlow()
    observeImagesFlow()

    // 触发初始加载
//    onLoadAlbums()
  }

  private fun observeAlbumsFlow() = intent {
    imageAlbumManager.albumsFlow.onEach { albums ->
      if (albums.isNotEmpty()) {
        val defaultAlbum = albums.firstOrNull { it.id == "all_images" } ?: albums.firstOrNull()
        val currentSelectedAlbum = state.selectedAlbum

        // 如果还没有选中相册，自动选择默认相册
        val albumToSelect = currentSelectedAlbum ?: defaultAlbum

        reduce {
          state.copy(
            albums = albums,
            selectedAlbum = albumToSelect,
            isLoading = false
          )
        }

        // 如果是首次加载，自动加载默认相册的图片
        if (currentSelectedAlbum == null && albumToSelect != null) {
          loadImagesForSelectedAlbum(albumToSelect)
        }
      } else {
        // 处理没有相册的情况，停止loading状态
        reduce {
          state.copy(
            albums = emptyList(),
            selectedAlbum = null,
            images = emptyList(),
            isLoading = false
          )
        }
      }
    }.launchIn(uiModelScope)
  }

  private fun observeImagesFlow() = intent {
    imageAlbumManager.imagesByFolderFlow.onEach { imagesByFolder ->
      val currentAlbum = state.selectedAlbum
      if (currentAlbum != null) {
        val images = when (currentAlbum.id) {
          "all_images" -> imagesByFolder[ImageAlbumManager.ALL_IMAGES_FOLDER] ?: emptyList()
          else -> {
            // 查找匹配的 bucketId
            val bucketId = currentAlbum.id.removePrefix("folder_")
            val folderKey = imagesByFolder.keys.find { it.startsWith("$bucketId:") }
            folderKey?.let { imagesByFolder[it] } ?: emptyList()
          }
        }
        reduce {
          state.copy(
            images = images,
            isLoading = false
          )
        }
      } else if (imagesByFolder.isEmpty()) {
        // 处理没有图片数据的情况，停止loading状态
        reduce {
          state.copy(
            images = emptyList(),
            isLoading = false
          )
        }
      }
    }.launchIn(uiModelScope)
  }

  private fun loadImagesForSelectedAlbum(album: ImageAlbum) = intent {
    val currentImages = imageAlbumManager.imagesByFolderFlow.value
    val images = when (album.id) {
      "all_images" -> currentImages[ImageAlbumManager.ALL_IMAGES_FOLDER] ?: emptyList()
      else -> {
        // 查找匹配的 bucketId
        val bucketId = album.id.removePrefix("folder_")
        val folderKey = currentImages.keys.find { it.startsWith("$bucketId:") }
        folderKey?.let { currentImages[it] } ?: emptyList()
      }
    }

    reduce {
      state.copy(images = images)
    }
  }

  fun onLoadAlbums() = intent {
    // 检查是否已有缓存数据
    val hasCache = imageAlbumManager.albumsFlow.value.isNotEmpty()

    reduce { state.copy(isLoading = !hasCache) } // 有缓存就不显示loading

    // 触发数据加载（如果有缓存会立即通过StateFlow更新UI）
    imageAlbumManager.onFetchImages()
  }

  fun onAlbumSelected(album: ImageAlbum) = intent {
    reduce {
      state.copy(
        selectedAlbum = album,
        showAlbumDropdown = false
      )
    }

    // 图片会通过observeImagesFlow自动更新
    val currentImages = imageAlbumManager.imagesByFolderFlow.value
    val images = when (album.id) {
      "all_images" -> currentImages[ImageAlbumManager.ALL_IMAGES_FOLDER] ?: emptyList()
      else -> {
        // 查找匹配的 bucketId
        val bucketId = album.id.removePrefix("folder_")
        val folderKey = currentImages.keys.find { it.startsWith("$bucketId:") }
        folderKey?.let { currentImages[it] } ?: emptyList()
      }
    }

    reduce {
      state.copy(images = images)
    }
  }

  fun onImageSelected(image: ImageItem) = intent {
    val currentSelected = state.selectedImages.toMutableList()

    if (currentSelected.contains(image)) {
      currentSelected.remove(image)
    } else {
      currentSelected.add(image)
    }

    reduce { state.copy(selectedImages = currentSelected) }
  }

  fun onImageRemoved(image: ImageItem) = intent {
    val currentSelected = state.selectedImages.toMutableList()
    currentSelected.remove(image)
    reduce { state.copy(selectedImages = currentSelected) }
  }

  fun onAlbumDropdownToggle() = intent {
    reduce { state.copy(showAlbumDropdown = !state.showAlbumDropdown) }
  }

  fun onAlbumDropdownDismiss() = intent {
    reduce { state.copy(showAlbumDropdown = false) }
  }
}
