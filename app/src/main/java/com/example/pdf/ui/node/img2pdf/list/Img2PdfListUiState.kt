package com.example.pdf.ui.node.img2pdf.list

import android.graphics.Bitmap
import android.net.Uri
import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlin.uuid.ExperimentalUuidApi
import kotlin.uuid.Uuid

@Parcelize
data class Img2PdfItemData @OptIn(ExperimentalUuidApi::class) constructor(
  val id: String = Uuid.random().toHexString(),
  val cropped: Boolean = false,
  val cropTimes: Int = 0,
  val uri: Uri,
  val croppedBitmap: Bitmap? = null
) : Parcelable

data class ShowDeleteDialogState(
  val show: Boolean,
  val index: Int,
) {
  companion object {
    val Empty = ShowDeleteDialogState(false, -1)
  }
}

data class Img2PdfListUiState(
  val list: List<Img2PdfItemData> = emptyList(),
  val processing: Boolean = false,
  val showLeaveDialog: Boolean = false,
  val showDeleteDialogState: ShowDeleteDialogState = ShowDeleteDialogState.Empty,
)