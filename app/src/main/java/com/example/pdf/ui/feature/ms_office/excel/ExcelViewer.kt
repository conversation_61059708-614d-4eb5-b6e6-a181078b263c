package com.example.pdf.ui.feature.ms_office.excel

import android.view.ViewGroup
import android.webkit.JavascriptInterface
import android.webkit.RenderProcessGoneDetail
import android.webkit.WebSettings
import android.webkit.WebView
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import com.multiplatform.webview.web.AccompanistWebChromeClient
import com.multiplatform.webview.web.AccompanistWebViewClient
import com.multiplatform.webview.web.PlatformWebViewParams
import com.multiplatform.webview.web.WebView
import com.multiplatform.webview.web.rememberWebViewState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.IOException
import kotlin.io.encoding.Base64
import kotlin.io.encoding.ExperimentalEncodingApi


@Composable
fun ExcelViewerWithWebView(
  document: File,
  modifier: Modifier = Modifier,
  onError: () -> Unit,
) {
  val scope = rememberCoroutineScope()
  val webViewState = rememberWebViewState("file:///android_asset/excel_viewer_fixed.html")
  val webViewParams = remember {
    PlatformWebViewParams(
      client = object : AccompanistWebViewClient() {
        override fun onRenderProcessGone(
          view: WebView?,
          detail: RenderProcessGoneDetail?
        ): Boolean {
          onError()

          // 销毁当前的WebView
          runCatching {
            if (view != null) {
              val parent = view.parent
              if (parent is ViewGroup) {
                parent.removeView(view)
              }
              view.destroy()
            }
          }

          return true
        }
      },
      chromeClient = AccompanistWebChromeClient()
    )
  }

  WebView(
    state = webViewState,
    modifier = modifier,
    onCreated = { webView ->
      with(webView) {
        addJavascriptInterface(WebAppInterface(scope, webView, document), "AndroidExcelInterface")
        settings.apply {
          domStorageEnabled = true
          mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
        }
      }
    },
    onDispose = { webView ->
      runCatching {
        with(webView) {
          stopLoading()
          clearHistory()
          clearCache(true)
          destroy()
        }
      }
    },
    platformWebViewParams = webViewParams
  )
}

// Javascript Interface Class
@Suppress("unused")
class WebAppInterface(
  private val coroutineScope: CoroutineScope,
  private val webView: WebView,
  private val file: File
) {

  @OptIn(ExperimentalEncodingApi::class)
  @JavascriptInterface // Annotation required for methods accessible from JS
  fun getExcelDataAsBase64() {
    if (!file.exists()) {
      // Handle error - maybe call a JS function to show error
      webView.post { // Ensure UI updates are on the main thread
        webView.evaluateJavascript("javascript:showError('File not found')", null)
      }
      return
    }

    coroutineScope.launch {
      try {
        val base64String = withContext(Dispatchers.IO) {
          Base64.encode(file.readBytes())
        }

        // Call the JavaScript function in HTML to process the data
        // Ensure this runs on the main thread
        webView.post {
          // Escape the base64 string properly for JS if necessary,
          // though usually direct injection works for base64.
          // Be mindful of potential large string size limits.
          webView.evaluateJavascript("javascript:displayExcelData('$base64String')", null)
        }
      } catch (e: IOException) {
        e.printStackTrace()
        // Handle error reading file
        webView.post {
          webView.evaluateJavascript("javascript:showError('Error reading file')", null)
        }
      } catch (e: OutOfMemoryError) {
        e.printStackTrace()
        // Handle large file memory issues
        webView.post {
          webView.evaluateJavascript("javascript:showError('File is too large to process')", null)
        }
      } catch (e: Exception) {
        e.printStackTrace()
      }
    }
  }
}