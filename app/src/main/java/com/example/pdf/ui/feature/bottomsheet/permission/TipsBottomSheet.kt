package com.example.pdf.ui.feature.bottomsheet.permission

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.pdf.PreviewComposable
import com.example.pdf.R
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Button
import com.example.pdf.lumo.components.ButtonVariant
import com.example.pdf.lumo.components.Text
import com.example.pdf.ui.composable.BlankSpacer
import com.example.pdf.ui.feature.bottomsheet.BottomSheetSurface
import cafe.adriel.lyricist.LocalStrings

@Composable
fun TipsBottomSheetContent(
  title: String,
  description: String,
  iconPainter: Painter,
  confirmText: String = LocalStrings.current.confirm,
  cancelText: String = LocalStrings.current.cancel,
  onConfirm: () -> Unit,
  onCancel: () -> Unit,
) {
  BottomSheetSurface {
    Column(
      modifier = Modifier
        .fillMaxWidth()
        .padding(24.dp),
      horizontalAlignment = Alignment.CenterHorizontally
    ) {
      Row {
        Column(modifier = Modifier.weight(1f)) {
          Text(
            text = title,
            style = AppTheme.typography.h2,
            color = AppTheme.colors.onBackground
          )

          Spacer(modifier = Modifier.height(8.dp))

          Text(
            text = description,
            style = AppTheme.typography.body1.copy(
              fontWeight = FontWeight.Medium,
              lineHeight = 19.sp
            ),
            color = AppTheme.colors.onBackground.copy(alpha = 0.7f)
          )
        }

        Spacer(modifier = Modifier.width(16.dp))

        Image(
          painter = iconPainter,
          contentDescription = null,
          modifier = Modifier.size(44.dp)
        )
      }

      Spacer(modifier = Modifier.height(24.dp))

      Row {
        Button(
          onClick = onCancel,
          modifier = Modifier
            .weight(1f)
            .height(48.dp),
          variant = ButtonVariant.Secondary
        ) {
          Text(
            text = cancelText.uppercase(),
            style = AppTheme.typography.buttonLarge,
          )
        }

        BlankSpacer(24.dp)

        Button(
          onClick = onConfirm,
          modifier = Modifier
            .weight(1f)
            .height(48.dp),
          variant = ButtonVariant.Primary
        ) {
          Text(
            text = confirmText.uppercase(),
            style = AppTheme.typography.buttonLarge,
          )
        }
      }
    }
  }
}


@Preview
@Composable
private fun TipsBottomSheetContentPreview() {
  PreviewComposable {
    TipsBottomSheetContent(
      title = "lalalalalala",
      description = "lalalalalalalalaalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalala",
      iconPainter = painterResource(R.drawable.ic_permission_access_file_locked_c),
      onConfirm = {},
      onCancel = {}
    )
  }
}