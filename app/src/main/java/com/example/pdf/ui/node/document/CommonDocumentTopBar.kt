package com.example.pdf.ui.node.document

import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.rounded.ArrowBackIos
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import com.example.pdf.PreviewComposable
import com.example.pdf.R
import com.example.pdf.lumo.AppColors
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.LocalContentColor
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.IconButton
import com.example.pdf.lumo.components.IconButtonVariant

@Composable
fun CommonDocumentTopBar(
  title: String,
  isBookmarked: Boolean,
  onBackClick: () -> Unit,
  onBookmarkClick: (Boolean) -> Unit,
  onShareClick: () -> Unit,
  onMoreClick: () -> Unit,
) {
  TopAppBar(
    title = {
      Text(
        text = title,
        style = AppTheme.typography.h3,
        modifier = Modifier
          .fillMaxWidth()
          .basicMarquee()
      )
    },
    navigationIcon = {
      IconButton(onClick = onBackClick, shape = CircleShape, variant = IconButtonVariant.Ghost) {
        Icon(imageVector = Icons.AutoMirrored.Rounded.ArrowBackIos)
      }
    },
    actions = {
      IconButton(
        onClick = { onBookmarkClick(!isBookmarked) },
        shape = CircleShape,
        variant = IconButtonVariant.Ghost
      ) {
        Icon(
          painter = if (isBookmarked)
            painterResource(R.drawable.ic_bookmark_selected)
          else
            painterResource(R.drawable.ic_bookmark),
          contentDescription = "bookmark",
          tint = if (isBookmarked) AppColors.primary else LocalContentColor.current
        )
      }

      IconButton(onClick = onShareClick, shape = CircleShape, variant = IconButtonVariant.Ghost) {
        Icon(
          painter = painterResource(R.drawable.ic_share),
          contentDescription = "share",
        )
      }

      IconButton(onClick = onMoreClick, shape = CircleShape, variant = IconButtonVariant.Ghost) {
        Icon(
          painter = painterResource(R.drawable.ic_more),
          contentDescription = "more",
        )
      }
    },
    colors = TopAppBarDefaults.topAppBarColors(containerColor = AppTheme.colors.surface),
  )
}

@Preview
@Composable
private fun TopBarPreview() {
  PreviewComposable {
    CommonDocumentTopBar(
      title = "Sample Document.docx",
      isBookmarked = false,
      onBackClick = {},
      onBookmarkClick = {},
      onShareClick = {},
      onMoreClick = {}
    )
  }
}

@Preview
@Composable
private fun TopBarBookmarkedPreview() {
  PreviewComposable {
    CommonDocumentTopBar(
      title = "Sample Document.docx",
      isBookmarked = true,
      onBackClick = {},
      onBookmarkClick = {},
      onShareClick = {},
      onMoreClick = {}
    )
  }
}