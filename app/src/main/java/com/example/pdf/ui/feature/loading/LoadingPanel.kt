package com.example.pdf.ui.feature.loading

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Text

@Composable
fun LoadingPanel(
  modifier: Modifier = Modifier
) {
  val strings = LocalStrings.current

  Column(
    modifier = modifier,
    verticalArrangement = Arrangement.Center,
    horizontalAlignment = Alignment.CenterHorizontally
  ) {
    LoadingFileContent(Modifier.size(64.dp))

    Spacer(Modifier.height(8.dp))

    Text(text = strings.loading, style = AppTheme.typography.h4)
  }
}