package com.example.pdf.ui.node.document

import com.example.pdf.mmkv.UserSettingsKvStore
import com.example.pdf.mvi_ui_model.UiModel
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.koin.android.annotation.KoinViewModel
import java.io.File

@KoinViewModel
class CommonDocumentUiModel(
  private val document: File,
  private val userSettingsKvStore: UserSettingsKvStore,
) : UiModel<CommonDocumentUiState, Nothing>(CommonDocumentUiState()) {

  init {
    registerBookmarkedDocumentPathsFlow()
  }

  private fun registerBookmarkedDocumentPathsFlow() = intent {
    userSettingsKvStore.bookmarkedDocumentPathsFlow.onEach { bookmarkedDocumentPaths ->
      reduce {
        state.copy(isBookmarked = document.absolutePath in bookmarkedDocumentPaths)
      }
    }.launchIn(uiModelScope)

  }

  fun onBookmarkClick(file: File, bookmarked: Boolean) = intent {
    val needHandleDocumentPath = file.absolutePath
    val bookmarkedPaths = userSettingsKvStore.bookmarkedDocumentPathsFlow.value

    val updatedBookmarkedDocumentPaths = if (bookmarked) {
      bookmarkedPaths + needHandleDocumentPath
    } else {
      bookmarkedPaths - needHandleDocumentPath
    }

    userSettingsKvStore.bookmarkedDocumentPathsFlow.value = updatedBookmarkedDocumentPaths

    reduce { state.copy(isBookmarked = file.absolutePath in updatedBookmarkedDocumentPaths) }
  }

}