package com.example.pdf.ui.node.settings

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.automirrored.rounded.KeyboardArrowRight
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.pdf.PreviewComposable
import com.example.pdf.R
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.card.Card
import com.example.pdf.lumo.components.card.CardDefaults

@Composable
fun SettingsItem(
  icon: Painter,
  title: String,
  onClick: () -> Unit,
  modifier: Modifier = Modifier,
  suffix: @Composable () -> Unit = {
    Icon(
      imageVector = Icons.AutoMirrored.Rounded.KeyboardArrowRight,
      contentDescription = null,
      modifier = Modifier.size(28.dp)
    )
  },
) {
  Box(modifier) {
    Card(
      onClick = onClick,
      modifier = Modifier.fillMaxWidth(),
    ) {
      Row(
        modifier = Modifier
          .fillMaxWidth()
          .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
      ) {
        Row(
          verticalAlignment = Alignment.CenterVertically,
          horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
          Box(
            modifier = Modifier
              .size(34.dp)
              .background(AppTheme.colors.disabled.copy(.5f), CardDefaults.Shape),
            contentAlignment = Alignment.Center
          ) {
            Image(painter = icon, contentDescription = null, modifier = Modifier.size(24.dp))
          }
          Text(
            text = title,
            style = AppTheme.typography.h2,
            fontSize = 17.sp
          )
        }
        suffix()
      }
    }
  }
}

@Preview
@Composable
private fun SettingsItemPreview() {
  PreviewComposable {
    SettingsItem(
      icon = painterResource(R.drawable.ic_settings_lang),
      title = "Language",
      onClick = {}
    )
  }
}