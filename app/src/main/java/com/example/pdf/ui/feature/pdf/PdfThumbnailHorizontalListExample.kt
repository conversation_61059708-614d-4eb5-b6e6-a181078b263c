//package com.example.pdf.ui.feature.pdf
//
//import androidx.compose.foundation.layout.Column
//import androidx.compose.foundation.layout.Spacer
//import androidx.compose.foundation.layout.fillMaxWidth
//import androidx.compose.foundation.layout.height
//import androidx.compose.foundation.layout.padding
//import androidx.compose.material3.Text
//import androidx.compose.runtime.Composable
//import androidx.compose.runtime.getValue
//import androidx.compose.runtime.mutableStateOf
//import androidx.compose.runtime.remember
//import androidx.compose.runtime.setValue
//import androidx.compose.ui.Modifier
//import androidx.compose.ui.graphics.ImageBitmap
//import androidx.compose.ui.unit.dp
//import com.bhuvaneshw.pdf.compose.PdfState
//import com.example.pdf.lumo.AppTheme
//
///**
// * Example usage of PdfThumbnailHorizontalList with a PDF viewer
// */
//@Composable
//fun PdfViewerWithThumbnails(
//    pdfState: PdfState,
//    thumbnails: List<ImageBitmap?>,
//    modifier: Modifier = Modifier
//) {
//    // State to track the currently selected page
//    var selectedPageIndex by remember { mutableStateOf(0) }
//
//    Column(modifier = modifier) {
//        // Main PDF viewer content would go here
//        // ...
//
//        Spacer(modifier = Modifier.height(16.dp))
//
//        // Title for the thumbnails section
//        Text(
//            text = "Pages",
//            style = AppTheme.typography.h3,
//            modifier = Modifier.padding(horizontal = 16.dp)
//        )
//
//        Spacer(modifier = Modifier.height(8.dp))
//
//        // Thumbnail list
//        PdfThumbnailHorizontalList(
//            thumbnails = thumbnails.mapIndexed { index, bitmap ->
//                PdfThumbnailItem(pageIndex = index, thumbnail = bitmap)
//            },
//            selectedPageIndex = selectedPageIndex,
//            onThumbnailSelected = { pageIndex ->
//                selectedPageIndex = pageIndex
//                // Navigate to the selected page in the PDF viewer
//                pdfState.pdfViewer?.goToPage(pageIndex + 1)
//            },
//            modifier = Modifier.fillMaxWidth()
//        )
//    }
//}
