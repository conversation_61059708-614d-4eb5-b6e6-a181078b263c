package com.example.pdf.ui.node.image_picker

import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material.icons.rounded.KeyboardArrowDown
import androidx.compose.material.icons.rounded.KeyboardArrowUp
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.coil.CoilImage
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.Surface
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.card.CardDefaults

@Composable
fun AlbumDropdown(
  selectedAlbum: ImageAlbum?,
  albums: List<ImageAlbum>,
  expanded: Boolean,
  onExpandedChange: (Boolean) -> Unit,
  onAlbumSelected: (ImageAlbum) -> Unit,
  modifier: Modifier = Modifier
) {
  Box(modifier = modifier) {
    // Dropdown trigger
    Surface(
      onClick = { onExpandedChange(!expanded) },
      modifier = Modifier.clip(CardDefaults.Shape),
      shape = CardDefaults.Shape,
      color = AppTheme.colors.disabled.copy(.5f)
    ) {
      Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(2.dp),
        modifier = Modifier.padding(vertical = 8.dp).padding(start = 16.dp, end = 8.dp)
      ) {
        Text(
          text = selectedAlbum?.name ?: "Select Album",
          style = AppTheme.typography.h3,
          color = AppTheme.colors.onBackground,
          fontSize = 17.sp,
          maxLines = 1,
          overflow = TextOverflow.Ellipsis,
        )
        Icon(
          imageVector = if (expanded) Icons.Rounded.KeyboardArrowUp else Icons.Rounded.KeyboardArrowDown,
          tint = AppTheme.colors.onBackground
        )
      }
    }

    // Dropdown menu
    if (expanded) {
      Popup(
        alignment = Alignment.TopStart,
        onDismissRequest = { onExpandedChange(false) },
        properties = PopupProperties(focusable = true)
      ) {
        Surface(
          modifier = Modifier
            .widthIn(min = 200.dp, max = 300.dp)
            .heightIn(max = 400.dp)
            .padding(top = 4.dp),
          shape = RoundedCornerShape(12.dp),
          shadowElevation = 8.dp,
          color = AppTheme.colors.surface
        ) {
          LazyColumn(
            modifier = Modifier.padding(vertical = 8.dp)
          ) {
            items(albums) { album ->
              AlbumDropdownItem(
                album = album,
                isSelected = album.id == selectedAlbum?.id,
                onClick = {
                  onAlbumSelected(album)
                  onExpandedChange(false)
                }
              )
            }
          }
        }
      }
    }
  }
}

@Composable
private fun AlbumDropdownItem(
  album: ImageAlbum,
  isSelected: Boolean,
  onClick: () -> Unit,
  modifier: Modifier = Modifier
) {
  val strings = LocalStrings.current

  Surface(
    onClick = onClick,
    modifier = modifier
      .fillMaxWidth()
      .padding(horizontal = 4.dp, vertical = 2.dp),
    color = if (isSelected) AppTheme.colors.primary.copy(alpha = 0.1f) else Color.Transparent,
    shape = RoundedCornerShape(8.dp)
  ) {
    Row(
      modifier = Modifier
        .fillMaxWidth()
        .padding(12.dp),
      verticalAlignment = Alignment.CenterVertically,
      horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
      // Album cover image
      if (album.coverUri != null) {
        CoilImage(
          data = album.coverUri,
          modifier = Modifier
            .size(40.dp)
            .clip(RoundedCornerShape(6.dp)),
          contentScale = ContentScale.Crop
        )
      } else {
        Box(
          modifier = Modifier
            .size(40.dp)
            .clip(RoundedCornerShape(6.dp))
            .background(AppTheme.colors.disabled),
          contentAlignment = Alignment.Center
        ) {
          Text(
            text = "📁",
            style = AppTheme.typography.body2
          )
        }
      }

      // Album info
      Column(
        modifier = Modifier.weight(1f)
      ) {
        Text(
          text = album.name,
          style = AppTheme.typography.body1,
          color = AppTheme.colors.onSurface,
          fontWeight = FontWeight.Medium,
          maxLines = 1,
          modifier = Modifier.basicMarquee()
        )
        Text(
          text = "${album.imageCount} ${strings.image.lowercase()}(s)",
          style = AppTheme.typography.body2,
          color = AppTheme.colors.textSecondary
        )
      }
    }
  }
}
