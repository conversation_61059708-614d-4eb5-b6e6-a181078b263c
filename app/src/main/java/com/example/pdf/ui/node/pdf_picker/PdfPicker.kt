@file:Suppress("FunctionName")

package com.example.pdf.ui.node.pdf_picker

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import com.example.pdf.biz.ad.interstitial.interstitialAdRegister
import com.example.pdf.guia.GlobalNavigator
import com.example.pdf.guia.ScreenNode
import com.example.pdf.guia.UseStatusBarDarkIcons
import com.example.pdf.mvi_ui_model.koinUiModel
import com.example.pdf.ui.feature.loading.LoadingDialog
import com.example.pdf.ui.node.document.pdf.PdfViewerInitEditMode
import com.example.pdf.ui.node.document.pdf.PdfViewerNode
import com.example.pdf.ui.node.pdf_picker.OnPdfPickerItemClickAction.AddSignatureToPdf
import com.example.pdf.ui.node.pdf_picker.OnPdfPickerItemClickAction.AnnotatePdf
import com.example.pdf.ui.node.pdf_picker.OnPdfPickerItemClickAction.LockPdf
import com.example.pdf.ui.node.pdf_picker.OnPdfPickerItemClickAction.UnlockPdf
import com.example.pdf.ui.node.pdf_remove_password.PdfRemovePasswordBottomSheetNode
import com.example.pdf.ui.node.pdf_set_password.PdfSetPasswordBottomSheetNode
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.push
import com.roudikk.guia.extensions.replaceLast
import kotlinx.parcelize.Parcelize
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState
import java.io.File

enum class PdfPickerListType {
  ALL, Encrypted, Unencrypted
}

enum class OnPdfPickerItemClickAction {
  LockPdf, UnlockPdf, AnnotatePdf, AddSignatureToPdf;

  fun executeOnPdfFileClick(pdfFile: File) {
    when (this) {
      LockPdf -> GlobalNavigator.tryTransaction { push(PdfSetPasswordBottomSheetNode(pdfFile)) }
      UnlockPdf -> GlobalNavigator.tryTransaction { push(PdfRemovePasswordBottomSheetNode(pdfFile)) }
      AnnotatePdf -> GlobalNavigator.tryTransaction {
        replaceLast(
          PdfViewerNode(
            pdfFile,
            initEditMode = PdfViewerInitEditMode.Annotate
          )
        )
      }

      AddSignatureToPdf -> GlobalNavigator.tryTransaction {
        replaceLast(
          PdfViewerNode(
            pdfFile,
            initEditMode = PdfViewerInitEditMode.Signature
          )
        )
      }
    }
  }
}

fun LockPdfPickerNode(): PdfPickerNode =
  PdfPickerNode(listType = PdfPickerListType.Unencrypted, itemClickAction = LockPdf)

fun UnlockPdfPickerNode(): PdfPickerNode =
  PdfPickerNode(listType = PdfPickerListType.Encrypted, itemClickAction = UnlockPdf)

fun AnnotatePdfPickerNode(): PdfPickerNode =
  PdfPickerNode(listType = PdfPickerListType.ALL, itemClickAction = AnnotatePdf)

fun AddSignatureToPdfPickerNode(): PdfPickerNode =
  PdfPickerNode(listType = PdfPickerListType.ALL, itemClickAction = AddSignatureToPdf)

@Parcelize
class PdfPickerNode(
  private val listType: PdfPickerListType,
  private val itemClickAction: OnPdfPickerItemClickAction
) : ScreenNode("pdf_picker") {
  @Composable
  override fun Content(navigator: Navigator) {
    UseStatusBarDarkIcons()

    val uiModel: PdfPickerUiModel = koinUiModel { parametersOf(listType) }
    val uiState by uiModel.collectAsState()

    if (uiState.isLoading) {
      LoadingDialog()
    }

    val (onTryToShowInterAdAndNavAction, onBackAction)
      = interstitialAdRegister(navigator)

    PdfPickerContent(
      uiState = uiState,
      onQueryChange = uiModel::onSearch,
      onQueryClear = uiModel::onQueryClear,
      onListTypeChange = uiModel::onListTypeChange,
      onFileClick = {
        if (listType == PdfPickerListType.ALL) {
          onTryToShowInterAdAndNavAction("open_document_file") { itemClickAction.executeOnPdfFileClick(it) }
        } else {
          itemClickAction.executeOnPdfFileClick(it)
        }
      },
      onBookmarkClick = uiModel::onBookmarkClick,
      onBack = onBackAction
    )
  }
}
