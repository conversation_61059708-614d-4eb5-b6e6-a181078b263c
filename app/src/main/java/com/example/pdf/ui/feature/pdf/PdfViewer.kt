package com.example.pdf.ui.feature.pdf

import android.annotation.SuppressLint
import android.net.Uri
import android.util.Log
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import com.multiplatform.webview.web.WebView
import com.multiplatform.webview.web.rememberWebViewState
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.io.encoding.Base64
import kotlin.io.encoding.ExperimentalEncodingApi

private const val BlankPdfBase64 =
  "JVBERi0xLjAKMSAwIG9iajw8L1R5cGUvQ2F0YWxvZy9QYWdlcyAyIDAgUj4+ZW5kb2JqCjIgMCBvYmo8PC9UeXBlL1BhZ2VzL0tpZHNbMyAwIFJdL0NvdW50IDE+PmVuZG9iagozIDAgb2JqPDwvVHlwZS9QYWdlL01lZGlhQm94WzAgMCA1OTUgODQyXS9QYXJlbnQgMiAwIFI+PmVuZG9iagp4cmVmCjAgNAowMDAwMDAwMDAwIDY1NTM1IGYKMDAwMDAwMDAwOSAwMDAwMCBuCjAwMDAwMDAwNTMgMDAwMDAgbgowMDAwMDAwMTAyIDAwMDAwIG4KdHJhaWxlcjw8L1NpemUgNC9Sb290IDEgMCBSPj4Kc3RhcnR4cmVmCjE3MAolJUVPRg=="

@SuppressLint("SetJavaScriptEnabled")
@Composable
fun PDFViewer(
  pdfUri: Uri,
  modifier: Modifier = Modifier,
  onEvaluateJavascriptAfter: ((String?) -> Unit)? = null
) {
  val context = LocalContext.current

  var pdfBase64 by remember { mutableStateOf<String?>(null) }

  LaunchedEffect(pdfUri) {
    pdfBase64 = withContext(Dispatchers.IO) {
      convertInputStreamToBase64(context.contentResolver.openInputStream(pdfUri)!!)
    }
  }

  pdfBase64?.let {
    PDFViewer(
      pdfBase64 = it,
      modifier = modifier,
      onEvaluateJavascriptAfter = onEvaluateJavascriptAfter
    )
  }
}

@Composable
fun PDFViewer(
  pdfBase64: String,
  modifier: Modifier = Modifier,
  onEvaluateJavascriptAfter: ((String?) -> Unit)? = null
) {
  val scope = rememberCoroutineScope()
  val webViewState = rememberWebViewState("https://mozilla.github.io/pdf.js/web/viewer.html")

  WebView(
    state = webViewState,
    modifier = modifier,
    onCreated = { webView ->
      scope.launch {
        delay(500)

        Log.d("PDFViewer", "base64pdf: $pdfBase64")

        val jsCode = "PDFViewerApplication.open({" +
          "url: 'data:application/pdf;base64," + pdfBase64 + "'," +
          "originalUrl: ''," +
          "});"

        webView.evaluateJavascript(jsCode, onEvaluateJavascriptAfter)
      }
    })
}

@Composable
fun PreInitPDFViewer() {
  PDFViewer(pdfBase64 = BlankPdfBase64)
}

@OptIn(ExperimentalEncodingApi::class)
fun convertInputStreamToBase64(inputStream: java.io.InputStream): String {
  return inputStream.use { stream ->
    Base64.encode(stream.readBytes())
  }
}