package com.example.pdf.ui.composable

import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp

@Composable
fun RowScope.BlankSpacer(width: Dp) {
  Spacer(modifier = Modifier.width(width))
}

@Composable
fun ColumnScope.BlankSpacer(height: Dp) {
  Spacer(modifier = Modifier.height(height))
}

@Composable
fun ColumnScope.BlankHeightIn(min: Dp, max: Dp) {
  Spacer(modifier = Modifier.heightIn(min, max))
}

@Composable
fun ColumnScope.BlankWidthIn(min: Dp, max: Dp) {
  Spacer(modifier = Modifier.widthIn(min, max))
}