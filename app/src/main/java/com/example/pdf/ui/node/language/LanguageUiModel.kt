package com.example.pdf.ui.node.language

import android.content.Context
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.os.LocaleListCompat
import androidx.lifecycle.ViewModel
import com.example.pdf.LocaleSupport
import com.example.pdf.biz.PrefStore
import com.example.pdf.biz.notification.FixedNotificationHelper
import com.example.pdf.coroutine.AppCoroutineScope
import com.example.pdf.guia.GlobalNavigator
import com.example.pdf.kermit.debugLog
import com.example.pdf.lyricist.runtimeLanguageTagFlow
import com.example.pdf.ui.node.welcome.WelcomeNode
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.push
import com.roudikk.guia.extensions.replaceLast
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.annotation.KoinViewModel
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container
import java.util.Locale

@KoinViewModel
class LanguageUiModel(
  private val prefStore: PrefStore,
  private val appCoroutineScope: AppCoroutineScope,
  private val fixedNotificationHelper: FixedNotificationHelper,
) : ViewModel(), ContainerHost<LanguageUiState, LanguageSideEffect> {

  override val container: Container<LanguageUiState, LanguageSideEffect> =
    container(LanguageUiState())

  fun onRefresh(locale: Locale) = intent {
    val supportLocaleList = LocaleSupport.localeList
    val selectLocale = LocaleSupport.compatibleLanguage(locale) ?: Locale.ENGLISH

    reduce {
      state.copy(
        selectedLocale = selectLocale,
        localeList = supportLocaleList
      )
    }
  }

  fun onLanguageSelect(locale: Locale) = intent {
    reduce { state.copy(selectedLocale = locale) }
  }

  fun onConfirmLanguage(context: Context) = intent {
    debugLog(tag = "language") { "state.selectedLocale: ${state.selectedLocale}" }
    changeLocale(state.selectedLocale) {
      debugLog(tag = "language") { "changeLocale: $it" }
      fixedNotificationHelper.updateNoti(context)
    }

    val hasCompletedSetup = prefStore.selectLanguageFinish && prefStore.welcomeFinish

    if (hasCompletedSetup.not()) {
      prefStore.saveSelectLanguageFinish()
      GlobalNavigator.tryTransaction {
        push(WelcomeNode())
      }
    } else {
      postSideEffect(LanguageSideEffect.ChangeLanguageAndBack)
    }
  }

  private fun changeLocale(
    locale: Locale,
    onLocaleChanged: ((changeSuccessful: Boolean) -> Unit)? = null
  ) {
    if (locale != Locale.getDefault()) {
      appCoroutineScope.launch {
        val languageTag = locale.toLanguageTag()
        debugLog(tag = "language") { "setApplicationLocales(): $languageTag" }

        runtimeLanguageTagFlow.emit(languageTag)
        launch(Dispatchers.Main.immediate) {
          AppCompatDelegate.setApplicationLocales(LocaleListCompat.forLanguageTags(languageTag))
          onLocaleChanged?.invoke(true)
        }
      }
    } else {
      onLocaleChanged?.invoke(false)
    }
  }

  private fun resetLocale() {
    appCoroutineScope.launch(Dispatchers.Main.immediate) {
      AppCompatDelegate.setApplicationLocales(
        LocaleListCompat.getEmptyLocaleList()
      )
    }
  }

}