package com.bhuvaneshw.pdf.compose.ui

import androidx.compose.foundation.layout.BoxScope
import com.bhuvaneshw.pdf.compose.PdfState

open class PdfContainerScope(
    val pdfState: PdfState
)

class PdfContainerBoxScope(
    pdfState: PdfState,
    private val boxScope: BoxScope
) : PdfContainerScope(pdfState), BoxScope by boxScope

class PdfToolBarScope(
    val pdfState: PdfState,
    val toolBarState: PdfToolBarState,
)
