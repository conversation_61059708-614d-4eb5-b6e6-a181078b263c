package com.example.pdf.ui.feature.image_picker

import android.annotation.SuppressLint
import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import com.example.pdf.PreviewComposable
import com.example.pdf.android.toast.showToast
import com.example.pdf.lyricist.globalStrings
import com.example.pdf.kermit.debugLog
import com.example.pdf.lumo.components.Button
import com.example.pdf.lumo.components.Text

/**
 * A container composable for the Photo Picker functionality that allows selecting multiple images (excluding GIFs)
 * and returns their Uris.
 *
 * @param onPickStarted Called when image picking starts.
 * @param onPickComplete Called when image selection completes successfully with the list of selected image Uris. If the user cancels, the list will be empty.
 * @param modifier Modifier to be applied to the container.
 * @param content Composable content to be displayed as the trigger for image picking.
 */
@Composable
fun ImageUriPickerContainer(
  limit: Int = 99,
  onPickStarted: () -> Unit = {},
  onPickComplete: (List<Uri>) -> Unit,
  @SuppressLint("ModifierParameter") modifier: Modifier = Modifier,
  content: @Composable (startPicker: () -> Unit) -> Unit
) {

  // Create Photo Picker launcher using rememberLauncherForActivityResult for multiple images
  val imagePickerLauncher = rememberLauncherForActivityResult(
    contract = if (limit > 1) ActivityResultContracts.PickMultipleVisualMedia(limit) else ActivityResultContracts.PickVisualMedia()
  ) { result ->
    @Suppress("UNCHECKED_CAST") val uris = when (result) {
      is Uri -> listOf(result)
      is List<*> -> result as? List<Uri> ?: emptyList()
      else -> emptyList()
    }
    onPickComplete(uris)
  }

  // Function to start the photo picker that will be passed to the content
  val startPicker = remember {
    {
      onPickStarted()
      // Launch the picker with ImageOnly to exclude videos and potentially GIFs
      // Note: System behavior regarding GIFs might vary. Filtering might be needed post-selection if GIFs are still included.
      imagePickerLauncher.launch(
        PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly)
      )
    }
  }

  Box(
    modifier = modifier,
    contentAlignment = Alignment.Center
  ) {
    // Pass the startPicker function to the content
    content(startPicker)
  }
}


@Preview(showBackground = true)
@Composable
fun ImageUriPickerContainerPreview() {
  val context = LocalContext.current

  PreviewComposable {
    ImageUriPickerContainer(
      onPickStarted = { },
      onPickComplete = { uris ->
        if (uris.isNotEmpty()) {
          showToast(globalStrings.selectedImages.format(uris.size))
          // You can process the selected uris here
          uris.forEach { uri ->
            debugLog(message = "Selected image URI: $uri")
          }
        } else {
          showToast(globalStrings.imageSelectionCancelled)
        }
      }
    ) { startPicker ->
      Button(onClick = startPicker) {
        Text("Select Images (Uri)")
      }
    }
  }
}