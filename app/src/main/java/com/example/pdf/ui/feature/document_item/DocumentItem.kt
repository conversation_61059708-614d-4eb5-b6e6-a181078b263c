package com.example.pdf.ui.feature.document_item

import android.annotation.SuppressLint
import androidx.compose.animation.Crossfade
import androidx.compose.foundation.Image
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.Circle
import androidx.compose.material.icons.rounded.Bookmark
import androidx.compose.material.icons.rounded.BookmarkBorder
import androidx.compose.material.icons.rounded.CheckCircle
import androidx.compose.material.icons.rounded.MoreHoriz
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.pdf.PreviewComposable
import com.example.pdf.R
import com.example.pdf.lumo.AppColors
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.HorizontalDivider
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.IconButton
import com.example.pdf.lumo.components.IconButtonVariant
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.card.Card
import com.example.pdf.lumo.components.card.CardDefaults
import com.example.pdf.lumo.grayTint5f
import com.example.pdf.lumo.grayTint7f
import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toJavaLocalDate
import kotlinx.datetime.toLocalDateTime
import java.io.File
import java.util.Locale

data class DocumentArgs(
  val fileName: String,
  val fileSize: String,
  val lastModified: String,
  val fileExtension: String,
)

@SuppressLint("UnusedCrossfadeTargetStateParameter")
@Composable
fun DocumentItem(
  file: File,
  modifier: Modifier = Modifier,
  isSelectionMode: Boolean = false,
  isSelected: Boolean = false,
  isBookmarked: Boolean = false,
  isShowBookmarkIcon: Boolean = true,
  isShowMoreIcon: Boolean = true,
  isShowHorizontalDivider: Boolean = true,
  useMarqueeWithFileName: Boolean = false,
  containerColor: Color = if (isSelected) AppTheme.colors.primary.copy(0.08f) else AppColors.background,
  onBookmarkClick: (file: File, bookmarked: Boolean) -> Unit = { _, _ -> },
  onMoreClick: (file: File) -> Unit = {},
  onSelect: (file: File, selected: Boolean) -> Unit = { _, _ -> },
  onClick: (file: File) -> Unit = {},
  onLongClick: (file: File) -> Unit = {},
) {
  val documentArgs = remember(file) {
    DocumentArgs(
      fileName = file.name,
      fileSize = formatFileSize(file.length()),
      lastModified = formatDate(file.lastModified()),
      fileExtension = file.extension.lowercase(Locale.ROOT)
    )
  }

  val documentPainter = matchFileIconPainter(documentArgs.fileExtension)

  Crossfade(documentArgs.fileName) {
    Card(
      colors = CardDefaults.cardColors(
        containerColor = containerColor
      ),
      shape = RectangleShape,
      modifier = modifier
        .fillMaxWidth()
        .combinedClickable(
          onClick = {
            if (isSelectionMode) {
              onSelect(file, !isSelected)
            } else {
              onClick(file)
            }
          },
          onLongClick = { onLongClick(file) }),
    ) {
      Row(
        modifier = Modifier
          .fillMaxWidth()
          .padding(start = 16.dp, end = 10.dp)
          .padding(vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically
      ) {
        if (isSelectionMode) {
          Icon(
            imageVector = if (isSelected) Icons.Rounded.CheckCircle else Icons.Outlined.Circle,
            tint = AppTheme.colors.primary,
            modifier = Modifier.padding(start = 8.dp, end = 14.dp)
          )
        }

        Image(painter = documentPainter, contentDescription = null, modifier = Modifier.size(48.dp))

        Spacer(modifier = Modifier.width(8.dp))

        // File details
        Column(
          modifier = Modifier.weight(1f)
        ) {
          Text(
            text = documentArgs.fileName,
            maxLines = 1,
            style = AppTheme.typography.body1.copy(fontWeight = FontWeight.Medium),
            overflow = TextOverflow.Ellipsis,
            modifier = if (useMarqueeWithFileName) Modifier.basicMarquee() else Modifier
          )

          Text(
            text = "${documentArgs.fileSize} | ${documentArgs.lastModified}",
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            style = AppTheme.typography.body2,
            color = grayTint7f
          )
        }

        if (isShowBookmarkIcon) {
          Spacer(modifier = Modifier.width(4.dp))

          IconButton(
            variant = IconButtonVariant.Ghost,
            onClick = { onBookmarkClick(file, !isBookmarked) },
            modifier = Modifier.size(34.dp)
          ) {
            Icon(
              painter = if (isBookmarked)
                painterResource(R.drawable.ic_bookmark_selected)
              else
                painterResource(R.drawable.ic_bookmark),
              contentDescription = "Bookmark",
              tint = if (isBookmarked) AppColors.primary else grayTint5f
            )
          }
        }

        if (!isSelectionMode) {
          if (isShowMoreIcon) {
            Spacer(modifier = Modifier.width(2.dp))

            IconButton(
              onClick = { onMoreClick(file) },
              variant = IconButtonVariant.Ghost,
              modifier = Modifier.size(34.dp)
            ) {
              Icon(
                painter = painterResource(R.drawable.ic_more),
                contentDescription = "More Options",
                tint = grayTint5f
              )
            }
          }
        }
      }

      if (isShowHorizontalDivider) {
        if (isSelectionMode) {
          HorizontalDivider(Modifier.padding(start = 66.dp, end = 6.dp))
        } else {
          HorizontalDivider()
        }
      }
    }
  }

}

// Helper function to format file size
fun formatFileSize(size: Long): String {
  if (size < 1024) {
    return "$size B"
  }

  val kb = size / 1024.0
  return if (kb < 1024) {
    String.format(Locale.US, "%.2f KB", kb)
  } else {
    String.format(Locale.US, "%.2f MB", kb / 1024)
  }
}

// Helper function to format date
fun formatDate(timestamp: Long): String {
  val instant = Instant.fromEpochMilliseconds(timestamp)
  val localDate = instant.toLocalDateTime(TimeZone.currentSystemDefault()).date
  return java.time.format.DateTimeFormatter
    .ofLocalizedDate(java.time.format.FormatStyle.MEDIUM)
    .withLocale(Locale.getDefault())
    .format(localDate.toJavaLocalDate())
}

@Composable
private fun matchFileIconPainter(fileExtension: String): Painter {
  return when (fileExtension) {
    "pdf" -> {
      painterResource(R.drawable.ic_pdf)
    }

    in listOf("doc", "docx") -> {
      painterResource(R.drawable.ic_word)
    }

    in listOf("xls", "xlsx") -> {
      painterResource(R.drawable.ic_excel)
    }

    in listOf("ppt", "pptx") -> {
      painterResource(R.drawable.ic_ppt)
    }

    else -> {
      painterResource(R.drawable.ic_word)
    }
  }
}

@Preview(locale = "en")
@Composable
private fun DocumentItemPreview() {
  PreviewComposable {
    DocumentItem(
      file = File.createTempFile("lalala", ".docx"),
      isBookmarked = true,
      isSelected = true,
      isSelectionMode = true
    )
  }
}