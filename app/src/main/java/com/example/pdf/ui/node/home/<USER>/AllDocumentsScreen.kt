package com.example.pdf.ui.node.home.all

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.PrimaryTabRow
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRowDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.view.HapticFeedbackConstantsCompat
import androidx.core.view.ViewCompat
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.PreviewComposable
import com.example.pdf.R
import com.example.pdf.android.file.DocumentType
import com.example.pdf.android.file.rememberDocumentTypeItemDataList
import com.example.pdf.biz.ad.interstitial.OnTryToShowInterAdAndNavAction
import com.example.pdf.biz.ad.nat1ve.NativeAd
import com.example.pdf.biz.ad.nat1ve.NativeAdPlace
import com.example.pdf.guia.NavigateAction
import com.example.pdf.lumo.AppColors
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.Scaffold
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.card.Card
import com.example.pdf.lumo.components.card.CardDefaults
import com.example.pdf.mvi_ui_model.koinUiModel
import com.example.pdf.ui.composable.BlankSpacer
import com.example.pdf.ui.feature.document_item.DocumentItem
import com.example.pdf.ui.feature.document_node.DocumentNode
import com.example.pdf.ui.node.document_more_action.DocumentMoreActionNode
import com.example.pdf.ui.node.documents_sort_settings.DocumentsSortSettingsNode
import com.example.pdf.ui.node.file_access_requester.FileAccessTipsPanel
import com.example.pdf.ui.node.file_access_requester.FileAccessTipsPanelType
import com.example.pdf.ui.node.home.EmptyFileListPlaceholder
import com.example.pdf.ui.node.home.EmptyFileListPlaceholderForBookmarks
import com.example.pdf.ui.node.home.HomeTab
import com.example.pdf.ui.node.search.SearchNode
import com.example.pdf.ui.node.selection.SelectionNode
import com.example.pdf.ui.node.selection.SelectionNodeArgs
import com.example.pdf.ui.shape.TriangleShape
import com.roudikk.guia.extensions.push
import com.roudikk.guia.extensions.requireLocalNavigator
import org.orbitmvi.orbit.compose.collectAsState
import java.io.File

@Composable
fun AllDocumentsScreen(
  onTryToShowInterAdAndNavAction: OnTryToShowInterAdAndNavAction
) {
  val view = LocalView.current

  val navigator = requireLocalNavigator()
  val uiModel: AllDocumentsUiModel = koinUiModel()
  val uiState by uiModel.collectAsState()

  DocumentsContent(
    adPlace = NativeAdPlace.All,
    uiState = uiState,
    onDocumentTypeChange = uiModel::onDocumentTypeChange,
    onDocumentClick = { document ->
      DocumentNode(document)?.let {
        onTryToShowInterAdAndNavAction("open_document_file") { push(it) }
      }
    },
    onBookmarkClick = uiModel::onBookMarkDocument,
    onMoreClick = {
      navigator.push(DocumentMoreActionNode(it))
    },
    onSortIconClick = {
      navigator.push(DocumentsSortSettingsNode())
    },
    onSelectionModeSwitch = {
      navigator.push(
        SelectionNode(
          args = SelectionNodeArgs(
            displayFiles = uiState.displayFiles,
            selectedFiles = emptyList()
          )
        )
      )
    },
    onDocumentItemLongClick = {
      navigator.push(
        SelectionNode(
          args = SelectionNodeArgs(
            displayFiles = uiState.displayFiles,
            selectedFiles = listOf(it)
          )
        )
      )
      ViewCompat.performHapticFeedback(
        view,
        HapticFeedbackConstantsCompat.SEGMENT_FREQUENT_TICK
      )
    },
    onSearchBarClick = { navigator.push(SearchNode()) },
  )
}

val DocumentsTopTabHeight = 42.dp

@Composable
fun DocumentsContent(
  adPlace: NativeAdPlace?,
  uiState: DocumentsUiState,
  onDocumentTypeChange: (DocumentType) -> Unit,
  onDocumentClick: (File) -> Unit,
  onBookmarkClick: (File, Boolean) -> Unit,
  onMoreClick: (File) -> Unit,
  onSortIconClick: () -> Unit,
  onSelectionModeSwitch: () -> Unit,
  onDocumentItemLongClick: (File) -> Unit,
  onSearchBarClick: () -> Unit,
) {
  val tabIndex = remember(uiState.selectDocumentType) {
    uiState.selectDocumentType.ordinal
  }

  val documentTypeItemDataList = rememberDocumentTypeItemDataList()

  val listState = rememberLazyListState()

  Scaffold(
    topBar = {
      DocumentsToolBar(
        onSortIconClick = onSortIconClick,
        onSelectionModeIconClick = { onSelectionModeSwitch() },
        onSearchBarClick = onSearchBarClick
      )
    }
  ) {
    LazyColumn(
      state = listState,
      modifier = Modifier
        .fillMaxSize()
        .padding(top = it.calculateTopPadding())
    ) {

      item {
        PrimaryTabRow(
          selectedTabIndex = tabIndex,
          containerColor = AppTheme.colors.primary,
          indicator = {
            TabRowDefaults.PrimaryIndicator(
              modifier = Modifier.tabIndicatorOffset(tabIndex, matchContentSize = true),
              width = 14.dp,
              height = 8.dp,
              shape = TriangleShape,
              color = AppTheme.colors.onPrimary
            )
          },
          divider = {},
        ) {
          documentTypeItemDataList.forEachIndexed { index, dtid ->
            Tab(
              selected = tabIndex == index,
              onClick = { onDocumentTypeChange(dtid.type) },
              modifier = Modifier.height(DocumentsTopTabHeight),
              selectedContentColor = AppTheme.colors.onPrimary,
              text = {
                Text(
                  text = dtid.label.uppercase(),
                  style = AppTheme.typography.label1,
                  color = AppTheme.colors.onPrimary,
                  modifier = Modifier.padding(bottom = 2.dp)
                )
              }
            )
          }
        }
      }

      if (uiState.displayFiles.isNotEmpty()) {
        item {
          FileAccessTipsPanel(
            panelType = FileAccessTipsPanelType.Item,
            modifier = Modifier
              .fillMaxWidth()
              .padding(horizontal = 16.dp, vertical = 8.dp)
          )
        }


        val adItem = {
          adPlace?.let {
            item("AD_ITEM") {
              NativeAd(adPlace = it)
            }
          }
        }

        uiState.displayFiles.forEachIndexed { index, documentFile ->

          item {
            val isBookmarked = uiState.bookmarkedFilePaths.contains(documentFile.absolutePath)

            DocumentItem(
              file = documentFile,
              isSelectionMode = false,
              isSelected = false,
              isBookmarked = isBookmarked,
              isShowBookmarkIcon = isBookmarked,
//        onBookmarkClick = onBookmarkClick,
              onMoreClick = onMoreClick,
              onClick = onDocumentClick,
              onLongClick = onDocumentItemLongClick,
              modifier = Modifier.animateItem()
            )
          }

          if (index == minOf(2, uiState.displayFiles.size - 1)) {
            adItem()
          }
        }

        item { Spacer(modifier = Modifier.height(116.dp)) }
      } else {
        item {
          when (uiState.homeTab) {
            HomeTab.ALL -> {
              documentTypeItemDataList.find { data -> data.type == uiState.selectDocumentType }
                ?.let { matchData ->
                  EmptyFileListPlaceholder(
                    documentTypeItemData = matchData,
                    modifier = Modifier.fillParentMaxSize()
                  )
                }
            }

            HomeTab.BOOKMARKS -> {
              EmptyFileListPlaceholderForBookmarks(modifier = Modifier.fillParentMaxSize())
            }

            else -> {}
          }
        }
      }
    }

    if (uiState.displayFiles.isEmpty()) {
      FileAccessTipsPanel(
        modifier = Modifier
          .fillMaxSize()
          .padding(top = it.calculateTopPadding())
          .padding(top = DocumentsTopTabHeight)
      )
    }
  }

}

@Composable
fun DocumentsToolBar(
  modifier: Modifier = Modifier,
  onSortIconClick: (() -> Unit)? = null,
  onSelectionModeIconClick: () -> Unit,
  onSearchBarClick: () -> Unit,
) {
  val strings = LocalStrings.current

  Box(
    modifier = modifier.background(color = AppTheme.colors.primary),
  ) {
    Row(
      modifier = Modifier
        .fillMaxWidth()
        .statusBarsPadding()
        .padding(horizontal = 16.dp)
        .padding(top = 8.dp, bottom = 8.dp),
      verticalAlignment = Alignment.CenterVertically,
    ) {
      Card(
        onClick = onSearchBarClick,
        modifier = Modifier.weight(1f),
        colors = CardDefaults.cardColors(contentColor = AppColors.secondary)
      ) {
        Row(
          verticalAlignment = Alignment.CenterVertically,
          modifier = Modifier.padding(horizontal = 12.dp)
        ) {
          Icon(painter = painterResource(R.drawable.ic_search))

          Text(
            text = strings.searchPlaceholder,
            modifier = Modifier.padding(vertical = 10.dp, horizontal = 4.dp),
            style = AppTheme.typography.label1,
            textAlign = TextAlign.Center
          )
        }
      }

      Spacer(Modifier.width(12.dp))

      onSortIconClick?.let {
        Box(
          modifier = Modifier
            .background(AppTheme.colors.background, CardDefaults.Shape)
            .clip(CardDefaults.Shape)
            .clickable(onClick = onSortIconClick)
        ) {
          Image(
            painter = painterResource(R.drawable.ic_list_ordering),
            contentDescription = null,
            modifier = Modifier.padding(7.dp)
          )
        }

        BlankSpacer(12.dp)
      }


      Box(
        modifier = Modifier
          .background(AppTheme.colors.background, CardDefaults.Shape)
          .clip(CardDefaults.Shape)
          .clickable(onClick = onSelectionModeIconClick)
      ) {
        Image(
          painter = painterResource(R.drawable.ic_multi_check),
          contentDescription = null,
          modifier = Modifier.padding(7.dp)
        )
      }
    }
  }
}


@Preview
@Composable
private fun AllDocumentsContentPreview() {
  PreviewComposable {
    val files = listOf(
      File.createTempFile("lalala", ".docx"),
      File.createTempFile("lalala", ".ppt"),
      File.createTempFile("lalala", ".pdf"),
    )

    var documentType by remember { mutableStateOf(DocumentType.ALL) }

    DocumentsContent(
      adPlace = null,
      uiState = DocumentsUiState(
        displayFiles = files,
        selectDocumentType = documentType,
      ),
      onDocumentTypeChange = { documentType = it },
      onDocumentClick = { },
      onBookmarkClick = { _, _ -> },
      onMoreClick = { },
      onSortIconClick = { },
      onSelectionModeSwitch = { },
      onDocumentItemLongClick = { },
      onSearchBarClick = { }
    )
  }
}