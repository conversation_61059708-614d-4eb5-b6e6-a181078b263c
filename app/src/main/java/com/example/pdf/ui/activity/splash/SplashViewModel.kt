package com.example.pdf.ui.activity.splash

import android.app.Activity
import android.content.Intent
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.pdf.AppActivity
import com.example.pdf.appActivity
import com.example.pdf.biz.PrefStore
import com.example.pdf.biz.ad.appopen.AppOpenAdFacade
import com.example.pdf.biz.ad.appopen.AppOpenAdLoadingStateEvent
import com.example.pdf.biz.ad.appopen.AppOpenAdShowStateEvent
import com.example.pdf.biz.ad.interstitial.InterstitialAdFacade
import com.example.pdf.biz.notification.NotificationActionNavigator
import com.example.pdf.biz.remoteconfig.RealRemoteConfig
import com.example.pdf.biz.skipSplash
import com.example.pdf.biz.ump.UserMessagingPlatformManager
import com.example.pdf.configureTradPlusSdk
import com.example.pdf.kermit.debugLog
import com.example.pdf.ui.activity.SplashLaunchType
import com.example.pdf.ui.activity.splashActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.android.annotation.KoinViewModel
import org.koin.core.annotation.InjectedParam
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

@KoinViewModel
class SplashViewModel(
  @InjectedParam private val launchType: SplashLaunchType,
  private val prefStore: PrefStore,
  private val appOpenAdFacade: AppOpenAdFacade,
  private val interstitialAdFacade: InterstitialAdFacade,
  private val remoteConfig: RealRemoteConfig,
  private val userMessagingPlatformManager: UserMessagingPlatformManager
) : ViewModel(), ContainerHost<SplashViewState, SplashSideEffect> {

  override val container: Container<SplashViewState, SplashSideEffect> =
    container(SplashViewState())

  init {
    splashActivity?.intent?.let(NotificationActionNavigator::handleNotificationClick)
    configureAppOpenAd()
  }

  private var hasRequestConsentAndConfigureAds = false

  fun requestConsentAndConfigureAds(
    activity: Activity,
  ) {
    debugLog(tag = "SplashViewModel") { "requestConsentAndConfigureAds() hasRequestConsentAndConfigureAds: $hasRequestConsentAndConfigureAds" }
    if (hasRequestConsentAndConfigureAds) return

    debugLog(tag = "SplashViewModel") { "requestConsentAndConfigureAds() call in" }

    userMessagingPlatformManager.requestConsentInfoUpdate(activity) {
      onLoadAds(activity)

      GlobalScope.launch {
        configureTradPlusSdk(activity.applicationContext)
      }
    }

    hasRequestConsentAndConfigureAds = true
  }

  private fun onLoadAds(
    activity: Activity
  ) {
    viewModelScope.launch(Dispatchers.Main.immediate) {
      appOpenAdFacade.tryToShowAd(activity)
    }
  }

  private fun configureAppOpenAd() {
    appOpenAdFacade.mergedAdLoadingStateEventFlow.onEach {
      when (it) {
        AppOpenAdLoadingStateEvent.FailedToLoad -> {
          delay(1000)
          onNavUp()
        }

        AppOpenAdLoadingStateEvent.TimeOut -> {
          onNavUp()
        }

        AppOpenAdLoadingStateEvent.Loaded -> {
          onShowAdmobAppOpenAd()
        }
      }
    }.launchIn(viewModelScope)


    appOpenAdFacade.mergedAdShowStateEventFlow.onEach {
      when (it) {
        AppOpenAdShowStateEvent.FailedToShow,
        AppOpenAdShowStateEvent.Finish -> {
          onNavUp()
        }

        AppOpenAdShowStateEvent.SkipToShow -> {
          delay(1000)
          onNavUp()
        }

        AppOpenAdShowStateEvent.Showing -> {
          intent {
            delay(500)
            reduce { state.copy(appOpenAdShowing = true) }
          }
        }

      }
    }.launchIn(viewModelScope)
  }

  private fun onShowAdmobAppOpenAd() = intent {
    postSideEffect(SplashSideEffect.ShowAdmobAppOpenAd)
  }

  private fun onNavUp() = intent {
    val handleIntentAction = suspend {
      debugLog(tag = "SplashViewModel") { "onNavUp() handleIntentAction" }
      withContext(Dispatchers.Main.immediate) {
        NotificationActionNavigator.handleIntentAction(
          intent = splashActivity?.intent,
          isActivityInForeground = true
        )
      }
    }

    debugLog(tag = "SplashViewModel") { "onNavUp() launchType: $launchType" }

    when (launchType) {
      // real cold launch & launch by launcher
      SplashLaunchType.COLD_START -> {
        withContext(Dispatchers.Main.immediate) {
          if (appActivity == null) {
            debugLog(tag = "SplashViewModel") { "real COLD_START" }
            splashActivity?.let {
              it.startActivity(Intent(it, AppActivity::class.java))
            }
          } else {
            skipSplash()
            debugLog(tag = "SplashViewModel") { "launch by launcher" }
          }
        }
      }

      SplashLaunchType.WARM_START -> {
        skipSplash()
      }

      SplashLaunchType.NOTIFICATION_CLICK -> {
        if (appActivity == null) {
          debugLog(tag = "SplashViewModel") { "NOTIFICATION_CLICK onNavUp() appActivity == null" }

          withContext(Dispatchers.Main.immediate) {
            splashActivity?.let {
              it.startActivity(Intent(it, AppActivity::class.java))
              handleIntentAction()
            }
          }
        } else {
          debugLog(tag = "SplashViewModel") { "NOTIFICATION_CLICK onNavUp() appActivity != null" }

          handleIntentAction()
          skipSplash()
        }
      }
    }


    postSideEffect(SplashSideEffect.NavUp)
  }

  override fun onCleared() {
    runCatching { appActivity?.let(interstitialAdFacade::tryToLoadAd) }
    super.onCleared()

    debugLog(tag = "SplashViewModel") { "onCleared()" }
  }
}