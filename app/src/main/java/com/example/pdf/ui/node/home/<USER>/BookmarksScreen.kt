package com.example.pdf.ui.node.home.bookmarks

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.platform.LocalView
import androidx.core.view.HapticFeedbackConstantsCompat
import androidx.core.view.ViewCompat
import com.example.pdf.biz.ad.interstitial.OnTryToShowInterAdAndNavAction
import com.example.pdf.biz.ad.nat1ve.NativeAdPlace
import com.example.pdf.guia.NavigateAction
import com.example.pdf.mvi_ui_model.koinUiModel
import com.example.pdf.ui.feature.document_node.DocumentNode
import com.example.pdf.ui.node.document_more_action.DocumentMoreActionNode
import com.example.pdf.ui.node.documents_sort_settings.DocumentsSortSettingsNode
import com.example.pdf.ui.node.home.all.DocumentsContent
import com.example.pdf.ui.node.search.SearchNode
import com.example.pdf.ui.node.selection.SelectionNode
import com.example.pdf.ui.node.selection.SelectionNodeArgs
import com.roudikk.guia.extensions.push
import com.roudikk.guia.extensions.requireLocalNavigator
import org.orbitmvi.orbit.compose.collectAsState

@Composable
fun BookmarksScreen(
  onTryToShowInterAdAndNavAction: OnTryToShowInterAdAndNavAction
) {
  val view = LocalView.current
  val navigator = requireLocalNavigator()
  val uiModel: BookmarksUiModel = koinUiModel()
  val uiState by uiModel.collectAsState()

  DocumentsContent(
    adPlace = NativeAdPlace.Bookmarks,
    uiState = uiState,
    onDocumentTypeChange = uiModel::onDocumentTypeChange,
    onDocumentClick = { document ->
      DocumentNode(document)?.let {
        onTryToShowInterAdAndNavAction("open_document_file") { push(it) }
      }
    },
    onBookmarkClick = uiModel::onBookMarkDocument,
    onMoreClick = {
      navigator.push(DocumentMoreActionNode(it))
    },
    onSortIconClick = {
      navigator.push(DocumentsSortSettingsNode())
    },
    onSelectionModeSwitch = {
      navigator.push(
        SelectionNode(
          args = SelectionNodeArgs(
            displayFiles = uiState.displayFiles,
            selectedFiles = emptyList()
          )
        )
      )
    },
    onDocumentItemLongClick = {
      navigator.push(
        SelectionNode(
          args = SelectionNodeArgs(
            displayFiles = uiState.displayFiles,
            selectedFiles = listOf(it)
          )
        )
      )

      ViewCompat.performHapticFeedback(
        view,
        HapticFeedbackConstantsCompat.SEGMENT_FREQUENT_TICK
      )
    },
    onSearchBarClick = { navigator.push(SearchNode()) }
  )
}
