//package com.example.pdf.ui.feature.pdf
//
//import androidx.compose.foundation.layout.Column
//import androidx.compose.foundation.layout.Spacer
//import androidx.compose.foundation.layout.fillMaxWidth
//import androidx.compose.foundation.layout.height
//import androidx.compose.foundation.layout.padding
//import androidx.compose.material3.Text
//import androidx.compose.runtime.Composable
//import androidx.compose.runtime.LaunchedEffect
//import androidx.compose.runtime.getValue
//import androidx.compose.runtime.mutableStateOf
//import androidx.compose.runtime.remember
//import androidx.compose.runtime.setValue
//import androidx.compose.ui.Modifier
//import androidx.compose.ui.platform.LocalContext
//import androidx.compose.ui.unit.dp
//import com.example.pdf.lumo.AppTheme
//import com.example.pdf.ui.node.document.pdf.PdfViewerUiState
//import kotlinx.coroutines.launch
//import java.io.File
//
///**
// * 如何将缩略图列表集成到现有的PDF查看器中
// */
//@Composable
//fun PdfViewerWithThumbnailsIntegration(
//    document: File,
//    pdfViewerUiState: PdfViewerUiState,
//    onPageSelected: (Int) -> Unit,
//    modifier: Modifier = Modifier
//) {
//    val context = LocalContext.current
//
//    // 缩略图列表
//    var thumbnails by remember { mutableStateOf<List<PdfThumbnailItem>>(emptyList()) }
//
//    // 在协程中加载缩略图
//    LaunchedEffect(document) {
//        launch {
//            // 如果PdfState可用，使用它来生成缩略图
//            if (pdfViewerUiState.pdfState != null) {
//                thumbnails = PdfThumbnailGenerator.generateThumbnails(
//                    pdfState = pdfViewerUiState.pdfState
//                )
//            } else {
//                // 否则直接从文件生成
//                thumbnails = PdfThumbnailGenerator.generateThumbnails(
//                    pdfFile = document
//                )
//            }
//        }
//    }
//
//    Column(modifier = modifier) {
//        // 这里是您现有的PDF查看器内容
//        // ...
//
//        // 添加缩略图部分
//        if (thumbnails.isNotEmpty()) {
//            Spacer(modifier = Modifier.height(16.dp))
//
//            Text(
//                text = "页面",
//                style = AppTheme.typography.h3,
//                modifier = Modifier.padding(horizontal = 16.dp)
//            )
//
//            Spacer(modifier = Modifier.height(8.dp))
//
//            // 缩略图水平列表
//            PdfThumbnailHorizontalList(
//                thumbnails = thumbnails,
//                selectedPageIndex = pdfViewerUiState.currentPage - 1, // 转换为0索引
//                onThumbnailSelected = { pageIndex ->
//                    // 调用回调函数，通知页面选择
//                    onPageSelected(pageIndex + 1) // 转换回1索引
//                },
//                modifier = Modifier.fillMaxWidth()
//            )
//        }
//    }
//}
//
///**
// * 如何在现有的PDF查看器中使用缩略图列表
// *
// * 在您的PdfViewer.kt文件中，您可以这样集成：
// *
// * ```
// * @Composable
// * private fun PdfViewerContent(
// *   document: File,
// *   commonDocumentUiState: CommonDocumentUiState,
// *   pdfViewerUiState: PdfViewerUiState,
// *   onBack: () -> Unit,
// *   // ... 其他参数
// * ) {
// *   Scaffold(
// *     // ... 您现有的代码
// *   ) { paddingValues ->
// *     Column(
// *       modifier = Modifier
// *         .fillMaxSize()
// *         .padding(paddingValues)
// *     ) {
// *       // 主PDF查看器
// *       pdfViewerUiState.pdfState?.let {
// *         PdfViewerContainer(
// *           // ... 您现有的代码
// *           modifier = Modifier
// *             .weight(1f)
// *             .fillMaxWidth()
// *         )
// *       }
// *
// *       // 添加缩略图列表
// *       PdfViewerWithThumbnailsIntegration(
// *         document = document,
// *         pdfViewerUiState = pdfViewerUiState,
// *         onPageSelected = { pageIndex ->
// *           // 导航到选中的页面
// *           pdfViewerUiState.pdfState?.pdfViewer?.goToPage(pageIndex)
// *         },
// *         modifier = Modifier.fillMaxWidth()
// *       )
// *     }
// *   }
// * }
// * ```
// */
