package com.example.pdf.ui.composable

import android.annotation.SuppressLint
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed

@SuppressLint("ModifierFactoryUnreferencedReceiver")
inline fun Modifier.noRippleClickable(
  enabled: Boolean = true,
  crossinline onClick: () -> Unit
) = composed {
  clickable(
    enabled = enabled,
    indication = null,
    interactionSource = remember { MutableInteractionSource() }
  ) {
    onClick()
  }
}