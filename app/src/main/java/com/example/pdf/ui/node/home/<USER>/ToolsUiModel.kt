package com.example.pdf.ui.node.home.tools

import android.net.Uri
import android.os.Environment
import androidx.core.net.toFile
import com.example.pdf.android.file.ImportDocumentFilesHelper
import com.example.pdf.android.permission.FileAccessManager
import com.example.pdf.android.toast.showToast
import com.example.pdf.lyricist.globalStrings
import com.example.pdf.guia.GlobalNavigator
import com.example.pdf.kermit.debugLog
import com.example.pdf.mvi_ui_model.UiModel
import com.example.pdf.ui.node.convert_success.ScanSuccessNode
import com.example.pdf.ui.node.image_picker.ImageAlbumManager
import com.roudikk.guia.extensions.push
import org.koin.android.annotation.KoinViewModel
import java.io.File

@KoinViewModel
class ToolsUiModel(
  private val importDocumentFilesHelper: ImportDocumentFilesHelper,
  private val fileAccessManager: FileAccessManager,
  private val imageAlbumManager: ImageAlbumManager
) : UiModel<ToolsUiState, Nothing>(ToolsUiState()) {

  init {
    imageAlbumManager.onFetchImages()
  }

  fun onScanPdfComplete(
    pdfUri: Uri
  ) = intent {
    val internalDocumentDir = importDocumentFilesHelper.getInternalDocumentDirectory()
    val publicDocumentDir =
      Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)

    val hasFileAccessPermission = fileAccessManager.hasFileAccessPermission()

    val targetDir = if (hasFileAccessPermission) {
      publicDocumentDir
    } else {
      internalDocumentDir
    }

    try {
      // Ensure the target directory exists
      if (!targetDir.exists()) {
        targetDir.mkdirs()
      }

      val timestamp = System.currentTimeMillis()
      val newFileName = "PDF_Scanned_$timestamp.pdf"
      val destinationFile = File(targetDir, newFileName)

      pdfUri.toFile().copyTo(destinationFile, overwrite = true)
      val savedLocationMessage = if (hasFileAccessPermission) {
        globalStrings.savedToDocuments.format(destinationFile.absolutePath)
      } else {
        globalStrings.savedToInternal.format(destinationFile.absolutePath)
      }
      showToast(savedLocationMessage)

      GlobalNavigator.transaction {
        push(ScanSuccessNode(destinationFile))
      }

    } catch (e: Exception) {
      debugLog(tag = "WordToPdfUiModel", message = e.stackTraceToString())
      showToast(globalStrings.failedToSavePdf.format(e.localizedMessage))
    } finally {
      // Always update the processing state, regardless of success or failure
      reduce { state.copy(processing = false) }
    }


  }

}