package com.example.pdf.ui.node.splash

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.pdf.PreviewComposable
import com.example.pdf.R
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.LocalColors
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.progressindicators.LinearProgressIndicator

/**
 * Reusable splash content composable
 * Note: SplashNode has been replaced with SplashActivity
 * This composable is kept for potential reuse in other contexts
 */

@Composable
fun SplashContent() {
  Box(
    modifier = Modifier
      .fillMaxSize()
      .background(color = LocalColors.current.background)
  ) {
    Column(
      horizontalAlignment = Alignment.CenterHorizontally,
      modifier = Modifier
        .matchParentSize()
    ) {
      Spacer(modifier = Modifier.weight(5f))

      Box(contentAlignment = Alignment.Center) {
        Image(painter = painterResource(R.drawable.bg_splash), contentDescription = null)
        Image(
          painter = painterResource(R.drawable.logo),
          contentDescription = "App Icon",
          modifier = Modifier.size(80.dp)
        )

        Text(
          text = stringResource(R.string.app_name),
          style = AppTheme.typography.h2.copy(fontWeight = FontWeight.ExtraBold, fontSize = 21.sp),
          modifier = Modifier.padding(top = 136.dp)
        )
      }

      Spacer(modifier = Modifier.weight(5f))

      LinearProgressIndicator(
        modifier = Modifier
          .height(12.dp)
          .padding(horizontal = 36.dp),
        color = AppTheme.colors.primary,
        trackColor = AppTheme.colors.primary.copy(.1f)
      )

      Spacer(modifier = Modifier.weight(3f))
    }
  }
}


@Preview
@Composable
private fun SplashContentPreview() {
  PreviewComposable {
    SplashContent()
  }
}