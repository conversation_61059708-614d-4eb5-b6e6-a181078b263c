package com.example.pdf.ui.node.img2pdf

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.painterResource
import com.example.pdf.R
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.ui.feature.bottomsheet.permission.TipsBottomSheetContent
import com.holix.android.bottomsheetdialog.compose.BottomSheetDialog

@Composable
fun DeletePageTipsBottomSheet(
  onConfirm: () -> Unit,
  onDismiss: () -> Unit
) {
  BottomSheetDialog(onDismissRequest = onDismiss) {
    TipsBottomSheetContent(
      title = LocalStrings.current.deletePageTitle,
      description = LocalStrings.current.deletePageWarning,
      iconPainter = painterResource(R.drawable.img_editor_delete),
      confirmText = LocalStrings.current.delete,
      onConfirm = onConfirm,
      onCancel = onDismiss
    )
  }
}

