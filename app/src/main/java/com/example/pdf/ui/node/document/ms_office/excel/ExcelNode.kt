package com.example.pdf.ui.node.document.ms_office.excel

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.rounded.ArrowBackIos
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.example.pdf.android.share.shareFile
import com.example.pdf.android.toast.showToast
import com.example.pdf.lyricist.globalStrings
import com.example.pdf.appActivity
import com.example.pdf.biz.ad.banner.BannerAd
import com.example.pdf.biz.ad.banner.BannerAdPlace
import com.example.pdf.biz.ad.interstitial.interstitialAdRegister
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.IconButton
import com.example.pdf.lumo.components.IconButtonVariant
import com.example.pdf.lumo.components.Scaffold
import com.example.pdf.lumo.components.topbar.TopBar
import com.example.pdf.lumo.components.topbar.TopBarDefaults
import com.example.pdf.mvi_ui_model.koinUiModel
import com.example.pdf.ui.feature.document_node.DocumentNode
import com.example.pdf.ui.feature.ms_office.excel.ExcelViewerWithWebView
import com.example.pdf.ui.node.document_details.DocumentDetailsNode
import com.example.pdf.ui.node.document.CommonDocumentUiModel
import com.example.pdf.ui.node.document.CommonDocumentUiState
import com.example.pdf.ui.node.document.CommonDocumentTopBar
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.push
import kotlinx.parcelize.Parcelize
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState
import java.io.File

@Parcelize
class ExcelNode(
  override val document: File
) : DocumentNode("excel") {

  @Composable
  override fun Content(navigator: Navigator) {
    super.Content(navigator)

    val cdUiModel: CommonDocumentUiModel = koinUiModel { parametersOf(document) }
    val cdUiState by cdUiModel.collectAsState()

    val (_, onBackAction)
      = interstitialAdRegister(navigator)

    ExcelViewerContent(
      document = document,
      commonDocumentUiState = cdUiState,
      onBack = onBackAction,
      onBookmarkClick = { cdUiModel.onBookmarkClick(document, it) },
      onShareClick = { appActivity?.shareFile(File(document.absolutePath)) },
      onMoreClick = { navigator.push(DocumentDetailsNode(document)) }
    )
  }
}

@Composable
private fun ExcelViewerContent(
  document: File,
  commonDocumentUiState: CommonDocumentUiState,
  onBack: () -> Unit,
  onBookmarkClick: (Boolean) -> Unit,
  onShareClick: () -> Unit,
  onMoreClick: () -> Unit
) {
  Scaffold(
    topBar = {
      CommonDocumentTopBar(
        title = "",
        isBookmarked = commonDocumentUiState.isBookmarked,
        onBackClick = onBack,
        onBookmarkClick = onBookmarkClick,
        onShareClick = onShareClick,
        onMoreClick = onMoreClick
      )
    },
    bottomBar = {
      BannerAd(BannerAdPlace.Document, modifier = Modifier.navigationBarsPadding())
    }
  ) {
    ExcelViewerWithWebView(
      document = document,
      modifier = Modifier
        .fillMaxSize()
        .padding(it),
      onError = {
        showToast(globalStrings.failedToLoadDocument.format(document.name))
        onBack()
      }
    )
  }
}

@Composable
private fun ExcelViewerTopBar(
  onBack: () -> Unit,
) {
  TopBar(
    colors = TopBarDefaults.topBarColors(containerColor = AppTheme.colors.surface)
  ) {
    Row(
      modifier = Modifier
        .fillMaxWidth()
        .padding(horizontal = 8.dp)
    ) {
      IconButton(onClick = onBack, shape = CircleShape, variant = IconButtonVariant.Ghost) {
        Icon(imageVector = Icons.AutoMirrored.Rounded.ArrowBackIos)
      }
    }
  }
}