package com.example.pdf.ui.feature

import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.card.Card
import com.example.pdf.lumo.components.card.CardDefaults

@Composable
fun BoxScope.PageIndexTag(
  indexText: String,
  modifier: Modifier = Modifier,
  contentPaddingValues: PaddingValues = PaddingValues(horizontal = 10.dp, vertical = 4.dp)
) {
  Card(
    colors = CardDefaults.cardColors(
      containerColor = Color.DarkGray.copy(.8f),
      contentColor = Color.White,
    ),
    modifier = modifier
  ) {
    Text(
      text = indexText,
      modifier = Modifier.padding(contentPaddingValues),
      style = AppTheme.typography.label1,
    )
  }
}