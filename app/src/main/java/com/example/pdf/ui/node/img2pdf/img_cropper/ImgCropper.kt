package com.example.pdf.ui.node.img2pdf.img_cropper

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.rounded.ArrowBackIos
import androidx.compose.material.icons.rounded.Flip
import androidx.compose.material.icons.rounded.RotateLeft
import androidx.compose.material.icons.rounded.RotateRight
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cafe.adriel.lyricist.LocalStrings
import com.attafitamim.krop.core.crop.flipHorizontal
import com.attafitamim.krop.core.crop.flipVertical
import com.attafitamim.krop.core.crop.rotLeft
import com.attafitamim.krop.core.crop.rotRight
import com.attafitamim.krop.ui.CropperPreview
import com.example.pdf.R
import com.example.pdf.biz.ad.banner.BannerAd
import com.example.pdf.biz.ad.banner.BannerAdPlace
import com.example.pdf.guia.ScreenNode
import com.example.pdf.guia.UseStatusBarDarkIcons
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.IconButton
import com.example.pdf.lumo.components.IconButtonVariant
import com.example.pdf.lumo.components.NavigationBarDefaults.NavigationBarHeight
import com.example.pdf.lumo.components.NavigationBarItem
import com.example.pdf.lumo.components.Scaffold
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.topbar.TopBar
import com.example.pdf.lumo.components.topbar.TopBarDefaults
import com.example.pdf.mvi_ui_model.koinUiModel
import com.example.pdf.ui.composable.BlankSpacer
import com.example.pdf.ui.feature.PageIndexTag
import com.example.pdf.ui.node.img2pdf.DeletePageTipsBottomSheet
import com.example.pdf.ui.node.img2pdf.list.Img2PdfItemData
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import kotlinx.parcelize.Parcelize
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState

@Parcelize
class ImgCropperNode(
  private val img2PdfItemData: Img2PdfItemData,
  private val indexText: String,
) : ScreenNode("img_cropper") {

  @Composable
  override fun Content(navigator: Navigator) {
    UseStatusBarDarkIcons()

    val uiModel: ImgCropperUiModel = koinUiModel { parametersOf(img2PdfItemData) }
    val uiState by uiModel.collectAsState()

    ImgCropperContent(
      indexText = indexText,
      uiState = uiState,
      onBack = navigator::pop,
      onCrop = uiModel::onCrop,
      onDelete = uiModel::onDelete,
      onDisplayDeleteDialog = uiModel::onDisplayDeleteDialog,
      onDismissDeleteDialog = uiModel::onDismissDeleteDialog,
      onRotateLeft = { uiState.cropper.cropState?.rotLeft() },
      onRotateRight = { uiState.cropper.cropState?.rotRight() },
      onFlipHorizontal = { uiState.cropper.cropState?.flipHorizontal() },
      onFlipVertical = { uiState.cropper.cropState?.flipVertical() }
    )
  }

}

@Composable
private fun ImgCropperContent(
  indexText: String,
  uiState: ImgCropperUiState,
  onBack: () -> Unit,
  onCrop: () -> Unit,
  onDelete: () -> Unit,
  onDisplayDeleteDialog: () -> Unit,
  onDismissDeleteDialog: () -> Unit,
  onRotateLeft: () -> Unit,
  onRotateRight: () -> Unit,
  onFlipHorizontal: () -> Unit,
  onFlipVertical: () -> Unit,
) {
  if (uiState.showDeleteDialog) {
    DeletePageTipsBottomSheet(
      onConfirm = onDelete,
      onDismiss = onDismissDeleteDialog
    )
  }

  Scaffold(
    topBar = {
      ImgCropperTopBar(
        onBack = onBack,
        onCrop = onCrop,
        onDelete = onDisplayDeleteDialog
      )
    },
    bottomBar = {
      ImgCropperBottomBar(
        onRotateLeft = onRotateLeft,
        onRotateRight = onRotateRight,
        onFlipHorizontal = onFlipHorizontal,
        onFlipVertical = onFlipVertical,
        modifier = Modifier.navigationBarsPadding()
      )
    }
  ) {
    Box(
      modifier = Modifier
        .fillMaxSize()
        .padding(it),
      contentAlignment = Alignment.Center
    ) {
      uiState.cropper.cropState?.let { cropState ->
        CropperPreview(
          state = cropState,
          modifier = Modifier.fillMaxSize()
        )
      }

      PageIndexTag(
        indexText = indexText,
        modifier = Modifier
          .align(Alignment.BottomCenter)
          .padding(bottom = 24.dp)
      )
    }
  }
}

@Composable
private fun ImgCropperTopBar(
  onBack: () -> Unit,
  onCrop: () -> Unit,
  onDelete: () -> Unit,
) {
  val strings = LocalStrings.current

  TopBar(
    colors = TopBarDefaults.topBarColors(containerColor = AppTheme.colors.surface)
  ) {
    Row(
      modifier = Modifier
        .fillMaxWidth()
        .padding(horizontal = 8.dp),
      verticalAlignment = Alignment.CenterVertically
    ) {
      IconButton(onClick = onBack, shape = CircleShape, variant = IconButtonVariant.Ghost) {
        Icon(imageVector = Icons.AutoMirrored.Rounded.ArrowBackIos)
      }

      Spacer(Modifier.weight(1f))

      IconButton(onClick = onDelete, shape = CircleShape, variant = IconButtonVariant.Ghost) {
        Icon(painter = painterResource(R.drawable.ic_delete))
      }

      androidx.compose.material3.TextButton(
        onClick = onCrop,
        colors = androidx.compose.material3.ButtonDefaults.textButtonColors(contentColor = AppTheme.colors.primary)
      ) {
        androidx.compose.material3.Text(
          text = strings.ok,
          fontSize = 17.sp,
          fontWeight = FontWeight.Medium
        )
      }

      BlankSpacer(4.dp)
    }
  }
}

@Composable
private fun ImgCropperBottomBar(
  onRotateLeft: () -> Unit,
  onRotateRight: () -> Unit,
  onFlipHorizontal: () -> Unit,
  onFlipVertical: () -> Unit,
  modifier: Modifier = Modifier
) {
  val strings = LocalStrings.current

  Column(modifier) {
    Row(modifier = Modifier.height(NavigationBarHeight)) {
      NavigationBarItem(
        selected = true,
        onClick = onRotateLeft,
        icon = { Icon(imageVector = Icons.Rounded.RotateLeft) },
        label = { Text(text = strings.rotateLeft) },
        modifier = Modifier.weight(1f)
      )

      NavigationBarItem(
        selected = true,
        onClick = onRotateRight,
        icon = { Icon(imageVector = Icons.Rounded.RotateRight) },
        label = { Text(text = strings.rotateRight) },
        modifier = Modifier.weight(1f)
      )

      NavigationBarItem(
        selected = true,
        onClick = onFlipHorizontal,
        icon = { Icon(imageVector = Icons.Rounded.Flip) },
        label = { Text(text = strings.flipHorizontal) },
        modifier = Modifier.weight(1f)
      )

      NavigationBarItem(
        selected = true,
        onClick = onFlipVertical,
        icon = { Icon(imageVector = Icons.Rounded.Flip, modifier = Modifier.rotate(90f)) },
        label = { Text(text = strings.flipVertical) },
        modifier = Modifier.weight(1f)
      )
    }

    BannerAd(BannerAdPlace.ImgCropper)
  }
}