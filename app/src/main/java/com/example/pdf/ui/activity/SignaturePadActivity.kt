package com.example.pdf.ui.activity

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Parcelable
import android.view.View
import android.view.WindowInsets
import android.view.WindowInsetsController
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContract
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.example.pdf.lumo.AppTheme
import com.example.pdf.mvi_ui_model.koinUiModel
import com.example.pdf.ui.node.document.pdf.SignaturePad
import kotlinx.parcelize.Parcelize
import androidx.core.net.toUri
import com.example.pdf.BaseActivity

/**
 * Activity for creating handwritten signatures
 */
class SignaturePadActivity : BaseActivity() {

  companion object {
    /**
     * Create an intent to launch the SignaturePadActivity
     */
    fun createIntent(context: Context): Intent {
      return Intent(context, SignaturePadActivity::class.java)
    }
  }

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    enableEdgeToEdge()
    setFullScreen()

    setContent {
      AppTheme {
        SignaturePadScreen(
          onBack = { finish() },
          onSaveSignature = { uri ->
            val resultIntent = Intent().apply {
              putExtra("signature_uri", uri.toString())
            }
            setResult(RESULT_OK, resultIntent)
            finish()
          }
        )
      }
    }
  }

  /**
   * Composable for the signature pad screen
   */
  @Composable
  private fun SignaturePadScreen(
    onBack: () -> Unit,
    onSaveSignature: (Uri) -> Unit
  ) {
    SignaturePad(
      onBack = onBack,
      onSaveSignature = onSaveSignature,
      modifier = Modifier.fillMaxSize()
    )
  }
}

/**
 * ActivityResultContract for getting a signature from SignaturePadActivity
 */
class GetSignatureContract : ActivityResultContract<Unit, Uri?>() {
  override fun createIntent(context: Context, input: Unit): Intent {
    return SignaturePadActivity.createIntent(context)
  }

  override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
    if (resultCode != Activity.RESULT_OK || intent == null) return null
    val uriString = intent.getStringExtra("signature_uri") ?: return null
    return uriString.toUri()
  }
}
