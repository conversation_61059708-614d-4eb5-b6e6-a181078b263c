package com.example.pdf.ui.node.pdf_set_password

import android.app.Application
import androidx.compose.ui.text.input.TextFieldValue
import com.example.pdf.android.pdf.PdfBoxInitializer
import com.example.pdf.android.toast.showToast
import com.example.pdf.biz.ad.interstitial.OnTryToShowInterAdAndNavAction
import com.example.pdf.guia.GlobalNavigator
import com.example.pdf.kermit.debugLog
import com.example.pdf.lyricist.globalStrings
import com.example.pdf.mvi_ui_model.UiModel
import com.example.pdf.ui.node.convert_success.LockPdfSuccessNode
import com.example.pdf.ui.node.pdf_picker.PdfPasswordAction
import com.example.pdf.ui.node.pdf_picker.PdfPasswordActionEventFlow
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.replaceLast
import org.koin.android.annotation.KoinViewModel
import java.io.File
import com.tom_roush.pdfbox.pdmodel.PDDocument
import com.tom_roush.pdfbox.pdmodel.encryption.AccessPermission
import com.tom_roush.pdfbox.pdmodel.encryption.StandardProtectionPolicy

data class PdfSetPasswordUiState(
  val isLoading: Boolean = false,
  val password: TextFieldValue = TextFieldValue(),
  val passwordVisible: Boolean = false,
  val errorMessage: String? = null
)

@KoinViewModel
class PdfSetPasswordUiModel(
  private val pdfFile: File,
  private val application: Application
) : UiModel<PdfSetPasswordUiState, Nothing>(PdfSetPasswordUiState()) {

  fun onPasswordChange(password: TextFieldValue) = intent {
    reduce { state.copy(password = password, errorMessage = null) }
  }

  fun onTogglePasswordVisibility() = intent {
    reduce { state.copy(passwordVisible = !state.passwordVisible) }
  }

  fun onPasswordClear() = intent {
    reduce { state.copy(password = TextFieldValue(), errorMessage = null) }
  }

  fun onCancel() = intent {
    GlobalNavigator.transaction { pop() }
  }

  fun onConfirm(
    onTryToShowInterAdAndNavAction: OnTryToShowInterAdAndNavAction
  ) = intent {
    val password = state.password.text.trim()

    if (password.isEmpty()) {
      reduce { state.copy(errorMessage = globalStrings.passwordCannotBeEmpty) }
      return@intent
    }

    reduce { state.copy(isLoading = true, errorMessage = null) }

    try {
      val success = setPasswordToPdf(pdfFile, password)

      if (success) {
        PdfPasswordActionEventFlow.emit(PdfPasswordAction.SetPassword(pdfFile.absolutePath))
        onTryToShowInterAdAndNavAction("confirm_set_pdf_password") {
          showToast(globalStrings.passwordSetSuccessfully)

          pop()
          replaceLast(LockPdfSuccessNode((pdfFile)))
        }
      } else {
        reduce { state.copy(isLoading = false, errorMessage = globalStrings.failedToSetPassword) }
      }
    } catch (e: Exception) {
      debugLog(
        tag = "PdfSetPasswordUiModel",
        throwable = e
      ) { "Error setting password: ${e.message}" }
      reduce { state.copy(isLoading = false, errorMessage = "Error: ${e.message}") }
    }
  }

  private suspend fun setPasswordToPdf(pdfFile: File, password: String): Boolean {
    return PdfBoxInitializer.ensureInitialized(application) {
      try {
        // Load the PDF document
        val document = PDDocument.load(pdfFile)

        // Create access permissions
        val accessPermission = AccessPermission()

        // Create protection policy with the password
        val protectionPolicy = StandardProtectionPolicy(
          password, // Owner password
          password, // User password
          accessPermission
        )

        // Set encryption key length
        protectionPolicy.encryptionKeyLength = 128

        // Apply the protection policy
        document.protect(protectionPolicy)

        // Save the encrypted document
        val tempFile = File(pdfFile.parent, "temp_${pdfFile.name}")
        document.save(tempFile)
        document.close()

        // Replace the original file with the encrypted one
        if (pdfFile.delete() && tempFile.renameTo(pdfFile)) {
          return@ensureInitialized true
        }

        return@ensureInitialized false
      } catch (e: Exception) {
        debugLog(
          tag = "PdfSetPasswordUiModel",
          throwable = e
        ) { "Error setting password: ${e.message}" }
        return@ensureInitialized false
      }
    }
  }
}
