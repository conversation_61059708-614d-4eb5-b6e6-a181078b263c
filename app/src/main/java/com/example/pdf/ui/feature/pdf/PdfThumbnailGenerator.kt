package com.example.pdf.ui.feature.pdf

import android.graphics.Bitmap
import android.graphics.pdf.PdfRenderer
import android.os.Build
import android.os.ParcelFileDescriptor
import androidx.compose.ui.graphics.asImageBitmap
import com.bhuvaneshw.pdf.compose.PdfState
import com.example.pdf.android.pdf.PdfBoxInitializer
import com.example.pdf.kermit.debugLog
import com.tom_roush.pdfbox.pdmodel.PDDocument
import com.tom_roush.pdfbox.rendering.PDFRenderer
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.IOException
import androidx.core.graphics.createBitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.graphics.pdf.LoadParams
import com.example.pdf.appContext
import com.tom_roush.pdfbox.rendering.ImageType

/**
 * 工具类，用于从PDF文件生成缩略图
 */
object PdfThumbnailGenerator {
  private const val TAG = "PdfThumbnailGenerator"

  /**
   * 从PDF文件生成缩略图列表
   *
   * @param pdfFile PDF文件
   * @param thumbnailWidth 缩略图宽度（像素）
   * @return 包含所有页面缩略图的列表
   */
  suspend fun generateThumbnails(
    pdfFile: File,
    thumbnailWidth: Int = 200
  ): List<PdfThumbnailItem> = generateThumbnails(pdfFile, null, thumbnailWidth)

  /**
   * 从PDF文件生成缩略图列表，支持密码保护的PDF
   *
   * @param pdfFile PDF文件
   * @param password PDF密码，如果PDF有密码保护则需要提供
   * @param thumbnailWidth 缩略图宽度（像素）
   * @return 包含所有页面缩略图的列表
   */
  suspend fun generateThumbnails(
    pdfFile: File,
    password: String?,
    thumbnailWidth: Int = 200
  ): List<PdfThumbnailItem> = withContext(Dispatchers.IO) {
    val thumbnails = mutableListOf<PdfThumbnailItem>()

    // 首先尝试使用Android内置的PdfRenderer
    if (password.isNullOrEmpty()) {
      tryWithAndroidPdfRenderer(pdfFile, password, thumbnailWidth, thumbnails)
    } else {
      tryWithPdfBox(pdfFile, password, thumbnailWidth, thumbnails)
    }

    thumbnails
  }

  /**
   * 尝试使用Android内置的PdfRenderer生成缩略图
   *
   * @return 如果成功生成了至少一个缩略图，返回true
   */
  private suspend fun tryWithAndroidPdfRenderer(
    pdfFile: File,
    password: String?,
    thumbnailWidth: Int,
    thumbnails: MutableList<PdfThumbnailItem>
  ): Boolean = withContext(Dispatchers.IO) {
    var fileDescriptor: ParcelFileDescriptor? = null
    var pdfRenderer: PdfRenderer? = null
    var success = false

    try {
      // 打开PDF文件
      fileDescriptor = ParcelFileDescriptor.open(
        pdfFile,
        ParcelFileDescriptor.MODE_READ_ONLY
      )

      // 如果提供了密码且运行在Android 15或更高版本，使用LoadParams
      if (!password.isNullOrEmpty() && Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM) {
        try {
          // 使用Android 15的新API
          val loadParamsBuilder = LoadParams.Builder()
          val loadParams = loadParamsBuilder.setPassword(password).build()
          pdfRenderer = PdfRenderer(fileDescriptor, loadParams)
          debugLog(tag = TAG) { "Created PdfRenderer with LoadParams for password" }
        } catch (e: Exception) {
          debugLog(
            tag = TAG,
            throwable = e
          ) { "Failed to create PdfRenderer with LoadParams: ${e.message}" }
          // 如果创建失败，尝试不带密码创建
          pdfRenderer = PdfRenderer(fileDescriptor)
        }
      } else {
        // 创建PdfRenderer（不带密码或低版本Android）
        pdfRenderer = PdfRenderer(fileDescriptor)
      }

      val pageCount = pdfRenderer.pageCount

      for (i in 0 until pageCount) {
        val page = pdfRenderer.openPage(i)

        // 计算缩略图高度，保持宽高比
        val thumbnailHeight = (thumbnailWidth.toFloat() / page.width * page.height).toInt()

        // 创建位图并渲染页面
        val bitmap = createBitmap(thumbnailWidth, thumbnailHeight)

        page.render(
          bitmap,
          null,
          null,
          PdfRenderer.Page.RENDER_MODE_FOR_DISPLAY
        )

        // 将Bitmap转换为ImageBitmap并添加到列表
        thumbnails.add(
          PdfThumbnailItem(
            pageIndex = i,
            thumbnail = bitmap.asImageBitmap()
          )
        )

        page.close()
        success = true
      }

    } catch (e: SecurityException) {
      // 密码错误或需要密码
      debugLog(tag = TAG) { "SecurityException: Password required or incorrect password for PDF: ${pdfFile.name}" }
      e.printStackTrace()
    } catch (e: Exception) {
      debugLog(
        tag = TAG,
        throwable = e
      ) { "Error generating thumbnails with Android PdfRenderer: ${e.message}" }
      e.printStackTrace()
    } finally {
      // 确保资源被释放
      try {
        pdfRenderer?.close()
        fileDescriptor?.close()
      } catch (e: IOException) {
        debugLog(tag = TAG, throwable = e) { "Error closing resources" }
      }
    }

    success
  }

  /**
   * 使用PdfBox库生成缩略图（适用于密码保护的PDF）
   */
  private suspend fun tryWithPdfBox(
    pdfFile: File,
    password: String,
    thumbnailWidth: Int,
    thumbnails: MutableList<PdfThumbnailItem>
  ) = withContext(Dispatchers.IO) {
    try {
      // 确保PdfBox已初始化
      PdfBoxInitializer.ensureInitialized(appContext) {
        try {
          // 使用密码加载PDF文档
          val document = PDDocument.load(pdfFile, password)
          val renderer = PDFRenderer(document)

          // 生成每一页的缩略图
          for (i in 0 until document.numberOfPages) {
            // 渲染页面为Bitmap
            val renderedImage = renderer.renderImage(i, 1.0f, ImageType.RGB)

            // 计算缩略图尺寸，保持宽高比
            val aspectRatio = renderedImage.width.toFloat() / renderedImage.height
            val thumbnailHeight = (thumbnailWidth / aspectRatio).toInt()

            // 创建缩略图并绘制
            val bitmap = createBitmap(thumbnailWidth, thumbnailHeight)
            val canvas = Canvas(bitmap)

            // 设置白色背景（PDF可能有透明背景）
            canvas.drawColor(Color.WHITE)

            // 绘制PDF页面
            val paint = Paint()
            paint.isFilterBitmap = true
            canvas.drawBitmap(
              renderedImage,
              null,
              RectF(0f, 0f, thumbnailWidth.toFloat(), thumbnailHeight.toFloat()),
              paint
            )

            // 添加到缩略图列表
            thumbnails.add(
              PdfThumbnailItem(
                pageIndex = i,
                thumbnail = bitmap.asImageBitmap()
              )
            )

            // 释放原始渲染图像
            renderedImage.recycle()
          }

          // 关闭文档
          document.close()

          debugLog(tag = TAG) { "Successfully generated thumbnails with PdfBox" }
        } catch (e: Exception) {
          debugLog(
            tag = TAG,
            throwable = e
          ) { "Error generating thumbnails with PdfBox: ${e.message}" }
          e.printStackTrace()
        }
      }
    } catch (e: Exception) {
      debugLog(tag = TAG, throwable = e) { "Error initializing PdfBox: ${e.message}" }
      e.printStackTrace()
    }
  }

  /**
   * 从PdfState生成缩略图列表
   *
   * @param pdfState PDF状态对象
   * @param thumbnailWidth 缩略图宽度（像素）
   * @return 包含所有页面缩略图的列表
   */
  suspend fun generateThumbnails(
    pdfState: PdfState,
    thumbnailWidth: Int = 200
  ): List<PdfThumbnailItem> {
    val pdfFile = File(pdfState.source)
    return generateThumbnails(pdfFile, null, thumbnailWidth)
  }

  /**
   * 从PdfState生成缩略图列表，支持密码保护的PDF
   *
   * @param pdfState PDF状态对象
   * @param password PDF密码，如果PDF有密码保护则需要提供
   * @param thumbnailWidth 缩略图宽度（像素）
   * @return 包含所有页面缩略图的列表
   */
  suspend fun generateThumbnails(
    pdfState: PdfState,
    password: String?,
    thumbnailWidth: Int = 200
  ): List<PdfThumbnailItem> {
    val pdfFile = File(pdfState.source)
    return generateThumbnails(pdfFile, password, thumbnailWidth)
  }
}
