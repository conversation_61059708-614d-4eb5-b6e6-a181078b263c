package com.example.pdf.ui.node.img2pdf

import androidx.compose.foundation.layout.Column
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.painterResource
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.R
import com.example.pdf.biz.ad.nat1ve.NativeAd
import com.example.pdf.biz.ad.nat1ve.NativeAdPlace
import com.example.pdf.ui.feature.bottomsheet.permission.TipsBottomSheetContent
import com.holix.android.bottomsheetdialog.compose.BottomSheetDialog

@Composable
fun LeaveEditorTipsBottomSheet(
  onConfirm: () -> Unit,
  onDismiss: () -> Unit
) {
  BottomSheetDialog(onDismissRequest = onDismiss) {

    Column {
      TipsBottomSheetContent(
        title = LocalStrings.current.leaveNow,
        description = LocalStrings.current.leaveEditorWarning,
        iconPainter = painterResource(R.drawable.img_editor_leave),
        confirmText = LocalStrings.current.leave,
        onConfirm = onConfirm,
        onCancel = onDismiss
      )

      NativeAd(adPlace = NativeAdPlace.Img2PdfLeaveConfirm)
    }

  }
}

