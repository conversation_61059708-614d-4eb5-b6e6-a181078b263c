package com.example.pdf.ui.feature.bottomsheet

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.BottomSheetDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Surface
import com.example.pdf.lumo.contentColorFor

@Composable
fun BottomSheetSurface(
  modifier: Modifier = Modifier,
  cornerRadius: Dp = 28.dp,
  color: Color = AppTheme.colors.surface,
  contentColor: Color = contentColorFor(color),
  content: @Composable ColumnScope.() -> Unit
) {
  Surface(
    modifier = modifier
      .fillMaxWidth()
      .navigationBarsPadding(),
    color = color,
    contentColor = contentColor,
    shape = RoundedCornerShape(
      topStart = cornerRadius,
      topEnd = cornerRadius,
      bottomStart = 0.dp,
      bottomEnd = 0.dp
    )
  ) {
    Column(
      horizontalAlignment = Alignment.CenterHorizontally,
      modifier = Modifier.fillMaxWidth(),
    ) {
      BottomSheetDefaults.DragHandle(color = AppTheme.colors.secondary)
      content()
    }
  }
}