package com.example.pdf.ui.node.home.recent

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.PrimaryTabRow
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRowDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.unit.dp
import androidx.core.view.HapticFeedbackConstantsCompat
import androidx.core.view.ViewCompat
import com.example.pdf.android.file.DocumentType
import com.example.pdf.android.file.rememberDocumentTypeItemDataList
import com.example.pdf.biz.ad.interstitial.OnTryToShowInterAdAndNavAction
import com.example.pdf.biz.ad.nat1ve.NativeAd
import com.example.pdf.biz.ad.nat1ve.NativeAdPlace
import com.example.pdf.guia.NavigateAction
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Scaffold
import com.example.pdf.lumo.components.Text
import com.example.pdf.mvi_ui_model.koinUiModel
import com.example.pdf.ui.feature.document_item.DocumentItem
import com.example.pdf.ui.feature.document_node.DocumentNode
import com.example.pdf.ui.node.document_more_action.DocumentMoreActionNode
import com.example.pdf.ui.node.file_access_requester.FileAccessTipsPanel
import com.example.pdf.ui.node.file_access_requester.FileAccessTipsPanelType
import com.example.pdf.ui.node.home.EmptyFileListPlaceholder
import com.example.pdf.ui.node.home.all.DocumentsToolBar
import com.example.pdf.ui.node.home.all.DocumentsTopTabHeight
import com.example.pdf.ui.node.search.SearchNode
import com.example.pdf.ui.node.selection.SelectionNode
import com.example.pdf.ui.node.selection.SelectionNodeArgs
import com.example.pdf.ui.shape.TriangleShape
import com.roudikk.guia.extensions.push
import com.roudikk.guia.extensions.requireLocalNavigator
import org.orbitmvi.orbit.compose.collectAsState
import java.io.File

@Composable
fun RecentDocumentsScreen(
  onTryToShowInterAdAndNavAction: OnTryToShowInterAdAndNavAction
) {
  val view = LocalView.current
  val navigator = requireLocalNavigator()
  val uiModel: RecentDocumentsUiModel = koinUiModel()
  val uiState by uiModel.collectAsState()

  RecentDocumentsContent(
    uiState = uiState,
    onDocumentTypeChange = uiModel::onDocumentTypeChange,
    onDocumentClick = { document ->
      DocumentNode(document)?.let {
        onTryToShowInterAdAndNavAction("open_document_file") { push(it) }
      }
    },
    onBookmarkClick = uiModel::onBookMarkDocument,
    onMoreClick = {
      navigator.push(DocumentMoreActionNode(it))
    },
    onSelectionModeSwitch = {
      navigator.push(
        SelectionNode(
          args = SelectionNodeArgs(
            displayFiles = uiState.displayFiles,
            selectedFiles = emptyList()
          )
        )
      )
    },
    onDocumentItemLongClick = {
      navigator.push(
        SelectionNode(
          args = SelectionNodeArgs(
            displayFiles = uiState.displayFiles,
            selectedFiles = listOf(it)
          )
        )
      )
      ViewCompat.performHapticFeedback(
        view,
        HapticFeedbackConstantsCompat.SEGMENT_FREQUENT_TICK
      )
    },
    onSearchBarClick = { navigator.push(SearchNode()) }
  )
}


@Composable
private fun RecentDocumentsContent(
  uiState: RecentDocumentsUiState,
  onDocumentTypeChange: (DocumentType) -> Unit,
  onDocumentClick: (File) -> Unit,
  onBookmarkClick: (File, Boolean) -> Unit,
  onMoreClick: (File) -> Unit,
  onSelectionModeSwitch: () -> Unit,
  onDocumentItemLongClick: (File) -> Unit,
  onSearchBarClick: () -> Unit,
) {
  val tabIndex = remember(uiState.selectDocumentType) {
    uiState.selectDocumentType.ordinal
  }

  val documentTypeItemDataList = rememberDocumentTypeItemDataList()

  Scaffold(
    topBar = {
      DocumentsToolBar(
        onSortIconClick = null,
        onSelectionModeIconClick = { onSelectionModeSwitch() },
        onSearchBarClick = onSearchBarClick
      )
    }
  ) {
    LazyColumn(
      modifier = Modifier
        .fillMaxSize()
        .padding(top = it.calculateTopPadding())
    ) {
      item {
        PrimaryTabRow(
          selectedTabIndex = tabIndex,
          containerColor = AppTheme.colors.primary,
          indicator = {
            TabRowDefaults.PrimaryIndicator(
              modifier = Modifier.tabIndicatorOffset(tabIndex, matchContentSize = true),
              width = 14.dp,
              height = 8.dp,
              shape = TriangleShape,
              color = AppTheme.colors.onPrimary
            )
          },
          divider = {},
        ) {
          documentTypeItemDataList.forEachIndexed { index, dtid ->
            Tab(
              selected = tabIndex == index,
              onClick = { onDocumentTypeChange(dtid.type) },
              modifier = Modifier.height(DocumentsTopTabHeight),
              selectedContentColor = AppTheme.colors.onPrimary,
              text = {
                Text(
                  text = dtid.label.uppercase(),
                  style = AppTheme.typography.label1,
                  color = AppTheme.colors.onPrimary,
                  modifier = Modifier.padding(bottom = 2.dp)
                )
              }
            )
          }
        }
      }

      val adItem = {
        item("AD_ITEM") {
          NativeAd(adPlace = NativeAdPlace.Recent)
        }
      }

      if (uiState.displayFiles.isNotEmpty()) {
        item {
          FileAccessTipsPanel(
            panelType = FileAccessTipsPanelType.Item,
            modifier = Modifier
              .fillMaxWidth()
              .padding(horizontal = 16.dp, vertical = 8.dp)
          )
        }


        uiState.displayFiles.forEachIndexed { index, documentFile ->
          item {
            val isBookmarked = uiState.bookmarkedFilePaths.contains(documentFile.absolutePath)

            DocumentItem(
              file = documentFile,
              isSelectionMode = false,
              isSelected = false,
              isBookmarked = isBookmarked,
              isShowBookmarkIcon = isBookmarked,
//        onBookmarkClick = onBookmarkClick,
              onMoreClick = onMoreClick,
              onClick = onDocumentClick,
              onLongClick = onDocumentItemLongClick,
              modifier = Modifier.animateItem()
            )
          }

          if (index == minOf(2, uiState.displayFiles.size - 1)) {
            adItem()
          }
        }
      } else {
        item {
          documentTypeItemDataList.find { data -> data.type == uiState.selectDocumentType }
            ?.let { matchData ->
              EmptyFileListPlaceholder(
                documentTypeItemData = matchData,
                modifier = Modifier.fillParentMaxSize()
              )
            }
        }
      }

      item { Spacer(modifier = Modifier.height(116.dp)) }
    }

    if (uiState.displayFiles.isEmpty()) {
      FileAccessTipsPanel(
        modifier = Modifier
          .fillMaxSize()
          .padding(top = it.calculateTopPadding())
          .padding(top = DocumentsTopTabHeight)
      )
    }
  }

}