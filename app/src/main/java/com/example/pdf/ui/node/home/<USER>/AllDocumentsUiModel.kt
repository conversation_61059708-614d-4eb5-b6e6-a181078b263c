package com.example.pdf.ui.node.home.all

import com.example.pdf.android.file.DocumentFilesManager
import com.example.pdf.android.file.DocumentRepository
import com.example.pdf.android.file.DocumentType
import com.example.pdf.android.toast.showToast
import com.example.pdf.kermit.debugLog
import com.example.pdf.mmkv.UserSettingsKvStore
import com.example.pdf.mvi_ui_model.UiModel
import com.example.pdf.ui.node.home.HomeTab
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.koin.android.annotation.KoinViewModel
import java.io.File

@KoinViewModel
class AllDocumentsUiModel(
  private val documentRepository: DocumentRepository,
  private val documentFilesManager: DocumentFilesManager,
  private val userSettingsKvStore: UserSettingsKvStore
) : UiModel<DocumentsUiState, Nothing>(
  DocumentsUiState(
    selectDocumentType = userSettingsKvStore.documentTypeTabStateFlow.value,
    homeTab = HomeTab.ALL
  )
) {

  init {
    registerAllDocumentsFlow()
    registerBookmarkedDocumentPathsFlow()
    registerDocumentsSortedFlow()
    registerDocumentTypeTabStateFlow()
  }

  private fun registerAllDocumentsFlow() = intent {
    documentRepository.documentsFlow.onEach { allDocuments ->
      val filteredDocuments = allDocuments.let {
        documentFilesManager.filterByDocumentType(it, state.selectDocumentType)
      }.let {
        documentFilesManager.sortDocumentFiles(it, userSettingsKvStore.documentsSortedFlow.value)
      }

      reduce { state.copy(displayFiles = filteredDocuments) }
    }.launchIn(uiModelScope)
  }

  private fun registerBookmarkedDocumentPathsFlow() = intent {
    userSettingsKvStore.bookmarkedDocumentPathsFlow.onEach { bookmarkedPaths ->
      reduce { state.copy(bookmarkedFilePaths = bookmarkedPaths) }
    }.launchIn(uiModelScope)
  }

  private fun registerDocumentsSortedFlow() = intent {
    userSettingsKvStore.documentsSortedFlow.onEach { sortType ->
      val sortedDocuments = documentRepository.documentsFlow.first().let {
        documentFilesManager.filterByDocumentType(it, state.selectDocumentType)
      }.let {
        documentFilesManager.sortDocumentFiles(it, sortType)
      }
      reduce { state.copy(displayFiles = sortedDocuments) }
    }.launchIn(uiModelScope)
  }

  private fun registerDocumentTypeTabStateFlow() = intent {
    userSettingsKvStore.documentTypeTabStateFlow.onEach { documentType ->
      onHandleDocumentTypeChange(documentType)
    }.launchIn(uiModelScope)
  }

  private fun onHandleDocumentTypeChange(documentType: DocumentType) = intent {
    debugLog(tag = "AllDocumentsUiModel") { "Changed document type to $documentType" }

    val filteredDocuments = documentRepository.documentsFlow.first().let {
      documentFilesManager.filterByDocumentType(it, documentType)
    }.let {
      documentFilesManager.sortDocumentFiles(it, userSettingsKvStore.documentsSortedFlow.value)
    }

    reduce {
      state.copy(
        displayFiles = filteredDocuments,
        selectDocumentType = documentType
      )
    }
  }

  fun onDocumentTypeChange(documentType: DocumentType) = intent {
    userSettingsKvStore.documentTypeTabStateFlow.value = documentType
  }

  fun onBookMarkDocument(file: File, bookmarked: Boolean) = intent {
    val needHandleDocumentPath = file.absolutePath
    val bookmarkedPaths = userSettingsKvStore.bookmarkedDocumentPathsFlow.value

    val updatedBookmarkedDocumentPaths = if (bookmarked) {
      bookmarkedPaths + needHandleDocumentPath
    } else {
      bookmarkedPaths - needHandleDocumentPath
    }

    userSettingsKvStore.bookmarkedDocumentPathsFlow.value = updatedBookmarkedDocumentPaths

//    reduce { state.copy(bookmarkedFilePaths = updatedBookmarkedDocumentPaths) }
  }

}