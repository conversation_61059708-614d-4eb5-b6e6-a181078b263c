package com.example.pdf.ui.node.selection

import android.os.Parcelable
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.rounded.ArrowBackIos
import androidx.compose.material.icons.outlined.Circle
import androidx.compose.material.icons.rounded.CheckCircle
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.PreviewComposable
import com.example.pdf.R
import com.example.pdf.biz.ad.banner.BannerAd
import com.example.pdf.biz.ad.banner.BannerAdPlace
import com.example.pdf.biz.ad.nat1ve.NativeAd
import com.example.pdf.biz.ad.nat1ve.NativeAdPlace
import com.example.pdf.guia.ScreenNode
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.HorizontalDivider
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.NavigationBar
import com.example.pdf.lumo.components.NavigationBarDefaults.NavigationBarHeight
import com.example.pdf.lumo.components.NavigationBarItem
import com.example.pdf.lumo.components.Scaffold
import com.example.pdf.lumo.components.Surface
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.topbar.TopBar
import com.example.pdf.lumo.grayTint6f
import com.example.pdf.lumo.grayTint7f
import com.example.pdf.mvi_ui_model.koinUiModel
import com.example.pdf.ui.feature.document_item.DocumentItem
import com.example.pdf.ui.node.documents_delete.DocumentsDeleteDialog
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import kotlinx.parcelize.Parcelize
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState
import java.io.File

@Parcelize
data class SelectionNodeArgs(
  val displayFiles: List<File> = emptyList(),
  val selectedFiles: List<File> = emptyList()
) : Parcelable

@Parcelize
class SelectionNode(
  private val args: SelectionNodeArgs
) : ScreenNode("selection") {
  @Composable
  override fun Content(navigator: Navigator) {
    val uiModel: SelectionUiModel = koinUiModel { parametersOf(args) }
    val uiState by uiModel.collectAsState()

    if (uiState.showDeleteConfirmationDialog) {
      DocumentsDeleteDialog(
        documents = uiState.selectedFiles,
        onConfirm = uiModel::onDelete,
        onCancel = uiModel::onDismissDeleteConfirmationDialog
      )
    }

    SelectionContent(
      args = args,
      uiState = uiState,
      onBack = navigator::pop,
      onSelectAllSwitch = uiModel::onSelectAllSwitch,
      onSelectDocument = uiModel::onSelectDocument,
      onDeleteClick = uiModel::onShowDeleteConfirmationDialog,
      onShareClick = uiModel::onShare
    )
  }
}


@Composable
private fun SelectionContent(
  args: SelectionNodeArgs,
  uiState: SelectionUiState,
  onBack: () -> Unit,
  onSelectAllSwitch: (Boolean) -> Unit,
  onSelectDocument: (File, Boolean) -> Unit,
  onDeleteClick: () -> Unit,
  onShareClick: () -> Unit,
) {
  Scaffold(
    topBar = {
      SelectionTopBar(
        uiState = uiState,
        onBack = onBack,
        onSelectAllSwitch = onSelectAllSwitch
      )
    },
    bottomBar = {
      SelectionBottomBar(
        enabled = uiState.selectedFiles.isNotEmpty(),
        onDeleteClick = onDeleteClick,
        onShareClick = onShareClick,
        modifier = Modifier.navigationBarsPadding()
      )
    }
  ) {
    LazyColumn(
      modifier = Modifier
        .fillMaxSize()
        .padding(it)
    ) {
      args.displayFiles.forEachIndexed { index, documentFile ->
        item {
          val isBookmarked = uiState.bookmarkedFilePaths.contains(documentFile.absolutePath)

          DocumentItem(
            file = documentFile,
            isSelectionMode = true,
            isSelected = uiState.selectedFiles.contains(documentFile),
            isBookmarked = isBookmarked,
            isShowBookmarkIcon = isBookmarked,
            onSelect = onSelectDocument,
          )
        }

        if (index == minOf(2, args.displayFiles.size - 1)) {
          item("AD_ITEM") {
            NativeAd(adPlace = NativeAdPlace.Selection)
          }
        }
      }
    }
  }
}

@Composable
private fun SelectionTopBar(
  uiState: SelectionUiState,
  modifier: Modifier = Modifier,
  onBack: () -> Unit,
  onSelectAllSwitch: (Boolean) -> Unit,
) {
  val strings = LocalStrings.current

  TopBar(
    modifier = modifier,
  ) {
    Row(
      modifier = Modifier
        .fillMaxSize()
        .padding(horizontal = 16.dp),
      verticalAlignment = Alignment.CenterVertically
    ) {
      Surface(
        onClick = onBack,
        shape = CircleShape
      ) {
        Icon(
          imageVector = Icons.AutoMirrored.Rounded.ArrowBackIos,
          tint = AppTheme.colors.onSurface,
          modifier = Modifier
            .size(32.dp)
            .padding(end = 0.5.dp)
            .padding(6.dp)
        )
      }

      Text(
        text = "${uiState.selectedFiles.size} ${strings.selected.lowercase()}",
        style = AppTheme.typography.h2,
        modifier = Modifier
          .weight(1f)
          .padding(start = 16.dp)
      )

      val (tintColor, chipIcon, chipText) =
        if (uiState.isSelectAll) {
          Triple(AppTheme.colors.primary, Icons.Rounded.CheckCircle, strings.selectAll)
        } else {
          Triple(AppTheme.colors.onSurface.copy(.66f), Icons.Outlined.Circle, strings.selectAll)
        }

      Row(
        modifier = Modifier
          .background(color = AppTheme.colors.background, shape = RoundedCornerShape(6.dp))
          .clip(RoundedCornerShape(6.dp))
          .clickable(onClick = { onSelectAllSwitch(!uiState.isSelectAll) })
          .height(32.dp),
        verticalAlignment = Alignment.CenterVertically,
      ) {
        Spacer(Modifier.width(8.dp))

        Icon(
          imageVector = chipIcon,
          modifier = Modifier.size(20.dp),
          tint = tintColor
        )

        Spacer(Modifier.width(4.dp))

        Text(
          text = chipText,
          style = AppTheme.typography.label1,
          color = tintColor,
        )

        Spacer(Modifier.width(9.dp))
      }
    }
  }
}

@Composable
private fun SelectionBottomBar(
  enabled: Boolean,
  onDeleteClick: () -> Unit,
  onShareClick: () -> Unit,
  modifier: Modifier = Modifier
) {
  val strings = LocalStrings.current

  Column(modifier) {
    Row(modifier = Modifier.height(NavigationBarHeight)) {
      NavigationBarItem(
        selected = enabled,
        onClick = onDeleteClick,
        icon = {
          Image(
            painter = painterResource(R.drawable.ic_delete_c),
            contentDescription = null,
            colorFilter = if (enabled) null else ColorFilter.tint(
              color = grayTint7f
            )
          )
        },
        label = { Text(text = strings.delete) },
      )

      NavigationBarItem(
        selected = enabled,
        onClick = onShareClick,
        icon = {
          Image(
            painter = painterResource(R.drawable.ic_share_c),
            contentDescription = null,
            colorFilter = if (enabled) null else ColorFilter.tint(
              color = grayTint7f
            )
          )
        },
        label = { Text(text = strings.share) },
      )
    }

    BannerAd(BannerAdPlace.Selection)
  }


  HorizontalDivider()
}


@Preview(
  showSystemUi = true
)
@Composable
private fun SelectionContentPreview() {
  PreviewComposable {
    SelectionContent(
      args = SelectionNodeArgs(),
      uiState = SelectionUiState(),
      onBack = {},
      onSelectDocument = { _, _ -> },
      onSelectAllSwitch = { _ -> },
      onDeleteClick = {},
      onShareClick = {}
    )
  }
}