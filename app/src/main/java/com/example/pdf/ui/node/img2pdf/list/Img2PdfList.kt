package com.example.pdf.ui.node.img2pdf.list

import android.net.Uri
import android.view.View
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyGridItemScope
import androidx.compose.foundation.lazy.grid.LazyGridState
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.rounded.ArrowBackIos
import androidx.compose.material.icons.rounded.Add
import androidx.compose.material.icons.rounded.Close
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.core.view.HapticFeedbackConstantsCompat
import androidx.core.view.ViewCompat
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.biz.ad.banner.BannerAd
import com.example.pdf.biz.ad.banner.BannerAdPlace
import com.example.pdf.biz.ad.interstitial.interstitialAdRegister
import com.example.pdf.coil.CoilImage
import com.example.pdf.guia.ScreenNode
import com.example.pdf.guia.UseStatusBarDarkIcons
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Button
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.IconButton
import com.example.pdf.lumo.components.IconButtonVariant
import com.example.pdf.lumo.components.Scaffold
import com.example.pdf.lumo.components.Surface
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.topbar.TopBar
import com.example.pdf.lumo.components.topbar.TopBarDefaults
import com.example.pdf.mvi_ui_model.koinUiModel
import com.example.pdf.ui.feature.image_picker.AdaptiveImageUriPickerContainer
import com.example.pdf.ui.feature.image_picker.ImageUriPickerContainer
import com.example.pdf.ui.feature.loading.LoadingDialog
import com.example.pdf.ui.node.img2pdf.DeletePageTipsBottomSheet
import com.example.pdf.ui.node.img2pdf.LeaveEditorTipsBottomSheet
import com.example.pdf.ui.node.img2pdf.img_cropper.ImgCropperNode
import com.roudikk.guia.backstack.NavBackHandler
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.push
import kotlinx.coroutines.delay
import kotlinx.parcelize.Parcelize
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState
import sh.calvin.reorderable.ReorderableCollectionItemScope
import sh.calvin.reorderable.ReorderableItem
import sh.calvin.reorderable.ReorderableLazyGridState
import sh.calvin.reorderable.rememberReorderableLazyGridState
import kotlin.math.sqrt

const val MAX_IMG2PDF_ITEMS = 20

@Parcelize
class Img2PdfListNode(
  private val initImportUris: List<Uri>
) : ScreenNode("img2pdf_list") {
  @Composable
  override fun Content(navigator: Navigator) {
    UseStatusBarDarkIcons()

    val uiModel: Img2PdfListUiModel = koinUiModel { parametersOf(initImportUris) }
    val uiState by uiModel.collectAsState()

    val (onTryToShowInterAdAndNavAction, onBackAction)
      = interstitialAdRegister(navigator)

    NavBackHandler(onBack = { uiModel.onTryToBack(onBackAction) })

    Img2PdfListContent(
      uiState = uiState,
      onDismissLeaveDialog = uiModel::onDismissLeaveDialog,
      onDismissDeleteDialog = uiModel::onDismissDeleteDialog,
      onTryToBack = { uiModel.onTryToBack(onBackAction) },
      onBack = {
        uiModel.onDismissLeaveDialog()
        onBackAction()
      },
      onReorder = uiModel::onReorder,
      onItemClick = { index, itemData ->
        navigator.push(
          ImgCropperNode(itemData, indexText = "${index + 1}/${uiState.list.size}")
        )
      },
      onAddComplete = uiModel::onAddUris,
      onItemRemoveClick = uiModel::onDisplayDeleteDialog,
      onItemRemove = uiModel::onRemoveAt,
      onConvertStart = {
        onTryToShowInterAdAndNavAction("convert_img2pdf") {
          uiModel.onConvertToPdfWithConsistentWidth()
        }
      }
    )
  }
}

@Composable
private fun Img2PdfListContent(
  uiState: Img2PdfListUiState,
  onDismissLeaveDialog: () -> Unit,
  onDismissDeleteDialog: () -> Unit,
  onTryToBack: () -> Unit,
  onBack: () -> Unit,
  onReorder: (from: Int, to: Int) -> Unit,
  onItemClick: (Int, Img2PdfItemData) -> Unit,
  onItemRemoveClick: (Int) -> Unit,
  onItemRemove: (Int) -> Unit,
  onAddComplete: (List<Uri>) -> Unit,
  onConvertStart: () -> Unit
) {
  if (uiState.showLeaveDialog) {
    LeaveEditorTipsBottomSheet(onConfirm = onBack, onDismiss = onDismissLeaveDialog)
  }

  if (uiState.showDeleteDialogState.show) {
    DeletePageTipsBottomSheet(
      onConfirm = {
        onItemRemove(uiState.showDeleteDialogState.index)
        onDismissDeleteDialog()
      },
      onDismiss = onDismissDeleteDialog
    )
  }

  if (uiState.processing) {
    LoadingDialog()
  }

  Scaffold(
    topBar = { Img2PdfTopBar(onBackClick = onTryToBack) },
    bottomBar = {
      Img2PdfBottomBar(
        onConvertStart = onConvertStart,
        imgCount = uiState.list.size
      )
    },
    containerColor = AppTheme.colors.disabled.copy(.15f)
  ) {
    Column(
      modifier = Modifier
        .fillMaxSize()
        .padding(it),
      horizontalAlignment = Alignment.CenterHorizontally,
    ) {
      Img2PdfTips(
        modifier = Modifier
          .fillMaxWidth()
          .animateContentSize(),

        outSidePaddingValues = PaddingValues(
          start = 16.dp,
          end = 16.dp,
          top = 16.dp,
        )
      )

      Img2PdfGrid(
        img2PdfItemDataList = uiState.list,
        onReorder = onReorder,
        onItemClick = onItemClick,
        onAddComplete = onAddComplete,
        onItemRemoveClick = onItemRemoveClick,
        modifier = Modifier.weight(1f)
      )
    }
  }
}

@Composable
private fun Img2PdfTopBar(
  onBackClick: () -> Unit,
) {
  TopBar(
    colors = TopBarDefaults.topBarColors(containerColor = AppTheme.colors.surface)
  ) {
    Row(
      modifier = Modifier
        .fillMaxWidth()
        .padding(horizontal = 8.dp)
    ) {
      IconButton(onClick = onBackClick, shape = CircleShape, variant = IconButtonVariant.Ghost) {
        Icon(imageVector = Icons.AutoMirrored.Rounded.ArrowBackIos)
      }
    }
  }
}

@Composable
private fun Img2PdfBottomBar(
  onConvertStart: () -> Unit,
  imgCount: Int,
  modifier: Modifier = Modifier
) {
  val strings = LocalStrings.current
  Surface(modifier = modifier.navigationBarsPadding()) {
    Column {
      Button(
        onClick = onConvertStart,
        enabled = imgCount > 0,
        modifier = Modifier
          .fillMaxWidth()
          .padding(horizontal = 24.dp, vertical = 18.dp)
      ) {
        Text(
          text = "${strings.convert} ($imgCount)",
          style = AppTheme.typography.h2,
          modifier = Modifier.padding(8.dp)
        )
      }

      BannerAd(BannerAdPlace.Img2Pdf)
    }
  }
}

@Composable
private fun Img2PdfGrid(
  img2PdfItemDataList: List<Img2PdfItemData>,
  modifier: Modifier = Modifier,
  localView: View = LocalView.current,
  onReorder: (from: Int, to: Int) -> Unit = { _, _ -> },
  onItemClick: (index: Int, Img2PdfItemData) -> Unit = { _, _ -> },
  onItemRemoveClick: (Int) -> Unit = { _ -> },
  onAddComplete: (List<Uri>) -> Unit = { _ -> },
  gridState: LazyGridState = rememberLazyGridState(),
  reorderableGridState: ReorderableLazyGridState = rememberReorderableLazyGridState(gridState) { from, to ->
    onReorder(from.index, to.index)

    ViewCompat.performHapticFeedback(localView, HapticFeedbackConstantsCompat.SEGMENT_FREQUENT_TICK)
  },
) {
  val screenWidth = LocalConfiguration.current.screenWidthDp.dp
  val spacing = 16.dp

  val itemSize = remember(screenWidth) {
    val itemWidth = (screenWidth - spacing * 4) / 3
    val itemHeight = itemWidth * sqrt(2f)
    DpSize(itemWidth, itemHeight)
  }

  LazyVerticalGrid(
    columns = GridCells.Fixed(3),
    state = gridState,
    modifier = modifier
      .fillMaxSize()
      .padding(horizontal = spacing),
    horizontalArrangement = Arrangement.spacedBy(spacing),
    verticalArrangement = Arrangement.spacedBy(spacing)
  ) {
    itemsIndexed(
      items = img2PdfItemDataList,
      key = { _, data -> data.id }
    ) { index, data ->
      ReorderableItem(
        state = reorderableGridState,
        key = data.id,
        modifier = if (index < 3)
          Modifier.padding(top = spacing)
        else if (index == MAX_IMG2PDF_ITEMS - 1)
          Modifier.padding(bottom = spacing)
        else
          Modifier
      ) { isDragging ->
        ImgItem(
          index = index,
          data = data,
          onClick = { onItemClick(index, it) },
          onRemove = { index ->
            if (isDragging.not()) {
              onItemRemoveClick(index)
            }
          },
          itemSize = itemSize
        )
      }
    }

    item(key = "ADD_ITEM") {
      Box(
        modifier = if (img2PdfItemDataList.size < 3)
          Modifier.padding(top = spacing)
        else
          Modifier.padding(bottom = spacing)
      ) {
        AddItem(
          addLimit = (MAX_IMG2PDF_ITEMS - img2PdfItemDataList.size).coerceAtLeast(0),
          itemSize = itemSize,
          onAddComplete = onAddComplete
        )
      }
    }
  }
}

@Composable
private fun LazyGridItemScope.AddItem(
  addLimit: Int,
  itemSize: DpSize,
  onAddComplete: (List<Uri>) -> Unit,
  modifier: Modifier = Modifier
) {
  if (addLimit <= 0) return

  val density = LocalDensity.current
  val dashPathEffect = PathEffect.dashPathEffect(floatArrayOf(10f, 10f), 0f)
  val cornerRadiusPx = remember(density) { with(density) { 8.dp.toPx() } }
  val cornerRadius = CornerRadius(cornerRadiusPx)
  val strokeWidthPx = remember(density) { with(density) { 2.dp.toPx() } }

  AdaptiveImageUriPickerContainer(
    limit = addLimit,
    onPickComplete = onAddComplete
  ) { startPicker ->
    Box(
      modifier = modifier
        .size(itemSize)
        .clip(RoundedCornerShape(8.dp))
        .background(color = AppTheme.colors.disabled.copy(.5f))
        .clickable(onClick = startPicker)
        .drawBehind {
          drawRoundRect(
            color = Color.LightGray,
            style = Stroke(width = strokeWidthPx, pathEffect = dashPathEffect),
            cornerRadius = cornerRadius
          )
        },
      contentAlignment = Alignment.Center
    ) {
      Icon(
        imageVector = Icons.Rounded.Add,
        contentDescription = "Add Item",
        modifier = Modifier.size(32.dp),
        tint = Color.Gray
      )
    }
  }
}

@Composable
private fun ReorderableCollectionItemScope.ImgItem(
  index: Int,
  data: Img2PdfItemData,
  onClick: (Img2PdfItemData) -> Unit,
  onRemove: (Int) -> Unit,
  itemSize: DpSize,
  modifier: Modifier = Modifier,
) {
  val view = LocalView.current

  var enableCrossfade by remember { mutableStateOf(false) }

  LaunchedEffect(data.cropTimes) {
    enableCrossfade = true
    delay(1000)
    enableCrossfade = false
  }

  Surface(
    onClick = { onClick(data) },
    shape = RoundedCornerShape(8.dp),
    modifier = modifier
      .size(itemSize)
      .longPressDraggableHandle(
        onDragStarted = {
          ViewCompat.performHapticFeedback(view, HapticFeedbackConstantsCompat.GESTURE_START)
        },
        onDragStopped = {
          ViewCompat.performHapticFeedback(view, HapticFeedbackConstantsCompat.GESTURE_END)
        },
      )
  ) {
    Box(Modifier.fillMaxSize()) {
      CoilImage(
        data = data.croppedBitmap ?: data.uri,
        contentDescription = null,
        modifier = Modifier.fillMaxSize(),
        contentScale = ContentScale.Crop,
        enableCrossfade = enableCrossfade
      )

      Box(
        modifier = Modifier
          .padding(top = 7.dp, end = 6.dp)
          .clickable { onRemove(index) }
          .background(AppTheme.colors.primary, CircleShape)
          .clip(CircleShape)
          .align(Alignment.TopEnd)
          .size(20.dp),
        contentAlignment = Alignment.Center
      ) {
        Icon(
          imageVector = Icons.Rounded.Close,
          contentDescription = "remove item",
          modifier = Modifier.size(17.dp),
          tint = AppTheme.colors.onPrimary
        )
      }

      Box(
        modifier = Modifier
          .align(Alignment.TopStart)
          .padding(top = 6.dp, start = 6.dp)
          .size(22.dp)
          .background(AppTheme.colors.black.copy(.5f), RoundedCornerShape(4.dp)),
        contentAlignment = Alignment.Center,
      ) {
        Text(
          text = (index + 1).toString(),
          style = if (index + 1 >= 10) AppTheme.typography.body2 else AppTheme.typography.body1,
          color = AppTheme.colors.onPrimary,
        )
      }
    }
  }
}
