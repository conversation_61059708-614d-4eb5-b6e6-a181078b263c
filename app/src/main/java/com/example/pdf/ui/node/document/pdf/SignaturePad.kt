package com.example.pdf.ui.node.document.pdf

import android.annotation.SuppressLint
import android.net.Uri
import androidx.activity.compose.BackHandler
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.rounded.Redo
import androidx.compose.material.icons.automirrored.rounded.Undo
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.rounded.Check
import androidx.compose.material.icons.rounded.CleaningServices
import androidx.compose.material.icons.rounded.KeyboardArrowDown
import androidx.compose.material.icons.rounded.LineWeight
import androidx.compose.material3.Slider
import androidx.compose.material3.SliderDefaults
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asAndroidBitmap
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.core.graphics.toColor
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.android.res.ResDrawable
import com.example.pdf.android.toast.showToast
import com.example.pdf.biz.skipSplash
import com.example.pdf.guia.DialogNode
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.Gray100
import com.example.pdf.lumo.components.ButtonVariant
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.IconButton
import com.example.pdf.lumo.components.IconButtonVariant
import com.example.pdf.lumo.components.Scaffold
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.card.Card
import com.example.pdf.lumo.components.card.CardDefaults
import com.example.pdf.lumo.components.dashedBorder
import com.example.pdf.mvi_ui_model.koinUiModel
import com.example.pdf.ui.activity.GetSignatureContract
import com.example.pdf.ui.feature.loading.LoadingDialog
import com.roudikk.guia.core.Dialog
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import io.ak1.drawbox.DrawBox
import kotlinx.parcelize.Parcelize
import org.orbitmvi.orbit.compose.collectAsState


@Parcelize
class SignaturePadNode(
  private val onSignatureCreated: (Uri) -> Unit = {}
) : DialogNode("signature_pad") {

  @SuppressLint("UnusedBoxWithConstraintsScope")
  @Composable
  override fun Content(
    navigator: Navigator,
    dialog: Dialog?
  ) {


    SignaturePad(
      onBack = navigator::pop,
      onSaveSignature = { uri ->
        onSignatureCreated(uri)
        navigator.pop()
      })
  }

}

val DrawBoxBorderRGB = 240
val DrawBoxBorderColor = Color(DrawBoxBorderRGB, DrawBoxBorderRGB, DrawBoxBorderRGB)

/**
 * Main composable for the signature pad using DrawBox library
 * @param onBack Callback function that will be called when the back button is clicked
 * @param onSaveSignature Callback function that will be called with the URI of the saved signature
 * @param modifier Modifier to be applied to the container
 */
@Composable
fun SignaturePad(
  onBack: () -> Unit,
  onSaveSignature: (Uri) -> Unit,
  modifier: Modifier = Modifier.fillMaxSize(),
) {
  val context = LocalContext.current
  val uiModel: SignaturePadUiModel = koinUiModel()
  val uiState by uiModel.collectAsState()

  // Create a DrawController to control the DrawBox
  val drawController = rememberSignaturePadDrawController(uiModel)

  BackHandler {
    if (uiState.showSizeSelector || uiState.showColorSelector) {
      uiModel.closeSelectors()
    } else {
      onBack()
    }
  }

  if (uiState.processing) {
    LoadingDialog()
  }

  Scaffold(
    topBar = {
      SignatureTopBar(
        uiState = uiState,
        onBackClick = onBack,
        onClear = uiModel::onClear,
        onUndo = uiModel::onUndo,
        onRedo = uiModel::onRedo,
        onSave = uiModel::onSave,
        modifier = Modifier.padding(horizontal = 24.dp)
      )
    },
    bottomBar = {
      SignatureBottomBar(
        currentColor = uiState.currentColor,
        currentStrokeWidth = uiState.currentStrokeWidth,
        onSizeClick = uiModel::toggleSizeSelector,
        onColorClick = uiModel::toggleColorSelector
      )
    },
    modifier = modifier
  ) { paddingValues ->
    // DrawBox canvas
    Box(
      modifier = Modifier
        .fillMaxSize()
        .padding(horizontal = 40.dp, vertical = 2.dp)
        .padding(paddingValues)
    ) {
      // Use a white background for the DrawBox to make the drawing visible
      // The background will be made transparent when saving
      DrawBox(
        drawController = drawController,
        modifier = Modifier
          .fillMaxSize()
          .dashedBorder(color = DrawBoxBorderColor, strokeWidth = 3.dp, cornerRadius = 12.dp)
          .clip(CardDefaults.Shape),
        backgroundColor = Gray100,
        bitmapCallback = { imageBitmap, t ->
          if (t != null) {
            uiModel.onDismissProcessingDialog()
            t.printStackTrace()
          }

          // Function to save the signature as an image and return the URI
          if (imageBitmap != null) {
            uiModel.saveSignatureToCache(context, imageBitmap.asAndroidBitmap()) { uri ->
              uri?.let {
                uiModel.onDismissProcessingDialog()
                onSaveSignature(it)
              }
            }
          }
        }
      )

      // Size selector popup
      if (uiState.showSizeSelector) {
        SizeSelectionPopup(
          currentStrokeWidth = uiState.currentStrokeWidth,
          strokeWidths = uiState.strokeWidths,
          onStrokeWidthSelected = { width ->
            uiModel.onStrokeWidthSelected(width)
          },
          onDismiss = uiModel::closeSelectors,
          modifier = Modifier
            .align(Alignment.BottomEnd)
            .padding(end = 8.dp, bottom = 8.dp)
        )
      }

      // Color selector popup
      if (uiState.showColorSelector) {
        ColorSelectionPopup(
          currentColor = uiState.currentColor,
          colors = uiState.colors,
          onColorSelected = { color ->
            uiModel.onColorSelected(color)
          },
          onDismiss = uiModel::closeSelectors,
          modifier = Modifier
            .align(Alignment.BottomEnd)
            .padding(end = 8.dp, bottom = 8.dp)
        )
      }
    }
  }
}

/**
 * Composable for the top bar with action buttons
 */
@Composable
fun SignatureTopBar(
  uiState: SignaturePadUiState,
  onBackClick: () -> Unit,
  onClear: () -> Unit,
  onUndo: () -> Unit,
  onRedo: () -> Unit,
  onSave: () -> Unit,
  modifier: Modifier = Modifier
) {
  TopAppBar(
    title = {},
    navigationIcon = {
      IconButton(
        onClick = onBackClick,
        variant = IconButtonVariant.Ghost,
        shape = CircleShape
      ) {
        Icon(
          imageVector = Icons.Default.Close,
          contentDescription = "Back",
          tint = AppTheme.colors.onSurface
        )
      }
    },
    actions = {
      IconButton(
        onClick = onClear,
        variant = IconButtonVariant.Ghost,
        shape = CircleShape,
      ) {
        Icon(
          imageVector = Icons.Rounded.CleaningServices,
          contentDescription = "Clear",
          tint = AppTheme.colors.onSurface
        )
      }

      // Undo button
      val undoEnabled = remember(uiState.undoCount) { uiState.undoCount > 0 }
      IconButton(
        onClick = onUndo,
        enabled = undoEnabled,
        variant = IconButtonVariant.Ghost,
        shape = CircleShape
      ) {
        Icon(
          imageVector = Icons.AutoMirrored.Rounded.Undo,
          contentDescription = "Undo",
          tint = if (undoEnabled)
            AppTheme.colors.onSurface
          else
            AppTheme.colors.textDisabled
        )
      }

      // Redo button
      val redoEnabled = remember(uiState.redoCount) { uiState.redoCount > 0 }
      IconButton(
        onClick = onRedo,
        enabled = redoEnabled,
        variant = IconButtonVariant.Ghost,
        shape = CircleShape
      ) {
        Icon(
          imageVector = Icons.AutoMirrored.Rounded.Redo,
          contentDescription = "Redo",
          tint = if (redoEnabled)
            AppTheme.colors.onSurface
          else
            AppTheme.colors.textDisabled
        )
      }

      // Save button
      IconButton(
        onClick = onSave,
        enabled = undoEnabled,
        variant = IconButtonVariant.Ghost,
        shape = CircleShape
      ) {
        Icon(
          imageVector = Icons.Rounded.Check,
          contentDescription = "Save",
          tint = if (undoEnabled)
            AppTheme.colors.primary
          else
            AppTheme.colors.primary.copy(.6f)
        )
      }
    },
    colors = TopAppBarDefaults.topAppBarColors(containerColor = AppTheme.colors.surface),
    modifier = modifier
  )
}

/**
 * Composable for the new bottom bar with pen customization buttons
 */
@Composable
fun SignatureBottomBar(
  currentColor: Color,
  currentStrokeWidth: Float,
  onSizeClick: () -> Unit,
  onColorClick: () -> Unit
) {
  Row(
    modifier = Modifier
      .fillMaxWidth()
      .background(AppTheme.colors.surface)
      .padding(horizontal = 32.dp, vertical = 8.dp),
    horizontalArrangement = Arrangement.End,
    verticalAlignment = Alignment.CenterVertically
  ) {
    Spacer(modifier = Modifier.weight(1f))

    // Size button
    Box(
      modifier = Modifier
        .size(44.dp)
        .clip(CircleShape)
        .clickable(onClick = onSizeClick),
      contentAlignment = Alignment.Center
    ) {
      Icon(
        imageVector = Icons.Rounded.LineWeight,
        contentDescription = "Pen Size",
        tint = AppTheme.colors.onSurface,
        modifier = Modifier.size(24.dp)
      )
    }

    Spacer(modifier = Modifier.width(8.dp))

    // Color button with current color indicator
    Box(
      modifier = Modifier
        .size(44.dp)
        .clip(CircleShape)
        .clickable(onClick = onColorClick),
      contentAlignment = Alignment.Center
    ) {
      // Current color indicator
      Box(
        modifier = Modifier
          .size(24.dp)
          .border(
            width = 2.dp,
            color = AppTheme.colors.onSurface,
            shape = CircleShape
          )
          .clip(CircleShape),
        contentAlignment = Alignment.Center
      ) {
        Box(
          modifier = Modifier
            .size(15.dp)
            .clip(CircleShape)
            .background(currentColor)
        )
      }
    }
  }
}

/**
 * Composable for the size selection popup
 */
@Composable
fun SizeSelectionPopup(
  currentStrokeWidth: Float,
  strokeWidths: List<Float>,
  onStrokeWidthSelected: (Float) -> Unit,
  onDismiss: () -> Unit,
  modifier: Modifier = Modifier
) {
  Box(modifier) {
    Card(
      modifier = Modifier
        .width(280.dp)
        .wrapContentHeight(),
      shape = RoundedCornerShape(12.dp)
    ) {
      Column(
        modifier = Modifier
          .padding(16.dp)
          .fillMaxWidth()
      ) {
        Text(
          text = LocalStrings.current.size,
          style = AppTheme.typography.h3,
          modifier = Modifier.padding(bottom = 16.dp)
        )

        Row(
          modifier = Modifier.fillMaxWidth(),
          verticalAlignment = Alignment.CenterVertically
        ) {
          // Thin line icon
          Icon(
            painter = painterResource(ResDrawable.ic_stroke_width_min),
            contentDescription = "Thin Line",
            modifier = Modifier.size(26.dp)
          )

          // Slider for size selection
          var sliderPosition by remember { mutableFloatStateOf(currentStrokeWidth) }

          Slider(
            value = sliderPosition,
            onValueChange = {
              sliderPosition = it
            },
            onValueChangeFinished = {
              onStrokeWidthSelected(sliderPosition)
            },
            valueRange = strokeWidths.first()..strokeWidths.last(),
            steps = 0, // 0 steps means continuous slider
            colors = SliderDefaults.colors(
              thumbColor = AppTheme.colors.primary,
              activeTrackColor = AppTheme.colors.primary,
              inactiveTrackColor = AppTheme.colors.primary.copy(alpha = 0.3f)
            ),
            modifier = Modifier
              .weight(1f)
              .padding(horizontal = 8.dp)
          )

          // Thick line icon
          Icon(
            painter = painterResource(ResDrawable.ic_stroke_width_max),
            contentDescription = "Thick Line",
            modifier = Modifier.size(26.dp)
          )
        }
      }
    }

    IconButton(
      onClick = onDismiss,
      modifier = Modifier.align(Alignment.TopEnd),
      variant = IconButtonVariant.Ghost,
      shape = CircleShape
    ) {
      Icon(imageVector = Icons.Rounded.KeyboardArrowDown)
    }
  }

}

/**
 * Composable for the color selection popup
 */
@Composable
fun ColorSelectionPopup(
  currentColor: Color,
  colors: List<Color>,
  onColorSelected: (Color) -> Unit,
  onDismiss: () -> Unit,
  modifier: Modifier = Modifier
) {
  Box(modifier) {
    Card(
      modifier = Modifier
        .width(280.dp)
        .wrapContentHeight(),
      shape = RoundedCornerShape(12.dp)
    ) {
      Column(
        modifier = Modifier
          .padding(16.dp)
          .fillMaxWidth()
      ) {
        Text(
          text = LocalStrings.current.color,
          style = AppTheme.typography.h3,
          modifier = Modifier.padding(bottom = 16.dp)
        )

        // Color selection grid
        Row(
          modifier = Modifier.fillMaxWidth(),
          horizontalArrangement = Arrangement.SpaceEvenly
        ) {

          colors.forEach { color ->
            // Color button with current color indicator
            Box(
              modifier = Modifier
                .size(36.dp)
                .clip(CircleShape)
                .border(
                  width = if (currentColor == color) (2.5).dp else 0.dp,
                  color = if (currentColor == color) AppTheme.colors.onSurface else Color.Transparent,
                  shape = CircleShape
                )
                .clickable { onColorSelected(color) },
              contentAlignment = Alignment.Center
            ) {
              Box(
                modifier = Modifier
                  .size(24.dp)
                  .clip(CircleShape)
                  .background(color)
              )
            }
          }
        }
      }
    }

    IconButton(
      onClick = onDismiss,
      modifier = Modifier.align(Alignment.TopEnd),
      variant = IconButtonVariant.Ghost,
      shape = CircleShape
    ) {
      Icon(imageVector = Icons.Rounded.KeyboardArrowDown)
    }
  }
}

/**
 * Container composable for the signature pad that can be used in other screens
 * This version uses an Activity instead of a DialogNode
 */
@Composable
fun SignaturePadContainer(
  onSignatureCreated: (Uri) -> Unit,
  content: @Composable (startSignaturePad: () -> Unit) -> Unit
) {
  val context = LocalContext.current

  // Create the activity launcher
  val signatureLauncher = rememberLauncherForActivityResult(
    contract = GetSignatureContract(),
    onResult = { uri ->
      uri?.let { onSignatureCreated(it) }
    }
  )

  val startSignaturePad = remember {
    {
      // Launch the signature pad activity
      signatureLauncher.launch(Unit)
      skipSplash()
    }
  }

  Box(contentAlignment = Alignment.Center) {
    content(startSignaturePad)
  }
}
