package com.example.pdf.ui.node.image_picker

import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.rounded.ArrowBackIos
import androidx.compose.material.icons.filled.CameraAlt
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.LifecycleStartEffect
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.biz.ad.banner.BannerAd
import com.example.pdf.biz.ad.banner.BannerAdPlace
import com.example.pdf.biz.ad.interstitial.interstitialAdRegister
import com.example.pdf.coil.CoilImage
import com.example.pdf.guia.ScreenNode
import com.example.pdf.guia.UseStatusBarDarkIcons
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.*
import com.example.pdf.lumo.components.card.CardDefaults
import com.example.pdf.lumo.components.topbar.TopBar
import com.example.pdf.mvi_ui_model.koinUiModel
import com.example.pdf.ui.feature.loading.LoadingDialog
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import org.orbitmvi.orbit.compose.collectAsState

@Parcelize
class ImagePickerNode(
  @IgnoredOnParcel private val onImagesSelected: (List<Uri>) -> Unit = {}
) : ScreenNode("image_picker") {

  @Composable
  override fun Content(navigator: Navigator) {
    UseStatusBarDarkIcons()

    val uiModel: ImagePickerUiModel = koinUiModel()
    val uiState by uiModel.collectAsState()

    val (onTryToShowInterAdAndNavAction, onBackAction)
      = interstitialAdRegister(navigator)

    LifecycleStartEffect(Unit) {
      uiModel.onLoadAlbums()

      onStopOrDispose { }
    }

    // 只在完全没有数据时显示全屏加载
    if (uiState.isLoading && uiState.albums.isEmpty() && uiState.images.isEmpty()) {
      LoadingDialog()
    }

    ImagePickerContent(
      uiState = uiState,
      onBack = onBackAction,
      onConfirm = { selectedImages ->
        onTryToShowInterAdAndNavAction("confirm_image_picker_selected") {
          val uris = selectedImages.map { it.uri }
          navigator.pop()
          onImagesSelected(uris)
        }
      },
      onAlbumSelected = uiModel::onAlbumSelected,
      onAlbumDropdownToggle = uiModel::onAlbumDropdownToggle,
      onAlbumDropdownDismiss = uiModel::onAlbumDropdownDismiss,
      onImageSelected = uiModel::onImageSelected,
      onImageRemoved = uiModel::onImageRemoved
    )
  }
}

@Composable
private fun ImagePickerContent(
  uiState: ImagePickerUiState,
  onBack: () -> Unit,
  onConfirm: (List<ImageItem>) -> Unit,
  onAlbumSelected: (ImageAlbum) -> Unit,
  onAlbumDropdownToggle: () -> Unit,
  onAlbumDropdownDismiss: () -> Unit,
  onImageSelected: (ImageItem) -> Unit,
  onImageRemoved: (ImageItem) -> Unit
) {
  val strings = LocalStrings.current

  Scaffold(
    topBar = {
      TopAppBar(
        title = {
          // Album dropdown
          AlbumDropdown(
            selectedAlbum = uiState.selectedAlbum,
            albums = uiState.albums,
            expanded = uiState.showAlbumDropdown,
            onExpandedChange = { if (it) onAlbumDropdownToggle() else onAlbumDropdownDismiss() },
            onAlbumSelected = onAlbumSelected,
          )
        },
        navigationIcon = {
          IconButton(
            onClick = onBack,
            variant = IconButtonVariant.Ghost,
            shape = CircleShape
          ) {
            Icon(
              imageVector = Icons.AutoMirrored.Rounded.ArrowBackIos,
              tint = AppTheme.colors.onBackground
            )
          }
        },
        actions = {
          Button(
            onClick = { onConfirm(uiState.selectedImages) },
            enabled = uiState.selectedImages.isNotEmpty(),
            modifier = Modifier.scale(.85f).clip(CardDefaults.Shape)
          ) {
            Text(
              text = "${strings.import} (${uiState.selectedImages.size})",
              fontWeight = FontWeight.Medium
            )
          }
        },
        colors = TopAppBarDefaults.topAppBarColors(containerColor = AppTheme.colors.surface)
      )
    },
    bottomBar = {
      Column(Modifier.navigationBarsPadding()) {
        if (uiState.selectedImages.isNotEmpty()) {
          SelectedImagesBottomBar(
            selectedImages = uiState.selectedImages,
            onImageRemoved = onImageRemoved,
          )
        }

        BannerAd(BannerAdPlace.ImagePicker)
      }
    }
  ) { paddingValues ->
    LazyVerticalGrid(
      columns = GridCells.Fixed(3),
      modifier = Modifier
        .fillMaxSize()
        .padding(paddingValues)
        .padding(horizontal = 4.dp),
      horizontalArrangement = Arrangement.spacedBy(8.dp),
      verticalArrangement = Arrangement.spacedBy(8.dp),
      contentPadding = PaddingValues(8.dp)
    ) {
      // Images
      items(uiState.images) { image ->
        val selectedIndex = uiState.selectedImages.indexOf(image)
        ImageGridItem(
          image = image,
          isSelected = selectedIndex >= 0,
          selectedIndex = if (selectedIndex >= 0) selectedIndex + 1 else 0, // 1-based index
          onClick = { onImageSelected(image) }
        )
      }
    }
  }
}

@Composable
private fun CameraPlaceholder() {
  Box(
    modifier = Modifier
      .aspectRatio(1f)
      .clip(RoundedCornerShape(8.dp))
      .background(AppTheme.colors.disabled),
    contentAlignment = Alignment.Center
  ) {
    Icon(
      imageVector = Icons.Default.CameraAlt,
      tint = AppTheme.colors.textSecondary,
      modifier = Modifier.size(32.dp)
    )
  }
}

@Composable
private fun ImageGridItem(
  image: ImageItem,
  isSelected: Boolean,
  selectedIndex: Int = 0, // 选中序号，0表示未选中
  onClick: () -> Unit
) {
  Box(
    modifier = Modifier
      .aspectRatio(1f)
      .border(
        width = 2.dp,
        color = if (isSelected) AppTheme.colors.primary else Color.Transparent,
        shape = RoundedCornerShape(8.dp)
      )
      .clip(RoundedCornerShape(8.dp))
      .clickable { onClick() }
  ) {
    CoilImage(
      data = image.uri,
      modifier = Modifier.fillMaxSize(),
      contentScale = ContentScale.Crop
    )

    // Selection overlay
    if (isSelected) {
      Box(
        modifier = Modifier
          .fillMaxSize()
          .background(AppTheme.colors.primary.copy(alpha = 0.1f))
      )
    }

    // Selection indicator in top-right corner
    Box(
      modifier = Modifier
        .align(Alignment.TopEnd)
        .padding(8.dp)
        .size(24.dp)
        .clip(CircleShape)
        .background(
          if (isSelected) AppTheme.colors.primary else Color.White.copy(alpha = 0.8f)
        )
        .border(
          width = 2.dp,
          color = if (isSelected) AppTheme.colors.primary else Color.Gray,
          shape = CircleShape
        ),
      contentAlignment = Alignment.Center
    ) {
      if (isSelected) {
        Icon(
          imageVector = Icons.Default.Check,
          tint = Color.White,
          modifier = Modifier.size(16.dp)
        )
      }
    }

    // Selection index in top-left corner
    if (isSelected && selectedIndex > 0) {
      Box(
        modifier = Modifier
          .align(Alignment.TopStart)
          .padding(8.dp)
          .size(24.dp)
          .clip(CircleShape)
          .background(AppTheme.colors.primary),
        contentAlignment = Alignment.Center
      ) {
        Text(
          text = selectedIndex.toString(),
          style = AppTheme.typography.body3,
          color = Color.White
        )
      }
    }
  }
}

@Composable
private fun SelectedImagesBottomBar(
  selectedImages: List<ImageItem>,
  onImageRemoved: (ImageItem) -> Unit,
  modifier: Modifier = Modifier
) {
  Surface(
    modifier = modifier.fillMaxWidth(),
    color = AppTheme.colors.surface,
  ) {
    HorizontalDivider()

    LazyRow(
      modifier = Modifier.padding(vertical = 16.dp),
      horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
      item {
        Spacer(modifier = Modifier.width(8.dp))
      }

      items(selectedImages) { image ->
        SelectedImageItem(
          image = image,
          onRemove = { onImageRemoved(image) }
        )
      }

      item {
        Spacer(modifier = Modifier.width(8.dp))
      }
    }
  }
}

@Composable
private fun SelectedImageItem(
  image: ImageItem,
  onRemove: () -> Unit
) {
  Box(
    modifier = Modifier.size(60.dp)
  ) {
    CoilImage(
      data = image.uri,
      modifier = Modifier
        .fillMaxSize()
        .clip(RoundedCornerShape(8.dp)),
      contentScale = ContentScale.Crop
    )

    // Remove button
    IconButton(
      onClick = onRemove,
      modifier = Modifier
        .align(Alignment.TopEnd)
        .size(20.dp)
        .offset(x = 6.dp, y = (-6).dp),
      variant = IconButtonVariant.Destructive
    ) {
      Icon(
        imageVector = Icons.Rounded.Close,
        tint = Color.White,
        modifier = Modifier.size(12.dp)
      )
    }
  }
}
