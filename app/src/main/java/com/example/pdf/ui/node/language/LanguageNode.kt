package com.example.pdf.ui.node.language

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.rounded.ArrowBackIos
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.LocaleSupport
import com.example.pdf.biz.ad.banner.BannerAd
import com.example.pdf.biz.ad.banner.BannerAdPlace
import com.example.pdf.biz.ad.interstitial.interstitialAdRegister
import com.example.pdf.biz.ad.nat1ve.NativeAd
import com.example.pdf.biz.ad.nat1ve.NativeAdPlace
import com.example.pdf.guia.ScreenNode
import com.example.pdf.guia.UseStatusBarDarkIcons
import com.example.pdf.localeEmoji
import com.example.pdf.localeLanguage
import com.example.pdf.lumo.AppColors
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.OrangeLight
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.IconButton
import com.example.pdf.lumo.components.IconButtonVariant
import com.example.pdf.lumo.components.Scaffold
import com.example.pdf.lumo.components.Surface
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.card.CardDefaults
import com.example.pdf.lumo.components.topbar.TopBar
import com.example.pdf.lumo.components.topbar.TopBarDefaults
import com.example.pdf.mvi_ui_model.koinUiModel
import com.example.pdf.ui.composable.BlankSpacer
import com.roudikk.guia.backstack.NavBackHandler
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import kotlinx.parcelize.Parcelize
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect
import java.util.Locale

@Parcelize
class LanguageNode(
  private val showBackIcon: Boolean = false
) : ScreenNode("language") {
  @Composable
  override fun Content(navigator: Navigator) {
    UseStatusBarDarkIcons()

    val context = LocalContext.current
    val uiModel: LanguageUiModel = koinUiModel()
    val uiState by uiModel.collectAsState()

    val composeLocale = androidx.compose.ui.text.intl.Locale.current

    LaunchedEffect(composeLocale) {
      uiModel.onRefresh(composeLocale.toJavaLocal())
    }

    val (onTryToShowInterAdAndNavAction, _) = interstitialAdRegister(navigator)

    val onBack = remember {
      {
        navigator.pop()
        Unit
      }
    }

    uiModel.collectSideEffect {
      when (it) {
        is LanguageSideEffect.ChangeLanguageAndBack -> {
          onTryToShowInterAdAndNavAction("set_language_finish") {
            onBack()
          }
        }
      }
    }

    NavBackHandler {
      if (showBackIcon) {
        onBack()
      }
    }

    LanguageContent(
      composeLocale = composeLocale,
      showBackIcon = showBackIcon,
      viewState = uiState,
      onBack = onBack,
      onLanguageSelect = uiModel::onLanguageSelect,
      onConfirmLanguage = { uiModel.onConfirmLanguage(context) }
    )
  }
}

@Composable
private fun LanguageContent(
  composeLocale: androidx.compose.ui.text.intl.Locale,
  showBackIcon: Boolean,
  viewState: LanguageUiState,
  onBack: () -> Unit,
  onLanguageSelect: (Locale) -> Unit,
  onConfirmLanguage: () -> Unit,
) {
  Scaffold(
    topBar = {
      Column {
        LanguageTopBar(
          showBackIcon = showBackIcon,
          onBack = onBack,
          onConfirmLanguage = onConfirmLanguage
        )
        BannerAd(adPlace = BannerAdPlace.Language)
      }
    },
    bottomBar = {
      NativeAd(adPlace = NativeAdPlace.Language, modifier = Modifier.navigationBarsPadding())
    },
    containerColor = AppTheme.colors.secondary.copy(.15f)
  ) {
    val lazyListState = rememberLazyListState()
    var hasLazyListRender by remember { mutableStateOf(false) }

    LaunchedEffect(composeLocale, hasLazyListRender) {
      if (hasLazyListRender) {
        val compatibleLanguage = LocaleSupport.compatibleLanguage(composeLocale.toJavaLocal())
        val indexOf = viewState.localeList.indexOf(compatibleLanguage)
        lazyListState.scrollToItem(
          if (indexOf < 0) {
            0
          } else indexOf * 2
        )
      }
    }

    LazyColumn(
      state = lazyListState,
      modifier = Modifier
        .fillMaxSize()
        .padding(it),
    ) {
      item {
        hasLazyListRender = true
        Spacer(modifier = Modifier.height(16.dp))
      }

      viewState.localeList.forEach { locale: Locale ->
        item {
          LanguageSelectItem(
            onClick = onLanguageSelect,
            locale = locale,
            selectedLocale = viewState.selectedLocale,
            modifier = Modifier.padding(horizontal = 16.dp)
          )
        }
        item {
          Spacer(modifier = Modifier.height(12.dp))
        }
      }

      item {
        Spacer(modifier = Modifier.height(16.dp))
      }
    }
  }
}

@Composable
private fun LanguageTopBar(
  showBackIcon: Boolean,
  onBack: () -> Unit,
  onConfirmLanguage: () -> Unit
) {
  val strings = LocalStrings.current

  TopBar(
    colors = TopBarDefaults.topBarColors(containerColor = AppTheme.colors.surface)
  ) {
    Row(
      modifier = Modifier
        .fillMaxWidth()
        .padding(horizontal = 8.dp),
      verticalAlignment = Alignment.CenterVertically
    ) {
      if (showBackIcon) {
        IconButton(onClick = onBack, shape = CircleShape, variant = IconButtonVariant.Ghost) {
          Icon(imageVector = Icons.AutoMirrored.Rounded.ArrowBackIos)
        }

        BlankSpacer(8.dp)
      } else {
        BlankSpacer(16.dp)
      }

      Text(
        text = strings.language,
        modifier = Modifier.weight(1f),
        style = AppTheme.typography.h2
      )

      androidx.compose.material3.TextButton(
        onClick = onConfirmLanguage,
        colors = androidx.compose.material3.ButtonDefaults.textButtonColors(contentColor = AppTheme.colors.primary)
      ) {
        androidx.compose.material3.Text(
          text = strings.ok,
          fontSize = 17.sp,
          fontWeight = FontWeight.Medium
        )
      }

      BlankSpacer(4.dp)
    }
  }
}

@Composable
private fun LanguageSelectItem(
  onClick: (Locale) -> Unit,
  locale: Locale,
  selectedLocale: Locale,
  modifier: Modifier = Modifier,
) {
  val (border, color) = if (selectedLocale == locale) {
    BorderStroke(
      width = 2.dp,
      color = AppColors.primary
    ) to OrangeLight
  } else null to AppColors.surface

  val (emoji, languageText) = remember(locale) {
    locale.localeEmoji() to locale.localeLanguage()
  }

  Surface(
    onClick = { onClick(locale) },
    border = border,
    color = color,
    shape = CardDefaults.Shape,
    modifier = modifier
  ) {
    Row(
      modifier = Modifier
        .fillMaxWidth()
        .padding(horizontal = 24.dp, vertical = 16.dp)
    ) {
      Text(
        text = "$emoji    $languageText",
        modifier = Modifier.weight(1f),
        style = AppTheme.typography.h3.copy(fontWeight = FontWeight.Medium, fontSize = 18.sp),
        textAlign = TextAlign.Start
      )
    }
  }
}


fun androidx.compose.ui.text.intl.Locale.toJavaLocal(): Locale {
  return Locale(this.language, this.region)
}


@Preview
@Composable
private fun LanguageSelectItemPreview() {
  AppTheme {
    LanguageSelectItem(
      onClick = {},
      locale = LocaleSupport.Zh_TW,
      selectedLocale = LocaleSupport.Zh_TW
    )
  }
}