import android.net.Uri
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.ZoomIn
import androidx.compose.material.icons.filled.ZoomOut
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Slider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import java.io.InputStream

/**
 * A Composable function that displays the contents of a DOCX file.
 * It uses mammoth.js library to convert DOCX to HTML and displays it in a WebView.
 *
 * @param uri The URI of the DOCX file to display
 * @param modifier Modifier for styling
 */
@Composable
fun DocxViewer(uri: Uri, modifier: Modifier = Modifier) {
  val context = LocalContext.current
  var isLoading by remember { mutableStateOf(true) }
  var errorMessage by remember { mutableStateOf<String?>(null) }
  var htmlContent by remember { mutableStateOf("") }
  var searchQuery by remember { mutableStateOf(TextFieldValue("")) }
  var zoomLevel by remember { mutableFloatStateOf(1.0f) }
  var isSearchVisible by remember { mutableStateOf(false) }
  var webViewRef = remember { mutableStateOf<WebView?>(null) }

  LaunchedEffect(uri) {
    isLoading = true
    errorMessage = null

    try {
      // Read the file content
      val inputStream = withContext(Dispatchers.IO) {
        context.contentResolver.openInputStream(uri)
      }

      if (inputStream != null) {
        val base64Data = withContext(Dispatchers.IO) {
          inputStreamToBase64(inputStream)
        }

        // Prepare HTML with embedded mammoth.js
        htmlContent = """
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <script src="https://cdnjs.cloudflare.com/ajax/libs/mammoth/1.6.0/mammoth.browser.min.js"></script>
                        <style>
                            body { 
                                font-family: Arial, sans-serif; 
                                line-height: 1.6;
                                margin: 15px;
                            }
                            img { max-width: 100%; height: auto; }
                            table { border-collapse: collapse; width: 100%; }
                            table, th, td { border: 1px solid #ddd; }
                            th, td { padding: 8px; text-align: left; }
                            
                            /* Preserve document formatting */
                            #content { max-width: 800px; margin: 0 auto; }
                            #content h1, #content h2, #content h3, 
                            #content h4, #content h5, #content h6 { text-align: center; }
                            #content p[style*="text-align: center"], 
                            #content div[style*="text-align: center"] { text-align: center; }
                            #content p[style*="text-align: right"], 
                            #content div[style*="text-align: right"] { text-align: right; }
                            #content p[style*="text-align: justify"], 
                            #content div[style*="text-align: justify"] { text-align: justify; }
                            
                            /* Search highlight styles */
                            mark, .highlight {
                                background-color: yellow;
                                color: black;
                                padding: 2px;
                                border-radius: 2px;
                            }
                            mark.current, .highlight.current {
                                background-color: orange;
                            }
                        </style>
                    </head>
                    <body>
                        <div id="content">Loading document...</div>
                        
                        <script>
                            // Convert base64 to array buffer
                            function base64ToArrayBuffer(base64) {
                                const binaryString = window.atob(base64);
                                const bytes = new Uint8Array(binaryString.length);
                                for (let i = 0; i < binaryString.length; i++) {
                                    bytes[i] = binaryString.charCodeAt(i);
                                }
                                return bytes.buffer;
                            }
                            
                            // Process the document when page loads
                            document.addEventListener('DOMContentLoaded', function() {
                                try {
                                    const arrayBuffer = base64ToArrayBuffer("$base64Data");
                                    
                                    mammoth.convertToHtml({arrayBuffer: arrayBuffer}, 
                                        { 
                                            styleMap: [
                                                "p[style-name='centered'] => p[style='text-align: center']",
                                                "p[style-name='right'] => p[style='text-align: right']",
                                                "p[style-name='justify'] => p[style='text-align: justify']"
                                            ]
                                        })
                                        .then(function(result) {
                                            document.getElementById("content").innerHTML = result.value;
                                            // Apply additional formatting
                                            preserveDocumentFormatting();
                                            // Log any warnings
                                            console.log(result.messages);
                                        })
                                        .catch(function(error) {
                                            document.getElementById("content").innerHTML = 
                                                "<p>Error converting document: " + error.message + "</p>";
                                            console.error(error);
                                        });
                                } catch (e) {
                                    document.getElementById("content").innerHTML = 
                                        "<p>Error processing document: " + e.message + "</p>";
                                    console.error(e);
                                }
                            });
                            
                            // Search functionality
                            var currentHighlightIndex = -1;
                            var totalHighlights = 0;
                            
                            function escapeRegExp(str) {
                                return str.replace(/[\.\*\+\?\^\${'$'}\{\}\(\)\|\[\]\\]/g, '\\${'$'}&');
                            }
                            
                            function searchDocument(searchText) {
                                // Clear previous highlights
                                clearHighlights();
                                
                                if (!searchText || searchText.trim() === "") return;
                                
                                var contentDiv = document.getElementById("content");
                                var content = contentDiv.innerHTML;
                                
                                // Create a temporary div to search through
                                var tempDiv = document.createElement("div");
                                tempDiv.innerHTML = content;
                                
                                // Find and highlight all instances of the search text
                                highlightText(tempDiv, searchText);
                                
                                // Replace the content with the highlighted version
                                contentDiv.innerHTML = tempDiv.innerHTML;
                                
                                // Get all highlighted elements
                                var highlights = document.querySelectorAll('.highlight');
                                totalHighlights = highlights.length;
                                
                                if (totalHighlights > 0) {
                                    currentHighlightIndex = 0;
                                    navigateToHighlight(currentHighlightIndex);
                                }
                                
                                // Return results info
                                return { total: totalHighlights, current: currentHighlightIndex + 1 };
                            }
                            
                            function highlightText(element, searchText) {
                                if (element.nodeType === 3) { // Text node
                                    var text = element.nodeValue;
                                    var regex = new RegExp(escapeRegExp(searchText), 'gi');
                                    
                                    if (regex.test(text)) {
                                        var span = document.createElement('span');
                                        span.innerHTML = text.replace(regex, function(match) {
                                            return '<span class="highlight">' + match + '</span>';
                                        });
                                        
                                        if (element.parentNode) {
                                            element.parentNode.replaceChild(span, element);
                                        }
                                    }
                                } else if (element.nodeType === 1) { // Element node
                                    // Skip script tags
                                    if (element.tagName.toLowerCase() === 'script') return;
                                    
                                    // Recursively process child nodes
                                    for (var i = 0; i < element.childNodes.length; i++) {
                                        highlightText(element.childNodes[i], searchText);
                                    }
                                }
                            }
                            
                            function clearHighlights() {
                                var highlights = document.querySelectorAll('.highlight');
                                for (var i = 0; i < highlights.length; i++) {
                                    var h = highlights[i];
                                    var parent = h.parentNode;
                                    parent.replaceChild(document.createTextNode(h.textContent), h);
                                    parent.normalize(); // Merge adjacent text nodes
                                }
                                currentHighlightIndex = -1;
                                totalHighlights = 0;
                            }
                            
                            function navigateToHighlight(index) {
                                if (totalHighlights === 0) return;
                                
                                // Remove current highlight marker
                                var currentHighlights = document.querySelectorAll('.highlight.current');
                                for (var i = 0; i < currentHighlights.length; i++) {
                                    currentHighlights[i].classList.remove('current');
                                }
                                
                                // Set new current highlight
                                var highlights = document.querySelectorAll('.highlight');
                                if (highlights[index]) {
                                    highlights[index].classList.add('current');
                                    highlights[index].scrollIntoView({
                                        behavior: 'smooth',
                                        block: 'center'
                                    });
                                }
                            }
                            
                            function nextHighlight() {
                                if (totalHighlights === 0) return;
                                currentHighlightIndex = (currentHighlightIndex + 1) % totalHighlights;
                                navigateToHighlight(currentHighlightIndex);
                                return { current: currentHighlightIndex + 1, total: totalHighlights };
                            }
                            
                            function previousHighlight() {
                                if (totalHighlights === 0) return;
                                currentHighlightIndex = (currentHighlightIndex - 1 + totalHighlights) % totalHighlights;
                                navigateToHighlight(currentHighlightIndex);
                                return { current: currentHighlightIndex + 1, total: totalHighlights };
                            }
                            
                            // Format preservation function
                            function preserveDocumentFormatting() {
                                // Detect center-aligned paragraphs that might not have explicit styling
                                var contentElement = document.getElementById("content");
                                
                                // Find paragraphs that have Word-specific center alignment classes
                                var wordElements = contentElement.querySelectorAll("[class*='align-center'], [class*='center'], .AlignCenter");
                                for (var i = 0; i < wordElements.length; i++) {
                                    wordElements[i].style.textAlign = "center";
                                }
                                
                                // Handle headings (often centered in documents)
                                var headings = contentElement.querySelectorAll("h1, h2, h3");
                                for (var i = 0; i < headings.length; i++) {
                                    // Only center headings that don't already have explicit alignment
                                    if (!headings[i].style.textAlign) {
                                        headings[i].style.textAlign = "center";
                                    }
                                }
                                
                                // Detect lists and apply proper indentation
                                var lists = contentElement.querySelectorAll("ul, ol");
                                for (var i = 0; i < lists.length; i++) {
                                    lists[i].style.paddingLeft = "30px";
                                }
                            }
                            
                            // Make functions available to Android
                            window.docxViewer = {
                                search: searchDocument,
                                next: nextHighlight,
                                previous: previousHighlight,
                                clear: clearHighlights
                            };
                        </script>
                    </body>
                    </html>
                """.trimIndent()
      } else {
        errorMessage = "Failed to open file"
      }
    } catch (e: Exception) {
      errorMessage = "Error: ${e.message}"
    } finally {
      isLoading = false
    }
  }

  Column(modifier = modifier.fillMaxSize()) {
    // Toolbar with zoom and search controls
    if (!isLoading && errorMessage == null) {
      Row(
        modifier = Modifier
          .fillMaxWidth()
          .padding(8.dp),
        verticalAlignment = Alignment.CenterVertically
      ) {
        // Zoom controls
        IconButton(onClick = {
          zoomLevel = (zoomLevel - 0.1f).coerceAtLeast(0.5f)
          webViewRef.value?.settings?.textZoom = (zoomLevel * 100).toInt()
        }) {
          Icon(Icons.Filled.ZoomOut, contentDescription = "Zoom Out")
        }

        Slider(
          value = zoomLevel,
          onValueChange = {
            zoomLevel = it
            webViewRef.value?.settings?.textZoom = (zoomLevel * 100).toInt()
          },
          valueRange = 0.5f..2.0f,
          steps = 14,
          modifier = Modifier.weight(1f)
        )

        IconButton(onClick = {
          zoomLevel = (zoomLevel + 0.1f).coerceAtMost(2.0f)
          webViewRef.value?.settings?.textZoom = (zoomLevel * 100).toInt()
        }) {
          Icon(Icons.Filled.ZoomIn, contentDescription = "Zoom In")
        }

        // Search toggle button
        IconButton(onClick = { isSearchVisible = !isSearchVisible }) {
          Icon(
            if (isSearchVisible) Icons.Filled.Close else Icons.Filled.Search,
            contentDescription = if (isSearchVisible) "Close Search" else "Search"
          )
        }
      }

      // Search bar
      if (isSearchVisible) {
        Row(
          modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 8.dp),
          verticalAlignment = Alignment.CenterVertically
        ) {
          OutlinedTextField(
            value = searchQuery,
            onValueChange = {
              searchQuery = it
              // Execute search as user types
              webViewRef.value?.evaluateJavascript(
                "window.docxViewer.search('${searchQuery.text.replace("'", "\\'")}');",
                null
              )
            },
            placeholder = { Text("Search in document...") },
            modifier = Modifier.weight(1f),
            singleLine = true
          )

          // Next/Previous buttons
          IconButton(onClick = {
            webViewRef.value?.evaluateJavascript("window.docxViewer.previous();", null)
          }) {
            Text("Prev")
          }

          IconButton(onClick = {
            webViewRef.value?.evaluateJavascript("window.docxViewer.next();", null)
          }) {
            Text("Next")
          }
        }

        Spacer(modifier = Modifier.height(8.dp))
      }
    }

    // Document content
    Box(modifier = Modifier.weight(1f)) {
      when {
        isLoading -> {
          CircularProgressIndicator(
            modifier = Modifier.align(Alignment.Center)
          )
        }
        errorMessage != null -> {
          Text(
            text = errorMessage ?: "Unknown error",
            modifier = Modifier
              .align(Alignment.Center)
              .padding(16.dp)
          )
        }
        else -> {
          AndroidView(
            factory = { context ->
              WebView(context).apply {
                settings.apply {
                  javaScriptEnabled = true
                  textZoom = (zoomLevel * 100).toInt()
                  domStorageEnabled = true
                  setSupportZoom(true)
                  builtInZoomControls = true
                  displayZoomControls = false
                }
                webViewClient = WebViewClient()
                webViewRef.value = this
              }
            },
            update = { webView ->
              webView.loadDataWithBaseURL(
                "https://example.com",
                htmlContent,
                "text/html",
                "UTF-8",
                null
              )
              webView.settings.textZoom = (zoomLevel * 100).toInt()
            },
            modifier = Modifier.fillMaxSize()
          )
        }
      }
    }
  }
}

/**
 * Converts an InputStream to Base64 encoded string
 */
private fun inputStreamToBase64(inputStream: InputStream): String {
  val buffer = ByteArrayOutputStream()
  val data = ByteArray(1024)
  var count: Int

  while (inputStream.read(data).also { count = it } != -1) {
    buffer.write(data, 0, count)
  }

  inputStream.close()
  return android.util.Base64.encodeToString(buffer.toByteArray(), android.util.Base64.NO_WRAP)
}