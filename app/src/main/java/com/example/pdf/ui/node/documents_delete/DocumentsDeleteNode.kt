package com.example.pdf.ui.node.documents_delete

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.tooling.preview.Preview
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.PreviewComposable
import com.example.pdf.android.file.documentsDelete
import com.example.pdf.guia.DialogNode
import com.example.pdf.lumo.components.AlertDialog
import com.roudikk.guia.core.Dialog
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import java.io.File

@Parcelize
class DocumentsDeleteNode(
  private val documents: List<File> = emptyList(),
) : DialogNode(tag = "documents_delete") {
  @Composable
  override fun Content(navigator: Navigator, dialog: Dialog?) {
    val scope = rememberCoroutineScope()
    val onDelete: (List<File>) -> Unit = remember {
      {
        scope.launch {
          documentsDelete(
            documents = documents,
            onSuccess = { navigator.pop() },
            onFailure = { navigator.pop() }
          )
        }
      }
    }

    DocumentsDeleteDialog(
      documents = documents,
      onConfirm = { onDelete(documents) },
      onCancel = { navigator.pop() }
    )
  }
}

@Composable
fun DocumentsDeleteDialog(
  documents: List<File>,
  onConfirm: () -> Unit,
  onCancel: () -> Unit
) {
  val strings = LocalStrings.current
  val message = if (documents.size == 1) {
    strings.deleteConfirmSingle
  } else {
    strings.deleteConfirmMultiple.format(documents.size)
  }

  AlertDialog(
    onDismissRequest = onCancel,
    onConfirmClick = onConfirm,
    title = strings.confirmDeletion,
    text = message,
    confirmButtonText = strings.delete,
    dismissButtonText = strings.cancel
  )
}


@Preview
@Composable
private fun DocumentsDeleteDialog() {
  PreviewComposable {
    DocumentsDeleteDialog(
      documents = listOf(File.createTempFile("temp", ".txt")),
      onConfirm = { },
      onCancel = { }
    )
  }
}