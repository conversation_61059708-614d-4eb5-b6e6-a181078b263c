package com.example.pdf.ui.node.document.pdf

import android.annotation.SuppressLint
import android.net.Uri
import androidx.compose.animation.Crossfade
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.rounded.Redo
import androidx.compose.material.icons.automirrored.rounded.Undo
import androidx.compose.material.icons.rounded.Check
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material.icons.rounded.Edit
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.ColorMatrix
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.core.view.postDelayed
import cafe.adriel.lyricist.LocalStrings
import com.bhuvaneshw.pdf.PdfViewer.ScrollSpeedLimit
import com.bhuvaneshw.pdf.compose.ui.PdfScrollBar
import com.bhuvaneshw.pdf.compose.ui.PdfViewer
import com.bhuvaneshw.pdf.compose.ui.PdfViewerContainer
import com.example.pdf.android.res.ResDrawable
import com.example.pdf.android.share.shareFile
import com.example.pdf.appActivity
import com.example.pdf.biz.ad.banner.BannerAd
import com.example.pdf.biz.ad.banner.BannerAdPlace
import com.example.pdf.biz.ad.interstitial.interstitialAdRegister
import com.example.pdf.coil.CoilImage
import com.example.pdf.guia.UseStatusBarDarkIcons
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Button
import com.example.pdf.lumo.components.ButtonVariant
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.IconButton
import com.example.pdf.lumo.components.IconButtonVariant
import com.example.pdf.lumo.components.NavigationBarDefaults
import com.example.pdf.lumo.components.NavigationBarItem
import com.example.pdf.lumo.components.Scaffold
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.card.CardDefaults
import com.example.pdf.lumo.components.dashedBorder
import com.example.pdf.lumo.grayTint5f
import com.example.pdf.mvi_ui_model.koinUiModel
import com.example.pdf.ui.feature.PageIndexTag
import com.example.pdf.ui.feature.document_node.DocumentNode
import com.example.pdf.ui.feature.loading.LoadingDialog
import com.example.pdf.ui.feature.pdf.PdfThumbnailHorizontalList
import com.example.pdf.ui.feature.underline.underline
import com.example.pdf.ui.node.document.CommonDocumentTopBar
import com.example.pdf.ui.node.document.CommonDocumentUiModel
import com.example.pdf.ui.node.document.CommonDocumentUiState
import com.example.pdf.ui.node.document_details.DocumentDetailsNode
import com.roudikk.guia.backstack.NavBackHandler
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.push
import kotlinx.parcelize.Parcelize
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState
import java.io.File

enum class PdfViewerInitEditMode {
  Annotate,
  Signature
}

@Parcelize
class PdfViewerNode(
  override val document: File,
  private val initEditMode: PdfViewerInitEditMode? = null
) : DocumentNode("pdf_viewer") {
  @Composable
  override fun Content(navigator: Navigator) {
    super.Content(navigator)
    UseStatusBarDarkIcons()

    val cdUiModel: CommonDocumentUiModel = koinUiModel { parametersOf(document) }
    val cdUiState by cdUiModel.collectAsState()

    val pdfViewerUiModel: PdfViewerUiModel = koinUiModel { parametersOf(document, initEditMode) }
    val pdfViewerUiState by pdfViewerUiModel.collectAsState()

    if (pdfViewerUiState.showLeaveEditModeBottomSheet) {
      LeaveEditModeBottomSheet(
        onConfirm = {
          pdfViewerUiModel.onClearChangeAndTurnOffEditMode()
          pdfViewerUiModel.onDismissLeaveEditModeBottomSheet()
        },
        onDismiss = pdfViewerUiModel::onDismissLeaveEditModeBottomSheet
      )
    }

    if (pdfViewerUiState.processing) {
      LoadingDialog()
    }

    val (onTryToShowInterAdAndNavAction, onBackAction)
      = interstitialAdRegister(navigator)

    PdfViewerContent(
      document = document,
      commonDocumentUiState = cdUiState,
      pdfViewerUiState = pdfViewerUiState,
      onBack = { pdfViewerUiModel.onBack(onBackAction) },
      onBookmarkClick = { cdUiModel.onBookmarkClick(document, it) },
      onShareClick = { appActivity?.shareFile(File(document.absolutePath)) },
      onMoreClick = { navigator.push(DocumentDetailsNode(document)) },
      onEditModeChange = pdfViewerUiModel::onEditModeChange,
      onShowSignatureChooser = pdfViewerUiModel::onShowSignatureChooser,
      onAddSignatureToChooserList = pdfViewerUiModel::onAddSignatureToChooserList,
      onAddSignatureToPdf = pdfViewerUiModel::onAddSignatureToPdf,
      onRemoveSignatureFromChooserList = pdfViewerUiModel::onRemoveSignatureFromChooserList,
      onSubmitPdfPassword = pdfViewerUiModel::onSubmitPdfPassword,
      onCancelPdfPasswordInput = pdfViewerUiModel::onCancelPdfPasswordInput,
      onSave = {
        onTryToShowInterAdAndNavAction("save_pdf") {
          pdfViewerUiState.pdfState?.pdfViewer?.downloadFile()
        }
      },
      turnOnEditMode = pdfViewerUiModel::turnOnEditMode
    )

    pdfViewerUiModel.ConfigurePdfViewer(pdfViewerUiState.pdfState)

    NavBackHandler(onBack = { pdfViewerUiModel.onBack(onBackAction) })
  }

}

@Composable
private fun PdfViewerContent(
  document: File,
  commonDocumentUiState: CommonDocumentUiState,
  pdfViewerUiState: PdfViewerUiState,
  onBack: () -> Unit,
  onBookmarkClick: (Boolean) -> Unit,
  onShareClick: () -> Unit,
  onMoreClick: () -> Unit,
  onEditModeChange: (editMode: PdfViewerEditMode) -> Unit,
  onShowSignatureChooser: () -> Unit,
  onAddSignatureToChooserList: (Uri) -> Unit,
  onAddSignatureToPdf: (Uri) -> Unit,
  onRemoveSignatureFromChooserList: (Uri) -> Unit,
  onSubmitPdfPassword: (String) -> Unit,
  onCancelPdfPasswordInput: () -> Unit,
  onSave: () -> Unit,
  turnOnEditMode: () -> Unit,
) {
  Scaffold(
    topBar = {
      if (pdfViewerUiState.enabledEditMode) {
        PdfViewerEditorTopBar(
          onBack = onBack,
          onSave = onSave,
          pdfViewerUiState = pdfViewerUiState
        )
      } else {
        CommonDocumentTopBar(
          title = "",
          isBookmarked = commonDocumentUiState.isBookmarked,
          onBackClick = onBack,
          onBookmarkClick = onBookmarkClick,
          onShareClick = onShareClick,
          onMoreClick = onMoreClick
        )
      }
    },
    bottomBar = {
      Column(Modifier.navigationBarsPadding()) {
        PdfThumbnailHorizontalList(
          thumbnails = pdfViewerUiState.pdfThumbnailItems,
          selectedPageIndex = pdfViewerUiState.currentPage - 1,
          onThumbnailSelected = { pageIndex ->
            pdfViewerUiState.pdfState?.pdfViewer?.apply {
              scrollSpeedLimit = ScrollSpeedLimit.None
              goToPage(pageIndex + 1)

              if (pdfViewerUiState.editMode != null) {
                postDelayed(400) {
                  scrollSpeedLimit = ScrollSpeedLimit.None
                }
              } else {
                postDelayed(400) {
                  scrollSpeedLimit = DefaultScrollSpeedLimit
                }
              }
            }
          },
          modifier = Modifier.fillMaxWidth()
        )

        BannerAd(BannerAdPlace.Document)
      }
    },
    floatingActionButton = {
      if (pdfViewerUiState.enabledEditMode.not()) {
        IconButton(
          onClick = turnOnEditMode,
          modifier = Modifier.padding(end = 8.dp, bottom = 8.dp)
        ) {
          Icon(imageVector = Icons.Rounded.Edit)
        }
      } else {
        PdfViewerBottomEditorBar(
          showSignatureChooser = pdfViewerUiState.showSignatureChooser,
          signatureList = pdfViewerUiState.signatureList,
          editMode = pdfViewerUiState.editMode,
          onEditModeChange = onEditModeChange,
          onShowSignatureChooser = onShowSignatureChooser,
          onAddSignatureToChooserList = onAddSignatureToChooserList,
          onAddSignatureToPdf = onAddSignatureToPdf,
          onRemoveSignatureFromChooserList = onRemoveSignatureFromChooserList,
          modifier = Modifier.offset(x = 16.dp, y = 16.dp)
        )
      }
    },
  ) { paddingValues ->
    pdfViewerUiState.pdfState?.let {
      PdfViewerContainer(
        pdfState = pdfViewerUiState.pdfState,
        pdfViewer = {
          PdfViewer(
            modifier = Modifier.offset(
              y = if (pdfViewerUiState.enabledEditMode) -NavigationBarDefaults.NavigationBarHeight / 1.8f else 0.dp
            )
          )

          Crossfade(pdfViewerUiState.showPageTag) { displayPageTag ->
            if (displayPageTag) {
              PageIndexTag(
                indexText = "${pdfViewerUiState.currentPage}/${pdfViewerUiState.pageCount}",
                modifier = Modifier
                  .alpha(.8f)
                  .align(Alignment.TopStart)
                  .padding(top = 16.dp, start = 20.dp)
              )
            }
          }
        },
        pdfToolBar = null,
        pdfScrollBar = { parentSize ->
          PdfScrollBar(parentSize = parentSize)
        },
        modifier = Modifier
          .fillMaxSize()
          .padding(paddingValues)
      )

      if (pdfViewerUiState.pdfState.passwordRequired) {

        if (pdfViewerUiState.submitPasswordTimes != -1) {
          Box(
            modifier = Modifier
              .fillMaxSize()
              .background(Color.LightGray)
          )
        }

        PdfViewerPasswordDialog(
          onConfirm = onSubmitPdfPassword,
          onCancel = onCancelPdfPasswordInput,
          onDismissRequest = {},
          isFirstTimeInput = pdfViewerUiState.submitPasswordTimes <= 0
        )
      }
    }
  }
}


@SuppressLint("ConfigurationScreenWidthHeight")
@Composable
private fun PdfViewerBottomEditorBar(
  showSignatureChooser: Boolean,
  signatureList: List<Uri>,
  editMode: PdfViewerEditMode?,
  onEditModeChange: (PdfViewerEditMode) -> Unit,
  onShowSignatureChooser: () -> Unit,
  onAddSignatureToChooserList: (Uri) -> Unit,
  onAddSignatureToPdf: (Uri) -> Unit,
  onRemoveSignatureFromChooserList: (Uri) -> Unit,
  modifier: Modifier = Modifier,
) {
  val screenWidthDp = LocalConfiguration.current.screenWidthDp

  Crossfade(
    targetState = showSignatureChooser,
    modifier = modifier.background(AppTheme.colors.surface)
  ) {
    if (it) {
      LazyRow(
        modifier = Modifier
          .background(AppTheme.colors.surface)
          .fillMaxWidth()
          .height(NavigationBarDefaults.NavigationBarHeight),
        verticalAlignment = Alignment.CenterVertically
      ) {
        item {
          Box(modifier = Modifier.padding(horizontal = 16.dp)) {
            SignaturePadContainer(
              onSignatureCreated = onAddSignatureToChooserList,
            ) { startSignaturePad ->
              if (signatureList.isEmpty()) {
                Button(
                  onClick = startSignaturePad,
                  text = "+ " + LocalStrings.current.addSignature,
                  variant = ButtonVariant.Ghost,
                  modifier = Modifier
                    .padding(top = 9.dp)
                    .height(60.dp)
                    .width((screenWidthDp - 16 * 2).dp)
                    .clip(CardDefaults.Shape)
                    .dashedBorder(
                      color = AppTheme.colors.outline,
                      strokeWidth = 2.dp,
                      cornerRadius = 12.dp,
                    )
                )
              } else {
                Button(
                  onClick = startSignaturePad,
                  text = "+ " + LocalStrings.current.add,
                  variant = ButtonVariant.Ghost,
                  modifier = Modifier
                    .padding(top = 9.dp)
                    .height(60.dp)
                    .clip(CardDefaults.Shape)
                    .dashedBorder(
                      color = AppTheme.colors.outline,
                      strokeWidth = 2.dp,
                      cornerRadius = 12.dp,
                    )
                )
              }
            }
          }
        }

        items(signatureList) { signatureUri ->
          PdfSignatureItem(
            signatureUri = signatureUri,
            onClick = { onAddSignatureToPdf(signatureUri) },
            onDelete = { onRemoveSignatureFromChooserList(signatureUri) },
            modifier = Modifier
              .padding(top = 9.dp, end = 16.dp)
              .dashedBorder(
                color = AppTheme.colors.outline,
                strokeWidth = 1.dp,
                cornerRadius = 12.dp,
              )
          )
        }
      }
    } else {
      Row(
        Modifier
          .fillMaxWidth()
          .height(NavigationBarDefaults.NavigationBarHeight)
      ) {
        val selectedItemModifier = Modifier.underline(
          color = AppTheme.colors.primary,
          strokeWidth = 2.dp,
          offsetY = 5.dp
        )

        NavigationBarItem(
          selected = editMode == PdfViewerEditMode.HIGHLIGHT,
          onClick = { onEditModeChange(PdfViewerEditMode.HIGHLIGHT) },
          icon = {
            Image(
              painter = painterResource(ResDrawable.ic_editor_highlight),
              contentDescription = LocalStrings.current.highlight,
              colorFilter = if (editMode == PdfViewerEditMode.HIGHLIGHT)
                null
              else
                ColorFilter.colorMatrix(ColorMatrix().apply { setToSaturation(0f) })
            )
          },
          label = {
            Text(
              text = LocalStrings.current.highlight,
              modifier = if (editMode == PdfViewerEditMode.HIGHLIGHT) selectedItemModifier else Modifier
            )
          },
        )

        NavigationBarItem(
          selected = editMode == PdfViewerEditMode.INK,
          onClick = { onEditModeChange(PdfViewerEditMode.INK) },
          icon = {
            Image(
              painter = painterResource(ResDrawable.ic_editor_draw),
              contentDescription = LocalStrings.current.draw,
              colorFilter = if (editMode == PdfViewerEditMode.INK)
                null
              else
                ColorFilter.colorMatrix(ColorMatrix().apply { setToSaturation(0f) })
            )
          },
          label = {
            Text(
              text = LocalStrings.current.draw,
              modifier = if (editMode == PdfViewerEditMode.INK) selectedItemModifier else Modifier
            )
          },
        )

        NavigationBarItem(
          selected = false,
          onClick = onShowSignatureChooser,
          icon = {
            Image(
              painter = painterResource(ResDrawable.ic_editor_signature),
              contentDescription = LocalStrings.current.signature,
              colorFilter = if (editMode == PdfViewerEditMode.Signature)
                null
              else
                ColorFilter.colorMatrix(ColorMatrix().apply { setToSaturation(0f) })
            )
          },
          label = {
            Text(
              text = LocalStrings.current.signature,
              modifier = if (editMode == PdfViewerEditMode.Signature) selectedItemModifier else Modifier
            )
          },
        )
      }
    }
  }
}


/**
 * A composable that displays a signature item with a dashed border and a delete button.
 *
 * @param signatureUri The URI of the signature image
 * @param onClick Callback when the signature is clicked
 * @param onDelete Callback when the delete button is clicked
 * @param modifier Modifier for the signature item
 */
@Composable
fun PdfSignatureItem(
  signatureUri: Uri,
  onClick: (Uri) -> Unit,
  onDelete: (Uri) -> Unit,
  modifier: Modifier = Modifier
) {
  Box(
    modifier = modifier.size(height = 60.dp, width = 80.dp)
  ) {
    // Signature image with dashed border
    CoilImage(
      data = signatureUri,
      contentDescription = LocalStrings.current.signature,
      contentScale = ContentScale.Crop,
      alignment = Alignment.Center,
      modifier = Modifier
        .fillMaxSize()
        .clickable(onClick = { onClick(signatureUri) })
    )

    // Delete button in the top-right corner
    Box(
      modifier = Modifier
        .size(16.dp)
        .align(Alignment.TopEnd)
        .offset(x = 6.dp, y = (-6).dp)
        .background(grayTint5f, CircleShape)
        .clip(CircleShape)
        .clickable(onClick = { onDelete(signatureUri) }),
      contentAlignment = Alignment.Center
    ) {
      Icon(
        imageVector = Icons.Rounded.Close,
        contentDescription = LocalStrings.current.deleteSignature,
        modifier = Modifier.size(14.dp),
        tint = Color.White
      )
    }
  }
}

@Composable
fun PdfViewerEditorTopBar(
  onBack: () -> Unit,
  onSave: () -> Unit,
  pdfViewerUiState: PdfViewerUiState
) {
  TopAppBar(
    title = {},
    navigationIcon = {
      IconButton(onClick = onBack, shape = CircleShape, variant = IconButtonVariant.Ghost) {
        Icon(imageVector = Icons.Rounded.Close)
      }
    },
    actions = {
      IconButton(
        onClick = { pdfViewerUiState.pdfState?.pdfViewer?.editor?.undo() },
        shape = CircleShape,
        variant = IconButtonVariant.Ghost
      ) {
        Icon(imageVector = Icons.AutoMirrored.Rounded.Undo)
      }

      IconButton(
        onClick = { pdfViewerUiState.pdfState?.pdfViewer?.editor?.redo() },
        shape = CircleShape,
        variant = IconButtonVariant.Ghost
      ) {
        Icon(imageVector = Icons.AutoMirrored.Rounded.Redo)
      }

      IconButton(onClick = onSave, shape = CircleShape, variant = IconButtonVariant.Ghost) {
        Icon(imageVector = Icons.Rounded.Check, tint = AppTheme.colors.primary)
      }
    },
    colors = TopAppBarDefaults.topAppBarColors(containerColor = AppTheme.colors.surface)
  )
}