package com.example.pdf.ui.feature.ms_office

import android.widget.FrameLayout
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import com.office.viewer.OfficeViewer
import com.sebaslogen.resaca.koin.koinViewModelScoped
import java.io.File

@Composable
fun MSOfficeViewer(
  document: File,
  modifier: Modifier = Modifier,
) {
  val uiModel: MSOfficeViewerUiModel = koinViewModelScoped()

  AndroidView(
    factory = {
      FrameLayout(it).apply {
        val officeViewer = OfficeViewer(it)
        uiModel.onConfigureOfficeViewer(officeViewer)
        officeViewer.loadFile(this, document.absolutePath)
      }
    },
    modifier = modifier
  )
}

