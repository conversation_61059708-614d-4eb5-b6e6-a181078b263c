package com.example.pdf.ui.node.documents_sort_settings

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.CheckCircle
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.pdf.PreviewComposable
import com.example.pdf.android.file.DocumentFilesManager
import com.example.pdf.android.file.getDisplayName
import com.example.pdf.guia.BottomSheetNode
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Button
import com.example.pdf.lumo.components.ButtonVariant
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.Text
import com.example.pdf.mmkv.UserSettingsKvStore
import com.example.pdf.ui.feature.bottomsheet.BottomSheetSurface
import cafe.adriel.lyricist.LocalStrings
import com.roudikk.guia.core.BottomSheet
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import kotlinx.parcelize.Parcelize
import org.koin.compose.koinInject

@Parcelize
class DocumentsSortSettingsNode : BottomSheetNode("documents_sort_settings") {
  @Composable
  override fun Content(navigator: Navigator, bottomSheet: BottomSheet?) {
    val settings: UserSettingsKvStore = koinInject()
    var selectedSortType by remember { mutableStateOf(settings.documentsSortedFlow.value) }

    val onConfirm: () -> Unit = {
      settings.documentsSortedFlow.value = selectedSortType
      navigator.pop()
    }

    DocumentsSortSettingsContent(
      selectedSortType = selectedSortType,
      onSortChanged = { selectedSortType = it },
      onCancel = navigator::pop,
      onConfirm = onConfirm
    )
  }
}

@Composable
fun DocumentsSortSettingsContent(
  selectedSortType: DocumentFilesManager.SortType,
  onSortChanged: (DocumentFilesManager.SortType) -> Unit,
  onCancel: () -> Unit,
  onConfirm: () -> Unit
) {
  BottomSheetSurface {
    Text(text = LocalStrings.current.sortBy, style = AppTheme.typography.h2, modifier = Modifier.padding(8.dp))

    DocumentFilesManager.SortType.entries.forEach { sortType ->
      SortItem(
        selected = sortType == selectedSortType,
        sortType = sortType,
        onSortChanged = onSortChanged,
      )
    }

    Row(
      modifier = Modifier
        .fillMaxWidth()
        .padding(vertical = 16.dp, horizontal = 24.dp)
    ) {
      Button(
        onClick = onCancel,
        modifier = Modifier.weight(1f),
        variant = ButtonVariant.Secondary
      ) {
        Text(text = LocalStrings.current.cancel.uppercase(), style = AppTheme.typography.buttonLarge)
      }

      Spacer(Modifier.width(12.dp))

      Button(onClick = onConfirm, modifier = Modifier.weight(1f)) {
        Text(text = LocalStrings.current.ok, style = AppTheme.typography.buttonLarge)
      }
    }
  }
}

@Composable
private fun SortItem(
  selected: Boolean,
  sortType: DocumentFilesManager.SortType,
  onSortChanged: (DocumentFilesManager.SortType) -> Unit,
  modifier: Modifier = Modifier,
) {
  val (containerColor, contentColor) = if (selected) {
    AppTheme.colors.primary.copy(alpha = 0.12f) to AppTheme.colors.primary
  } else {
    AppTheme.colors.surface to AppTheme.colors.text.copy(.75f)
  }

  Box(
    modifier = modifier
      .background(containerColor)
      .clickable { onSortChanged(sortType) }) {
    Row(
      modifier = modifier
        .fillMaxWidth()
        .padding(horizontal = 24.dp),
      verticalAlignment = Alignment.CenterVertically
    ) {
      Text(
        text = sortType.getDisplayName(),
        style = AppTheme.typography.body2,
        modifier = Modifier
          .weight(1f)
          .padding(vertical = 16.dp),
        color = contentColor
      )

      if (selected) {
        Icon(
          imageVector = Icons.Rounded.CheckCircle,
          contentDescription = LocalStrings.current.selected,
          tint = contentColor,
          modifier = Modifier.size(20.dp)
        )
      }
    }
  }
}


@Preview
@Composable
private fun DocumentsSortSettingsContentPreview() {
  PreviewComposable {
    DocumentsSortSettingsContent(
      selectedSortType = DocumentFilesManager.SortType.SIZE_ASC,
      onSortChanged = {},
      onCancel = {},
      onConfirm = {}
    )
  }
}