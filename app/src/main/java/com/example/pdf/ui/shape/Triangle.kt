package com.example.pdf.ui.shape

import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Outline
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.LayoutDirection

val TriangleShape: Shape = Triangle()

/**
 * A triangle shape for Jetpack Compose.
 * Draws a triangle with the specified orientation.
 */
class Triangle(private val orientation: Orientation = Orientation.UP) : Shape {

  enum class Orientation {
    UP, DOWN, LEFT, RIGHT
  }

  override fun createOutline(
    size: Size,
    layoutDirection: LayoutDirection,
    density: Density
  ): Outline {
    val path = Path().apply {
      when (orientation) {
        Orientation.UP -> {
          moveTo(size.width / 2f, 0f)
          lineTo(size.width, size.height)
          lineTo(0f, size.height)
        }

        Orientation.DOWN -> {
          moveTo(0f, 0f)
          lineTo(size.width, 0f)
          lineTo(size.width / 2f, size.height)
        }

        Orientation.LEFT -> {
          moveTo(0f, size.height / 2f)
          lineTo(size.width, 0f)
          lineTo(size.width, size.height)
        }

        Orientation.RIGHT -> {
          moveTo(0f, 0f)
          lineTo(size.width, size.height / 2f)
          lineTo(0f, size.height)
        }
      }
      close()
    }
    return Outline.Generic(path)
  }
}