package com.example.pdf.ui.feature.ms_office

import com.example.pdf.mvi_ui_model.UiModel
import com.office.viewer.OfficeViewer
import org.koin.android.annotation.KoinViewModel

@KoinViewModel
class MSOfficeViewerUiModel : UiModel<Unit, Nothing>(Unit) {

  private var officeViewer: OfficeViewer? = null

  fun onConfigureOfficeViewer(
    officeViewer: OfficeViewer
  ) {
    this.officeViewer = officeViewer
  }

  override fun onCleared() {
    super.onCleared()
    officeViewer?.dispose()
  }
}