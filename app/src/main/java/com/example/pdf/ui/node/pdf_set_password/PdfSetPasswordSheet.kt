package com.example.pdf.ui.node.pdf_set_password

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material.icons.rounded.Visibility
import androidx.compose.material.icons.rounded.VisibilityOff
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusManager
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.platform.SoftwareKeyboardController
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.LifecycleStartEffect
import com.example.pdf.biz.ad.interstitial.interstitialAdRegister
import com.example.pdf.guia.BottomSheetNode
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Button
import com.example.pdf.lumo.components.ButtonVariant
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.IconButton
import com.example.pdf.lumo.components.IconButtonVariant
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.progressindicators.LinearProgressIndicator
import com.example.pdf.lumo.components.textfield.TextField
import com.example.pdf.lumo.components.textfield.TextFieldDefaults
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.ui.composable.BlankSpacer
import com.example.pdf.ui.feature.bottomsheet.BottomSheetSurface
import com.example.pdf.ui.feature.loading.LoadingDialog
import com.roudikk.guia.core.BottomSheet
import com.roudikk.guia.core.Navigator
import kotlinx.parcelize.Parcelize
import org.koin.compose.koinInject
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState
import java.io.File

@Parcelize
class PdfSetPasswordBottomSheetNode(
  private val pdfFile: File
) : BottomSheetNode("pdf_set_password") {

  @Composable
  override fun Content(
    navigator: Navigator,
    bottomSheet: BottomSheet?
  ) {
    val uiModel: PdfSetPasswordUiModel = koinInject { parametersOf(pdfFile) }
    val uiState by uiModel.collectAsState()

    val (onTryToShowInterAdAndNavAction, _)
      = interstitialAdRegister(navigator)

    PdfSetPasswordContent(
      password = uiState.password,
      passwordVisible = uiState.passwordVisible,
      isLoading = uiState.isLoading,
      errorMessage = uiState.errorMessage,
      onPasswordChange = uiModel::onPasswordChange,
      onTogglePasswordVisibility = uiModel::onTogglePasswordVisibility,
      onPasswordClear = uiModel::onPasswordClear,
      onConfirm = { uiModel.onConfirm(onTryToShowInterAdAndNavAction) },
      onCancel = uiModel::onCancel
    )
  }
}

@Composable
private fun PdfSetPasswordContent(
  password: androidx.compose.ui.text.input.TextFieldValue,
  passwordVisible: Boolean,
  isLoading: Boolean,
  errorMessage: String?,
  onPasswordChange: (androidx.compose.ui.text.input.TextFieldValue) -> Unit,
  onTogglePasswordVisibility: () -> Unit,
  onPasswordClear: () -> Unit,
  onConfirm: () -> Unit,
  onCancel: () -> Unit
) {
  val focusRequester = FocusRequester()
  val keyboardController = LocalSoftwareKeyboardController.current
  val focusManager = LocalFocusManager.current

  LifecycleStartEffect(Unit) {
    onStopOrDispose {
      focusManager.clearFocus()
      keyboardController?.hide()
    }
  }

  BottomSheetSurface(
    modifier = Modifier.imePadding(),
  ) {
    Text(text = LocalStrings.current.setPassword, style = AppTheme.typography.h2)

    Spacer(modifier = Modifier.height(16.dp))

    Text(
      text = LocalStrings.current.setPasswordDescription,
      style = AppTheme.typography.body1,
      color = AppTheme.colors.onBackground.copy(alpha = 0.7f)
    )

    TextField(
      value = password,
      onValueChange = onPasswordChange,
      colors = TextFieldDefaults.colors().copy(
        unfocusedContainerColor = AppTheme.colors.secondary.copy(.25f),
        focusedContainerColor = AppTheme.colors.secondary.copy(.25f),
        cursorColor = AppTheme.colors.primary,
      ),
      modifier = Modifier
        .padding(24.dp)
        .focusRequester(focusRequester),
      placeholder = { Text(LocalStrings.current.password) },
      visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation(),
      trailingIcon = {
        Row {
          if (password.text.isNotEmpty()) {
            IconButton(
              onClick = onPasswordClear,
              variant = IconButtonVariant.Secondary,
              shape = CircleShape,
              modifier = Modifier.size(22.dp)
            ) {
              Icon(imageVector = Icons.Filled.Clear)
            }

            BlankSpacer(8.dp)
          }

          IconButton(
            onClick = onTogglePasswordVisibility,
            variant = IconButtonVariant.Secondary,
            shape = CircleShape,
            modifier = Modifier.size(22.dp)
          ) {
            Icon(imageVector = if (passwordVisible) Icons.Rounded.Visibility else Icons.Rounded.VisibilityOff)
          }
        }
      },
      singleLine = true,
      keyboardOptions = KeyboardOptions.Default.copy(imeAction = ImeAction.Done),
      keyboardActions = KeyboardActions(onDone = { onConfirm() }),
      isError = errorMessage != null
    )

    if (errorMessage != null) {
      Text(
        text = errorMessage,
        color = AppTheme.colors.error,
        style = AppTheme.typography.h4,
        modifier = Modifier
          .padding(horizontal = 24.dp)
          .offset(y = (-16).dp)
      )
    }

    Row(
      modifier = Modifier
        .fillMaxWidth()
        .padding(horizontal = 24.dp)
    ) {
      Button(
        onClick = onCancel,
        modifier = Modifier.weight(1f),
        variant = ButtonVariant.Secondary,
        enabled = !isLoading
      ) {
        Text(text = LocalStrings.current.cancel.uppercase(), style = AppTheme.typography.buttonLarge)
      }

      Spacer(Modifier.width(12.dp))

      Button(
        onClick = onConfirm,
        modifier = Modifier.weight(1f),
        enabled = password.text.isNotEmpty() && !isLoading,
        loading = isLoading
      ) {
        Text(text = LocalStrings.current.ok, style = AppTheme.typography.buttonLarge)
      }
    }

    Spacer(Modifier.height(24.dp))
  }
}