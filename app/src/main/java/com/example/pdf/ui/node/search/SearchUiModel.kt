package com.example.pdf.ui.node.search

import androidx.compose.ui.text.input.TextFieldValue
import com.example.pdf.android.file.DocumentRepository
import com.example.pdf.mmkv.UserSettingsKvStore
import com.example.pdf.mvi_ui_model.UiModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.koin.android.annotation.KoinViewModel

@KoinViewModel
class SearchUiModel(
  private val documentRepository: DocumentRepository,
  private val userSettingsKvStore: UserSettingsKvStore
) : UiModel<SearchUiState, Nothing>(SearchUiState()) {

  init {
    registerBookmarkedDocumentPathsFlow()
  }

  private fun registerBookmarkedDocumentPathsFlow() = intent {
    userSettingsKvStore.bookmarkedDocumentPathsFlow.onEach { bookmarkedPaths ->
      reduce { state.copy(bookmarkedFilePaths = bookmarkedPaths) }
    }.launchIn(uiModelScope)
  }

  fun onQueryClear() = intent {
    searchJob?.cancel()
    reduce { state.copy(query = TextFieldValue(), documentResults = emptyList()) }
  }

  private var searchJob: Job? = null
  fun onSearch(query: TextFieldValue) = intent {
    searchJob?.cancel()

    reduce { state.copy(query = query) }

    if (query.text.trim().isEmpty()) {
      reduce {
        state.copy(documentResults = emptyList())
      }
      return@intent
    }

    searchJob = uiModelScope.launch(Dispatchers.Default) {
      val results = documentRepository.documentsFlow.first().filter {
        it.name.contains(query.text, ignoreCase = true)
      }

      subIntent {
        reduce {
          state.copy(documentResults = results)
        }
      }
    }
  }
}