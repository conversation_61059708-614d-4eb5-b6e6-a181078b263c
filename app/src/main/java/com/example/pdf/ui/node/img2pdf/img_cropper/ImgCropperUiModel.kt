package com.example.pdf.ui.node.img2pdf.img_cropper

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.asAndroidBitmap
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.unit.IntSize
import com.attafitamim.krop.core.crop.CropError
import com.attafitamim.krop.core.crop.CropResult
import com.attafitamim.krop.core.crop.CropState
import com.attafitamim.krop.core.crop.CropperLoading
import com.attafitamim.krop.core.crop.ImageCropper
import com.attafitamim.krop.core.crop.ImgTransform
import com.attafitamim.krop.core.crop.createResult
import com.attafitamim.krop.core.crop.crop
import com.attafitamim.krop.core.crop.cropState
import com.attafitamim.krop.core.crop.cropperStyle
import com.attafitamim.krop.core.images.ImageSrc
import com.example.pdf.appContext
import com.example.pdf.guia.GlobalNavigator
import com.example.pdf.mvi_ui_model.UiModel
import com.example.pdf.ui.node.img2pdf.list.Img2PdfItemData
import com.example.pdf.ui.node.img2pdf.list.OnCropItemEventFlow
import com.example.pdf.ui.node.img2pdf.list.OnRemoveItemEventFlow
import com.roudikk.guia.extensions.pop
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.takeWhile
import org.koin.android.annotation.KoinViewModel
import org.koin.core.annotation.InjectedParam
import java.util.Collections

data class CropCacheState(
  val transform: ImgTransform,
  val region: Rect
)

private val imageCropperStatesCache =
  Collections.synchronizedMap(mutableMapOf<String, CropCacheState>())

fun clearImageCroppersCache() {
  imageCropperStatesCache.clear()
}

@KoinViewModel
class ImgCropperUiModel(
  @InjectedParam private val img2PdfItemData: Img2PdfItemData,
) : UiModel<ImgCropperUiState, Nothing>(ImgCropperUiState()) {

  private val imageCropper = ImageCropper()

  init {
    configureCropper()
  }

  private fun configureCropper() = intent {
    reduce { state.copy(cropper = imageCropper) }
    cropperStyle()
    img2PdfItemData.uri.toBitmap(appContext)?.let {
      val cropResult = imageCropper.crop(bmp = it.asImageBitmap())
      handleCropResult(cropResult)
    }
  }

  private fun Uri.toBitmap(context: Context): Bitmap? {
    return try {
      context.contentResolver.openInputStream(this)?.use { inputStream ->
        BitmapFactory.decodeStream(inputStream)
      }
    } catch (e: Exception) {
      e.printStackTrace()
      null
    }
  }

  @Suppress("RedundantSuspendModifier")
  private suspend fun handleCropResult(cropResult: CropResult) {
    when (cropResult) {
      is CropResult.Success -> {
        val croppedBitmap = cropResult.bitmap.asAndroidBitmap()

        val newImg2PdfItemData = img2PdfItemData.copy(
          id = img2PdfItemData.id,
          cropped = true,
          cropTimes = img2PdfItemData.cropTimes + 1,
          croppedBitmap = croppedBitmap
        )

        OnCropItemEventFlow.emit(newImg2PdfItemData)

        img2PdfItemData.croppedBitmap?.recycle()
      }

      else -> {}
    }
  }

  fun onCrop() = intent {
    state.cropper.cropState?.let { cropState ->
      imageCropperStatesCache[img2PdfItemData.id] =
        CropCacheState(cropState.transform, cropState.region)
      cropState.done(true)

      GlobalNavigator.transaction { pop() }
    }
  }

  fun onDelete() = intent {
    reduce { state.copy(showDeleteDialog = false) }
    OnRemoveItemEventFlow.emit(img2PdfItemData)
    GlobalNavigator.transaction { pop() }
  }

  fun onDisplayDeleteDialog() = intent {
    reduce { state.copy(showDeleteDialog = true) }
  }

  fun onDismissDeleteDialog() = intent {
    reduce { state.copy(showDeleteDialog = false) }
  }

  private fun ImageCropper(
    cacheState: CropCacheState? = imageCropperStatesCache[img2PdfItemData.id],
  ): ImageCropper = object : ImageCropper {
    override var cropState: CropState? by mutableStateOf(null)
    val cropStateFlow = snapshotFlow { cropState }
    override var loadingStatus: CropperLoading? by mutableStateOf(null)
    override suspend fun crop(
      maxResultSize: IntSize?,
      createSrc: suspend () -> ImageSrc?
    ): CropResult {
      cropState = null
      val src = withLoading(CropperLoading.PreparingImage) { createSrc() }
        ?: return CropError.LoadingError
      val newCrop = cropState(src) { cropState = null }
      if (cacheState != null) {
        newCrop.apply {
          transform = cacheState.transform
          region = cacheState.region
        }
      }
      cropState = newCrop
      cropStateFlow.takeWhile { it === newCrop }.collect()
      if (!newCrop.accepted) return CropResult.Cancelled
      return withLoading(CropperLoading.SavingResult) {
        val result = newCrop.createResult(maxResultSize)
        if (result == null) CropError.SavingError
        else CropResult.Success(result)
      }
    }

    inline fun <R> withLoading(status: CropperLoading, op: () -> R): R {
      return try {
        loadingStatus = status
        op()
      } finally {
        loadingStatus = null
      }
    }
  }
}