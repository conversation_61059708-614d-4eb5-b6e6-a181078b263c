package com.example.pdf.ui.feature.document_picker

import android.annotation.SuppressLint
import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import com.example.pdf.PreviewComposable
import com.example.pdf.android.file.ImportDocumentFilesHelper
import com.example.pdf.android.file.document_converter.DocumentConverter
import com.example.pdf.android.toast.showToast
import com.example.pdf.lyricist.globalStrings
import com.example.pdf.lumo.components.Button
import com.example.pdf.lumo.components.Text
import kotlinx.coroutines.launch
import java.io.File

/**
 * A container composable for the document picker functionality that allows selecting documents and returns a Uri.
 *
 * @param onPickStarted Called when document picking starts.
 * @param onPickComplete Called when document selection completes successfully with the selected document Uri. If the user cancels, the Uri will be null.
 * @param modifier Modifier to be applied to the container.
 * @param documentMimeTypes Array of MIME types for documents that can be selected.
 * @param content Composable content to be displayed as the trigger for document picking.
 */
@Composable
fun DocumentUriPickerContainer(
  onPickStarted: () -> Unit = {},
  onPickComplete: (Uri?) -> Unit, // Changed to return Uri?
  @SuppressLint("ModifierParameter") modifier: Modifier = Modifier,
  documentMimeTypes: Array<String> = ImportDocumentFilesHelper.DOCUMENT_MIME_TYPES,
  content: @Composable (startPicker: () -> Unit) -> Unit
) {

  // Create document picker launcher using rememberLauncherForActivityResult
  val documentPickerLauncher = rememberLauncherForActivityResult(
    contract = ActivityResultContracts.OpenDocument()
  ) { uri: Uri? ->
    // Directly pass the uri (which can be null if cancelled) to the callback
    onPickComplete(uri)
  }

  // Function to start the document picker that will be passed to the content
  val startPicker = remember {
    {
      onPickStarted()
      documentPickerLauncher.launch(documentMimeTypes)
    }
  }

  Box(
    modifier = modifier,
    contentAlignment = Alignment.Center
  ) {
    // Pass the startPicker function to the content
    content(startPicker)
  }
}


@Preview(showBackground = true)
@Composable
fun DocumentUriPickerContainerPreview() {
  val context = LocalContext.current
  val scope = rememberCoroutineScope()

  PreviewComposable {
    DocumentUriPickerContainer(
      onPickStarted = { },
      onPickComplete = { uri ->
        scope.launch {
          uri?.let {
            val outputDir = context.cacheDir
            // It's generally better to derive the output filename from the input URI
            // For preview purposes, a fixed name is acceptable.
            val outputFile = File(outputDir, "preview_converted.pdf")
            DocumentConverter(context).convertDocToPdfByAspose(uri, outputFile.absolutePath).apply {
              showToast(globalStrings.convertSuccessful.format(this))
            }
          }
        }
      }
    ) { startPicker ->
      Button(onClick = startPicker) {
        Text("Select Document (Uri)")
      }
    }
  }
}