package com.example.pdf.ui.node.document_details

import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.pdf.PreviewComposable
import com.example.pdf.guia.BottomSheetNode
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Text
import com.example.pdf.mmkv.UserSettingsKvStore
import com.example.pdf.ui.feature.bottomsheet.BottomSheetSurface
import com.example.pdf.ui.feature.document_item.formatDate
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.ui.feature.document_item.formatFileSize
import com.roudikk.guia.core.BottomSheet
import com.roudikk.guia.core.Navigator
import kotlinx.coroutines.flow.first
import kotlinx.parcelize.Parcelize
import org.koin.compose.koinInject
import java.io.File

@Parcelize
class DocumentDetailsNode(
  private val document: File
) : BottomSheetNode("document_details") {
  @Composable
  override fun Content(navigator: Navigator, bottomSheet: BottomSheet?) {
    val scope = rememberCoroutineScope()
    val settingsKvStore: UserSettingsKvStore = koinInject()

    var lastViewedText by remember { mutableStateOf<String?>(null) }

    LaunchedEffect(Unit) {
      val recentDocuments = settingsKvStore.recentDocumentsFlow.first()
      val recentDocumentEntity = recentDocuments.get(document.absolutePath)

      if (recentDocumentEntity != null) {
        lastViewedText = formatDate(recentDocumentEntity.lastViewedTimestamp)
      }
    }

    DocumentDetailsContent(
      document = document,
      lastViewed = lastViewedText
    )
  }
}

data class DocumentDetails(
  val fileName: String,
  val storagePath: String,
  val fileSize: String,
  val lastModified: String,
//  val lastViewed: String
)

@Composable
private fun DocumentDetailsContent(
  document: File,
  lastViewed: String?
) {
  val details = remember(document.absolutePath) {
    DocumentDetails(
      fileName = document.name,
      storagePath = document.parent ?: "N/A",
      fileSize = formatFileSize(document.length()),
      lastModified = formatDate(document.lastModified()),
    )
  }

  BottomSheetSurface {
    Text(text = LocalStrings.current.details, style = AppTheme.typography.h2)
    Spacer(modifier = Modifier.height(24.dp))

    Column(
      modifier = Modifier
        .fillMaxWidth()
        .padding(horizontal = 24.dp)
    ) {

      val titleStyle =
        AppTheme.typography.body2.copy(color = AppTheme.colors.textSecondary.copy(.65f))

      Text(text = LocalStrings.current.fileName, style = titleStyle, color = titleStyle.color)
      Spacer(modifier = Modifier.height(2.dp))
      Text(text = details.fileName, style = AppTheme.typography.h3, modifier = Modifier.basicMarquee())
      Spacer(modifier = Modifier.height(12.dp))

      Text(text = LocalStrings.current.storagePath, style = titleStyle, color = titleStyle.color)
      Spacer(modifier = Modifier.height(2.dp))
      Text(text = details.storagePath, style = AppTheme.typography.h3, modifier = Modifier.basicMarquee())
      Spacer(modifier = Modifier.height(12.dp))

      Text(text = LocalStrings.current.lastModified, style = titleStyle, color = titleStyle.color)
      Spacer(modifier = Modifier.height(2.dp))
      Text(text = details.lastModified, style = AppTheme.typography.h3)
      Spacer(modifier = Modifier.height(12.dp))

      Text(text = LocalStrings.current.lastViewed, style = titleStyle, color = titleStyle.color)
      Spacer(modifier = Modifier.height(2.dp))
      Text(text = lastViewed ?: LocalStrings.current.noData, style = AppTheme.typography.h3)
      Spacer(modifier = Modifier.height(12.dp))

      Text(text = LocalStrings.current.fileSize, style = titleStyle, color = titleStyle.color)
      Spacer(modifier = Modifier.height(2.dp))
      Text(text = details.fileSize, style = AppTheme.typography.h3)

      Spacer(modifier = Modifier.height(24.dp))
    }
  }
}


@Preview
@Composable
private fun DocumentDetailsContentPreview() {
  PreviewComposable {
    DocumentDetailsContent(
      document = File("example.txt"),
      lastViewed = null
    )
  }
}