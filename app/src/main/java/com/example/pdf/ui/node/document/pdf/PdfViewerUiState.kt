package com.example.pdf.ui.node.document.pdf

import android.net.Uri
import com.bhuvaneshw.pdf.compose.PdfState
import com.example.pdf.ui.feature.pdf.PdfThumbnailItem

sealed interface PdfViewerEditMode {
  data object HIGHLIGHT: PdfViewerEditMode
  data object INK : PdfViewerEditMode
  data object Signature : PdfViewerEditMode
}

data class PdfViewerUiState(
  val pdfState: PdfState? = null,
  val editMode: PdfViewerEditMode? = null,
  val enabledEditMode: Boolean = false,
  val showPageTag: Boolean = false,
  val pageCount: Int = 0,
  val currentPage: Int = 0,
  val pdfThumbnailItems: List<PdfThumbnailItem> = emptyList(),
  val showSignatureChooser: Boolean = false,
  val signatureList: List<Uri> = emptyList(),
  val showLeaveEditModeBottomSheet: Boolean = false,
  val processing: Boolean = false,
  val submitPasswordTimes: Int = -1,
)
