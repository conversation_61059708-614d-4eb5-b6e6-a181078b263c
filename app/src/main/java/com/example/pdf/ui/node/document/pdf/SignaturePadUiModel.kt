package com.example.pdf.ui.node.document.pdf

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import android.net.Uri
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color as ComposeColor
import androidx.core.content.FileProvider
import com.example.pdf.kermit.debugLog
import com.example.pdf.mvi_ui_model.UiModel
import io.ak1.drawbox.DrawController
import io.ak1.drawbox.rememberDrawController
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import org.koin.android.annotation.KoinViewModel
import java.io.File
import java.io.FileOutputStream
import java.util.Date
import androidx.core.graphics.createBitmap

/**
 * UI state for the signature pad
 */
data class SignaturePadUiState(
  val drawController: DrawController? = null,
  val colors: List<ComposeColor> = listOf(
    ComposeColor.Black,
    ComposeColor(0xFFFF9500), // Orange
    ComposeColor(0xFFFF3B30), // Red
    ComposeColor(0xFF007AFF), // Blue
    ComposeColor(0xFF34C759)  // Green
  ),
  val strokeWidths: List<Float> = listOf(4f, 8f, 12f, 16f, 20f),
  val currentColor: ComposeColor = ComposeColor.Black,
  val currentStrokeWidth: Float = 10f,
  val undoCount: Int = 0,
  val redoCount: Int = 0,
  val showSizeSelector: Boolean = false,
  val showColorSelector: Boolean = false,
  val processing: Boolean = false,
)

/**
 * UiModel for the signature pad
 */
@KoinViewModel
class SignaturePadUiModel : UiModel<SignaturePadUiState, Nothing>(SignaturePadUiState()) {

  /**
   * Initialize the draw controller
   */
  fun initializeDrawController(drawController: DrawController) = intent {
    reduce { state.copy(drawController = drawController) }

    drawController.trackHistory(uiModelScope) { undoCount, redoCount ->
      intent { reduce { state.copy(undoCount = undoCount, redoCount = redoCount) } }
    }

    onColorSelected(state.currentColor)
    onStrokeWidthSelected(state.currentStrokeWidth)
  }

  /**
   * Change the current pen color
   */
  fun onColorSelected(color: ComposeColor) = intent {
    reduce { state.copy(currentColor = color) }

    withContext(Dispatchers.Main.immediate) {
      state.drawController?.changeColor(color)
    }
  }

  /**
   * Change the current stroke width
   */
  fun onStrokeWidthSelected(strokeWidth: Float) = intent {
    reduce { state.copy(currentStrokeWidth = strokeWidth) }

    withContext(Dispatchers.Main.immediate) {
      state.drawController?.changeStrokeWidth(strokeWidth)
    }
  }

  /**
   * Clear the drawing
   */
  fun onClear() = intent {
    state.drawController?.reset()

    closeSelectorsInternal()
  }

  /**
   * Undo the last drawing action
   */
  fun onUndo() = intent {
    state.drawController?.unDo()

    closeSelectorsInternal()
  }

  /**
   * Redo the last undone drawing action
   */
  fun onRedo() = intent {
    state.drawController?.reDo()

    closeSelectorsInternal()
  }

  /**
   * Save the current drawing as a bitmap
   */
  fun onSave() = intent {
    reduce { state.copy(processing = true) }
    closeSelectorsInternal()
    delay(500)
    state.drawController?.saveBitmap()
  }

  /**
   * Save the signature to the cache directory and return a URI
   */
  suspend fun saveSignatureToCache(
    context: Context,
    bitmap: Bitmap
  ): Uri? = withContext(Dispatchers.IO) {
    try {
      // Create a transparent version of the bitmap
      val transparentBitmap = createTransparentBitmap(bitmap)

      // Save the bitmap to a file in the cache directory
      val timestamp = Date().time
      val filename = "signature_$timestamp.png"
      val file = File(context.cacheDir, filename)

      FileOutputStream(file).use { out ->
        transparentBitmap.compress(Bitmap.CompressFormat.PNG, 100, out)
      }

      // Recycle the transparent bitmap as we don't need it anymore
      transparentBitmap.recycle()

      // Get a content URI for the file using FileProvider
      val authority = "${context.packageName}.provider"
      debugLog("Saved signature to ${file.absolutePath}")
      return@withContext FileProvider.getUriForFile(context, authority, file)
    } catch (e: Exception) {
      debugLog(throwable = e) { "Error saving signature: ${e.message}" }
      return@withContext null
    }
  }

  fun saveSignatureToCache(
    context: Context,
    bitmap: Bitmap,
    onSaveToCache: (Uri?) -> Unit
  ) = intent {
    val cacheUri = saveSignatureToCache(context, bitmap)
    withContext(Dispatchers.Main.immediate) { onSaveToCache(cacheUri) }
  }

  /**
   * Creates a transparent version of the bitmap by removing the white background
   */
  private suspend fun createTransparentBitmap(
    sourceBitmap: Bitmap
  ): Bitmap = withContext(Dispatchers.Default) {
    val width = sourceBitmap.width
    val height = sourceBitmap.height

    // Create a new bitmap with the same dimensions as the source bitmap, with alpha channel
    val resultBitmap = createBitmap(width, height)

    // Get the pixels from the source bitmap
    val sourcePixels = IntArray(width * height)
    sourceBitmap.getPixels(sourcePixels, 0, width, 0, 0, width, height)

    // Create a new array for the result pixels
    val resultPixels = IntArray(width * height)

    // Process each pixel
    for (i in sourcePixels.indices) {
      val pixel = sourcePixels[i]

      // Extract the RGB components
      val red = Color.red(pixel)
      val green = Color.green(pixel)
      val blue = Color.blue(pixel)

      // Check if this pixel is part of the background (white or nearly white)
      if ((red >= DrawBoxBorderRGB) && (green >= DrawBoxBorderRGB) && (blue >= DrawBoxBorderRGB)) {
        // Make it transparent
        resultPixels[i] = Color.TRANSPARENT
      } else {
        // Keep the original pixel with full opacity
        resultPixels[i] = Color.argb(255, red, green, blue)
      }
    }

    // Set the processed pixels to the result bitmap
    resultBitmap.setPixels(resultPixels, 0, width, 0, 0, width, height)

    return@withContext resultBitmap
  }

  /**
   * Save the current drawing as a bitmap with transparent background
   * This uses the exportPath function from DrawController if available
   */
  fun onSaveTransparent() = intent {
    state.drawController?.saveBitmap()
  }

  /**
   * Toggle the visibility of the size selector
   */
  fun toggleSizeSelector() = intent {
    reduce {
      state.copy(
        showSizeSelector = !state.showSizeSelector,
        showColorSelector = false // Close color selector if open
      )
    }
  }

  /**
   * Toggle the visibility of the color selector
   */
  fun toggleColorSelector() = intent {
    reduce {
      state.copy(
        showColorSelector = !state.showColorSelector,
        showSizeSelector = false // Close size selector if open
      )
    }
  }

  /**
   * Close all selectors
   */
  fun closeSelectors() = intent {
    closeSelectorsInternal()
  }

  suspend fun closeSelectorsInternal() = subIntent {
    reduce {
      state.copy(
        showSizeSelector = false,
        showColorSelector = false
      )
    }
  }

  fun onDismissProcessingDialog() = intent {
    reduce { state.copy(processing = false) }
  }
}

/**
 * Remember a DrawController and initialize it in the UiModel
 */
@Composable
fun rememberSignaturePadDrawController(uiModel: SignaturePadUiModel): DrawController {
  val drawController = rememberDrawController()
  uiModel.initializeDrawController(drawController)
  return drawController
}
