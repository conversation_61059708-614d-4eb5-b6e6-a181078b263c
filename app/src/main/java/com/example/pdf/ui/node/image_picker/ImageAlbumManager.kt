package com.example.pdf.ui.node.image_picker

import android.content.Context
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Single
import com.example.pdf.lyricist.globalStrings

@Single
class ImageAlbumManager(private val context: Context) {

  // 使用StateFlow缓存策略，参考DocumentRepository
  private val _imagesByFolderFlow: MutableStateFlow<Map<String, List<ImageItem>>> = MutableStateFlow(emptyMap())
  val imagesByFolderFlow: StateFlow<Map<String, List<ImageItem>>> get() = _imagesByFolderFlow

  private val _albumsFlow: MutableStateFlow<List<ImageAlbum>> = MutableStateFlow(emptyList())
  val albumsFlow: StateFlow<List<ImageAlbum>> get() = _albumsFlow

  companion object {
    private const val ALL_IMAGES_FOLDER_KEY = "ALL_IMAGES" // 内部键，不直接显示给用户
    const val ALL_IMAGES_FOLDER = ALL_IMAGES_FOLDER_KEY // 为了向后兼容，保持公共引用
  }
  
  private val allImagesFolderName: String
    get() = globalStrings.allImages

  /**
   * 获取所有相册，如果缓存为空则触发加载
   */
  suspend fun getAllAlbums(): List<ImageAlbum> {
    val currentAlbums = _albumsFlow.value
    if (currentAlbums.isEmpty()) {
      onFetchImages()
    }
    return currentAlbums
  }

  /**
   * 获取指定相册的图片，如果缓存为空则触发加载
   */
  suspend fun getImagesForAlbum(album: ImageAlbum): List<ImageItem> {
    val currentImages = _imagesByFolderFlow.value
    if (currentImages.isEmpty()) {
      onFetchImages()
    }

    return when (album.id) {
      "all_images" -> currentImages[ALL_IMAGES_FOLDER_KEY] ?: emptyList()
      else -> {
        // 查找匹配的 bucketId
        val bucketId = album.id.removePrefix("folder_")
        val folderKey = currentImages.keys.find { it.startsWith("$bucketId:") }
        folderKey?.let { currentImages[it] } ?: emptyList()
      }
    }
  }

  /**
   * 主要的数据获取方法，参考DocumentRepository.onFetchDocuments()
   */
  fun onFetchImages() {
    GlobalScope.launch {
      try {
        val imagesByFolder = loadAllImagesGroupedByFolder()
        val albums = createAlbumsFromImageMap(imagesByFolder)

        _imagesByFolderFlow.emit(imagesByFolder)
        _albumsFlow.emit(albums)
      } catch (e: Exception) {
        e.printStackTrace()
      }
    }
  }

  /**
   * 加载所有图片并按文件夹分组，返回Map<String, List<ImageItem>>
   */
  private suspend fun loadAllImagesGroupedByFolder(): Map<String, List<ImageItem>> = withContext(Dispatchers.IO) {
    val allImages = loadAllImagesFromStorage()
    val imagesByFolder = mutableMapOf<String, MutableList<ImageItem>>()

    // 始终添加"All Images"文件夹，即使没有图片也保留空列表
    imagesByFolder[ALL_IMAGES_FOLDER_KEY] = allImages.toMutableList()

    // 按文件夹分组，使用bucketId和bucketName的组合来避免同名文件夹冲突
    allImages.forEach { image ->
      val bucketName = image.bucketName
      val bucketId = image.bucketId
      
      if (bucketName != null && bucketId != null) {
        // 使用 bucketId + bucketName 作为唯一键
        val folderKey = "$bucketId:$bucketName"
        imagesByFolder.getOrPut(folderKey) { mutableListOf() }.add(image)
      }
    }

    // 转换为不可变Map
    imagesByFolder.mapValues { it.value.toList() }
  }

  /**
   * 从图片Map创建相册列表
   */
  private fun createAlbumsFromImageMap(imagesByFolder: Map<String, List<ImageItem>>): List<ImageAlbum> {
    val albums = mutableListOf<ImageAlbum>()

    imagesByFolder.forEach { (folderKey, images) ->
      // "All Images"文件夹始终创建相册，即使为空；其他文件夹只有非空时才创建
      if (folderKey == ALL_IMAGES_FOLDER_KEY || images.isNotEmpty()) {
        val (albumId, displayName) = if (folderKey == ALL_IMAGES_FOLDER_KEY) {
          "all_images" to allImagesFolderName
        } else {
          // folderKey 格式: "bucketId:bucketName"
          val parts = folderKey.split(":", limit = 2)
          val bucketId = parts.getOrNull(0) ?: ""
          val bucketName = parts.getOrNull(1) ?: folderKey
          
          // 使用 bucketId 作为唯一标识符，bucketName 作为显示名称
          val uniqueId = "folder_$bucketId"
          uniqueId to bucketName
        }

        albums.add(
          ImageAlbum(
            id = albumId,
            name = displayName,
            coverUri = images.firstOrNull()?.uri,
            imageCount = images.size
          )
        )
      }
    }

    // 确保"All Images"在第一位
    return albums.sortedBy { if (it.id == "all_images") 0 else 1 }
  }

  /**
   * 预加载缓存，可以在应用启动时调用
   */
  fun preloadCache() {
    onFetchImages()
  }

  /**
   * 清除缓存
   */
  fun clearCache() {
    GlobalScope.launch {
      _imagesByFolderFlow.emit(emptyMap())
      _albumsFlow.emit(emptyList())
    }
  }

  /**
   * 获取缓存状态信息（用于调试）
   */
  fun getCacheInfo(): String {
    val albumsCount = _albumsFlow.value.size
    val foldersCount = _imagesByFolderFlow.value.size
    val totalImages = _imagesByFolderFlow.value.values.sumOf { it.size }
    return "Albums: $albumsCount, Folders: $foldersCount, Total images: $totalImages"
  }

  private suspend fun loadAllImagesFromStorage(): List<ImageItem> = withContext(Dispatchers.IO) {
    val images = mutableListOf<ImageItem>()
    val contentResolver = context.contentResolver

    val collection = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
      MediaStore.Images.Media.getContentUri(MediaStore.VOLUME_EXTERNAL)
    } else {
      MediaStore.Images.Media.EXTERNAL_CONTENT_URI
    }

    val projection = arrayOf(
      MediaStore.Images.Media._ID,
      MediaStore.Images.Media.DISPLAY_NAME,
      MediaStore.Images.Media.DATE_ADDED,
      MediaStore.Images.Media.SIZE,
      MediaStore.Images.Media.BUCKET_DISPLAY_NAME,
      MediaStore.Images.Media.BUCKET_ID
    )

    val sortOrder = "${MediaStore.Images.Media.DATE_ADDED} DESC"

    try {
      contentResolver.query(
        collection,
        projection,
        null,
        null,
        sortOrder
      )?.use { cursor ->
        val idColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID)
        val nameColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DISPLAY_NAME)
        val dateColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATE_ADDED)
        val sizeColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.SIZE)
        val bucketNameColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.BUCKET_DISPLAY_NAME)
        val bucketIdColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.BUCKET_ID)

        while (cursor.moveToNext()) {
          val id = cursor.getLong(idColumn)
          val name = cursor.getString(nameColumn) ?: ""
          val dateAdded = cursor.getLong(dateColumn)
          val size = cursor.getLong(sizeColumn)
          val bucketName = cursor.getString(bucketNameColumn)
          val bucketId = cursor.getString(bucketIdColumn)

          val uri = Uri.withAppendedPath(collection, id.toString())

          images.add(
            ImageItem(
              id = id.toString(),
              uri = uri,
              displayName = name,
              dateAdded = dateAdded,
              size = size,
              bucketId = bucketId,
              bucketName = bucketName
            )
          )
        }
      }
    } catch (e: Exception) {
      e.printStackTrace()
    }

    images
  }



}
