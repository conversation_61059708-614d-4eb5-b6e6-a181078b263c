package com.example.pdf.ui.node.selection

import com.example.pdf.android.file.documentsDelete
import com.example.pdf.android.share.shareFiles
import com.example.pdf.android.toast.showToast
import com.example.pdf.appActivity
import com.example.pdf.guia.GlobalNavigator
import com.example.pdf.mmkv.UserSettingsKvStore
import com.example.pdf.mvi_ui_model.UiModel
import com.roudikk.guia.extensions.pop
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.withContext
import org.koin.android.annotation.KoinViewModel
import java.io.File

@KoinViewModel
class SelectionUiModel(
  private val args: SelectionNodeArgs,
  private val userSettingsKvStore: UserSettingsKvStore,
) : UiModel<SelectionUiState, Nothing>(SelectionUiState()) {

  init {
    registerBookmarkedDocumentPathsFlow()
    args.selectedFiles.firstOrNull()?.let { file ->
      onSelectDocument(file, true)
    }
  }

  private fun registerBookmarkedDocumentPathsFlow() = intent {
    userSettingsKvStore.bookmarkedDocumentPathsFlow.onEach { bookmarkedPaths ->
      reduce { state.copy(bookmarkedFilePaths = bookmarkedPaths) }
    }.launchIn(uiModelScope)
  }

  fun onSelectAllSwitch(selectAll: Boolean) = intent {
    val updatedSelection = if (selectAll) {
      args.displayFiles
    } else {
      emptyList()
    }

    reduce { state.copy(selectedFiles = updatedSelection, isSelectAll = selectAll) }
  }

  fun onSelectDocument(file: File, selected: Boolean) = intent {
    val selection = state.selectedFiles

    val updatedSelection = if (selected) {
      selection + file
    } else {
      selection - file
    }

    reduce {
      state.copy(
        selectedFiles = updatedSelection,
        isSelectAll = updatedSelection.size == args.displayFiles.size
      )
    }
  }

  fun onShowDeleteConfirmationDialog() = intent {
    reduce { state.copy(showDeleteConfirmationDialog = true) }
  }

  fun onDismissDeleteConfirmationDialog() = intent {
    reduce { state.copy(showDeleteConfirmationDialog = false) }
  }

  fun onDelete() = intent {
    documentsDelete(
      documents = state.selectedFiles,
      onSuccess = {
        intent {
          reduce {
            state.copy(
              selectedFiles = emptyList(),
              isSelectAll = false,
              showDeleteConfirmationDialog = false
            )
          }
          GlobalNavigator.transaction { pop() }
        }
      }
    )
  }

  fun onShare() = intent {
    val needShareFiles = state.selectedFiles

    withContext(Dispatchers.Main.immediate) {
      appActivity?.shareFiles(needShareFiles)
    }
  }
}