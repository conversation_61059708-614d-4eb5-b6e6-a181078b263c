package com.example.pdf.ui.feature.image_picker

/**
 * Migration Guide: From System ImageUriPickerContainer to Custom ImageUriPickerContainer
 * 
 * This guide shows how to migrate from the system image picker to our custom implementation
 * that provides album browsing, better UI, and more control over the selection process.
 */

/*
=== MIGRATION EXAMPLES ===

1. SIMPLE REPLACEMENT (Recommended)
   Replace ImageUriPickerContainer with EnhancedImageUriPickerContainer

   BEFORE:
   ```kotlin
   ImageUriPickerContainer(
     modifier = Modifier.weight(1f),
     onPickComplete = onImagesPickComplete
   ) { startPicker ->
     ToolItem(
       onClick = startPicker,
       painter = painterResource(R.drawable.img_tool_img_to_pdf),
       title = "Image to PDF",
       modifier = Modifier
         .weight(1f)
         .fillMaxWidth()
     )
   }
   ```

   AFTER:
   ```kotlin
   EnhancedImageUriPickerContainer(
     modifier = Modifier.weight(1f),
     onPickComplete = onImagesPickComplete
   ) { startPicker ->
     ToolItem(
       onClick = startPicker,
       painter = painterResource(R.drawable.img_tool_img_to_pdf),
       title = "Image to PDF",
       modifier = Modifier
         .weight(1f)
         .fillMaxWidth()
     )
   }
   ```

2. GRADUAL MIGRATION (For Testing)
   Use AdaptiveImageUriPickerContainer to switch between implementations

   ```kotlin
   AdaptiveImageUriPickerContainer(
     useCustomPicker = true, // Set to false to use system picker
     modifier = Modifier.weight(1f),
     onPickComplete = onImagesPickComplete
   ) { startPicker ->
     ToolItem(
       onClick = startPicker,
       painter = painterResource(R.drawable.img_tool_img_to_pdf),
       title = "Image to PDF",
       modifier = Modifier
         .weight(1f)
         .fillMaxWidth()
     )
   }
   ```

3. DIRECT CUSTOM IMPLEMENTATION
   Use CustomImageUriPickerContainer directly

   ```kotlin
   CustomImageUriPickerContainer(
     limit = 20, // Optional: set maximum selection limit
     onPickStarted = { 
       // Optional: show loading or analytics
     },
     onPickComplete = onImagesPickComplete,
     modifier = Modifier.weight(1f)
   ) { startPicker ->
     ToolItem(
       onClick = startPicker,
       painter = painterResource(R.drawable.img_tool_img_to_pdf),
       title = "Image to PDF",
       modifier = Modifier
         .weight(1f)
         .fillMaxWidth()
     )
   }
   ```

=== SPECIFIC FILE CHANGES ===

1. ToolsScreen.kt (Line ~139-151)
   
   REPLACE:
   ```kotlin
   ImageUriPickerContainer(
     modifier = Modifier.weight(1f),
     onPickComplete = onImagesPickComplete
   ) { startPick ->
     ToolItem(
       onClick = startPick,
       painter = painterResource(R.drawable.img_tool_img_to_pdf),
       title = "Image to PDF",
       modifier = Modifier
         .weight(1f)
         .fillMaxWidth()
     )
   }
   ```

   WITH:
   ```kotlin
   EnhancedImageUriPickerContainer(
     modifier = Modifier.weight(1f),
     onPickComplete = onImagesPickComplete
   ) { startPick ->
     ToolItem(
       onClick = startPick,
       painter = painterResource(R.drawable.img_tool_img_to_pdf),
       title = "Image to PDF",
       modifier = Modifier
         .weight(1f)
         .fillMaxWidth()
     )
   }
   ```

   ADD IMPORT:
   ```kotlin
   import com.example.pdf.ui.feature.image_picker.EnhancedImageUriPickerContainer
   ```

=== BENEFITS OF MIGRATION ===

✅ Album/folder browsing with dropdown selection
✅ Better UI with 3-column grid layout
✅ Multiple image selection with visual indicators
✅ Selected images preview in bottom bar
✅ Individual image removal from selection
✅ Material Design 3 styling consistent with app
✅ Better performance with CoilImage loading
✅ Same API - drop-in replacement

=== TESTING CHECKLIST ===

□ Image selection works correctly
□ Multiple images can be selected
□ Album switching works
□ Selected images appear in bottom bar
□ Images can be removed from selection
□ OK button confirms selection
□ Back button cancels selection
□ Navigation works properly
□ No memory leaks during image loading
□ Permissions are handled correctly

=== ROLLBACK PLAN ===

If issues are found, you can easily rollback by:
1. Changing EnhancedImageUriPickerContainer back to ImageUriPickerContainer
2. Or setting useCustomPicker = false in AdaptiveImageUriPickerContainer
3. The API is identical, so no other code changes needed

=== PERFORMANCE CONSIDERATIONS ===

- Custom picker loads images on-demand using CoilImage
- MediaStore queries are performed on background threads
- Album list is cached after first load
- Consider adding image preloading for better UX if needed

*/
