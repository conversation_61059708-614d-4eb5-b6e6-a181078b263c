package com.example.pdf.ui.node.settings

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.rounded.ArrowBackIos
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.pdf.BuildConfig
import com.example.pdf.R
import com.example.pdf.android.context.openBrowser
import com.example.pdf.android.context.sendEmail
import com.example.pdf.appContext
import com.example.pdf.guia.ScreenNode
import com.example.pdf.guia.UseStatusBarDarkIcons
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.IconButton
import com.example.pdf.lumo.components.IconButtonVariant
import com.example.pdf.lumo.components.Scaffold
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.topbar.TopBar
import com.example.pdf.lumo.components.topbar.TopBarDefaults
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.lyricist.globalStrings
import com.example.pdf.ui.composable.BlankSpacer
import com.example.pdf.ui.node.language.LanguageNode
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.push
import kotlinx.parcelize.Parcelize

@Parcelize
class SettingsNode : ScreenNode("settings") {

  @Composable
  override fun Content(navigator: Navigator) {
    UseStatusBarDarkIcons()

    val context = LocalContext.current

    SettingsContent(
      onBack = navigator::pop,
      onLanguageClick = {
        navigator.push(LanguageNode(showBackIcon = true))
      },
      onFeedbackClick = {
        context.sendEmail("<EMAIL>", subject = globalStrings.feedback)
      },
      onPrivacyPolicyClick = {
        context.openBrowser("https://sites.google.com/view/privacy-mypdf/")
      }
    )
  }
}

@Composable
private fun SettingsContent(
  onBack: () -> Unit,
  onLanguageClick: () -> Unit,
  onFeedbackClick: () -> Unit,
  onPrivacyPolicyClick: () -> Unit,
) {
  Scaffold(
    topBar = {
      SettingsTopBar(onBack = onBack)
    },
    containerColor = AppTheme.colors.secondary.copy(.15f)
  ) {
    Column(
      modifier = Modifier
        .fillMaxSize()
        .padding(it)
        .padding(horizontal = 16.dp)
    ) {
      BlankSpacer(16.dp)

      SettingsItem(
        icon = painterResource(R.drawable.ic_settings_lang),
        title = LocalStrings.current.language,
        onClick = onLanguageClick
      )

      BlankSpacer(16.dp)

      SettingsItem(
        icon = painterResource(R.drawable.ic_settings_feedback),
        title = LocalStrings.current.feedback,
        onClick = onFeedbackClick
      )

      BlankSpacer(16.dp)

      SettingsItem(
        icon = painterResource(R.drawable.ic_settings_privacy_policy),
        title = LocalStrings.current.privacyPolicy,
        onClick = onPrivacyPolicyClick
      )

      BlankSpacer(16.dp)

      Spacer(Modifier.weight(1f))

      Text(
        LocalStrings.current.version.format(BuildConfig.VERSION_NAME),
        style = AppTheme.typography.body1,
        fontWeight = FontWeight.Medium,
        color = AppTheme.colors.onDisabled,
        modifier = Modifier
          .padding(bottom = 24.dp)
          .align(Alignment.CenterHorizontally)
      )
    }
  }
}

@Composable
private fun SettingsTopBar(
  onBack: () -> Unit
) {
  TopBar(
    colors = TopBarDefaults.topBarColors(containerColor = AppTheme.colors.surface)
  ) {
    Row(
      modifier = Modifier
        .fillMaxWidth()
        .padding(horizontal = 8.dp),
      verticalAlignment = Alignment.CenterVertically
    ) {
      IconButton(onClick = onBack, shape = CircleShape, variant = IconButtonVariant.Ghost) {
        Icon(imageVector = Icons.AutoMirrored.Rounded.ArrowBackIos)
      }

      Text(text = LocalStrings.current.settings, modifier = Modifier.weight(1f), style = AppTheme.typography.h2)
    }
  }
}
