package com.example.pdf.ui.node.document.ms_office.powerpoint

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import com.example.pdf.android.share.shareFile
import com.example.pdf.appActivity
import com.example.pdf.biz.ad.banner.BannerAd
import com.example.pdf.biz.ad.banner.BannerAdPlace
import com.example.pdf.biz.ad.interstitial.interstitialAdRegister
import com.example.pdf.lumo.components.Scaffold
import com.example.pdf.mvi_ui_model.koinUiModel
import com.example.pdf.ui.feature.document_node.DocumentNode
import com.example.pdf.ui.feature.ms_office.MSOfficeViewer
import com.example.pdf.ui.node.document_details.DocumentDetailsNode
import com.example.pdf.ui.node.document.CommonDocumentUiModel
import com.example.pdf.ui.node.document.CommonDocumentUiState
import com.example.pdf.ui.node.document.CommonDocumentTopBar
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.push
import kotlinx.parcelize.Parcelize
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState
import java.io.File

@Parcelize
class PowerPointNode(override val document: File) : DocumentNode("ppt") {
  @Composable
  override fun Content(navigator: Navigator) {
    super.Content(navigator)

    val cdUiModel: CommonDocumentUiModel = koinUiModel { parametersOf(document) }
    val cdUiState by cdUiModel.collectAsState()

    val (_, onBackAction)
      = interstitialAdRegister(navigator)

    PowerPointContent(
      document = document,
      commonDocumentUiState = cdUiState,
      onBack = onBackAction,
      onBookmarkClick = { cdUiModel.onBookmarkClick(document, it) },
      onShareClick = { appActivity?.shareFile(File(document.absolutePath)) },
      onMoreClick = { navigator.push(DocumentDetailsNode(document)) }
    )
  }
}

@Composable
private fun PowerPointContent(
  document: File,
  commonDocumentUiState: CommonDocumentUiState,
  onBack: () -> Unit,
  onBookmarkClick: (Boolean) -> Unit,
  onShareClick: () -> Unit,
  onMoreClick: () -> Unit
) {
  Scaffold(
    topBar = {
      CommonDocumentTopBar(
        title = "",
        isBookmarked = commonDocumentUiState.isBookmarked,
        onBackClick = onBack,
        onBookmarkClick = onBookmarkClick,
        onShareClick = onShareClick,
        onMoreClick = onMoreClick
      )
    },
    bottomBar = {
      BannerAd(BannerAdPlace.Document, modifier = Modifier.navigationBarsPadding())
    }
  ) {
    MSOfficeViewer(
      document = document,
      modifier = Modifier
        .fillMaxSize()
        .padding(it)
    )
  }
}