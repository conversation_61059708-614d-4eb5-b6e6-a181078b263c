package com.example.pdf.ui.node.welcome

import androidx.annotation.DrawableRes
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerDefaults
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.intl.Locale
import androidx.compose.ui.text.style.LineBreak
import androidx.compose.ui.tooling.preview.Devices
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.LocaleSupport.latinsExcludeEn
import com.example.pdf.R
import com.example.pdf.biz.PrefStore
import com.example.pdf.biz.ad.interstitial.interstitialAdRegister
import com.example.pdf.biz.ad.nat1ve.NativeAd
import com.example.pdf.biz.ad.nat1ve.NativeAdPlace
import com.example.pdf.coroutine.AppCoroutineScope
import com.example.pdf.guia.ScreenNode
import com.example.pdf.guia.UseStatusBarDarkIcons
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Button
import com.example.pdf.lumo.components.Scaffold
import com.example.pdf.lumo.components.Text
import com.example.pdf.ui.composable.BlankSpacer
import com.example.pdf.ui.node.home.HomeNode
import com.roudikk.guia.backstack.NavBackHandler
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.popToRoot
import com.roudikk.guia.extensions.replaceLast
import com.roudikk.guia.extensions.setRoot
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import org.koin.compose.koinInject


data class WelcomePage(
  @param:DrawableRes val bgImgRes: Int,
  val titleKey: String,
)

private val welcomePages =
  listOf(
    WelcomePage(R.drawable.img_welcome_0, "welcomeTitle1"),
    WelcomePage(R.drawable.img_welcome_1, "welcomeTitle2"),
    WelcomePage(R.drawable.img_welcome_2, "welcomeTitle3"),
  )

@Parcelize
class WelcomeNode : ScreenNode("welcome") {
  @Composable
  override fun Content(navigator: Navigator) {
    UseStatusBarDarkIcons()
    val appScope: AppCoroutineScope = koinInject()
    val prefStore: PrefStore = koinInject()

    val (onTryToShowInterAdAndNavAction, _) = interstitialAdRegister(navigator)

    val onFinish = remember {
      {
        appScope.launch(Dispatchers.Default) {
          prefStore.saveWelcomeFinish()
        }
        onTryToShowInterAdAndNavAction("welcome_finish") {
          setRoot(HomeNode())
          popToRoot()
        }
      }
    }

    WelcomeContent(
      onFinish = onFinish,
      onBack = navigator::pop
    )
  }
}

@Composable
private fun WelcomeContent(
  onFinish: () -> Unit,
  onBack: () -> Unit,
) {
  val locale = Locale.current
  val strings = LocalStrings.current

  val coroutineScope = rememberCoroutineScope()
  val pagerState = rememberPagerState(pageCount = { welcomePages.size })

  NavBackHandler {
    if (pagerState.currentPage == 0) {
      onBack()
    } else {
      coroutineScope.launch {
        pagerState.animateScrollToPage(
          page = pagerState.currentPage - 1,
          animationSpec = tween(200)
        )
      }
    }
  }

  Scaffold(
    bottomBar = {
      NativeAd(adPlace = NativeAdPlace.Welcome, modifier = Modifier.navigationBarsPadding())
    }
  ) {
    HorizontalPager(
      state = pagerState,
      modifier = Modifier.padding(bottom = it.calculateBottomPadding()),
      flingBehavior = PagerDefaults.flingBehavior(
        state = pagerState,
        snapAnimationSpec = tween(200)
      )
    ) { index ->

      val currentWelcomePage = welcomePages[index]

      Box(
        modifier = Modifier.fillMaxSize()
      ) {
        Image(
          painter = painterResource(id = currentWelcomePage.bgImgRes),
          contentDescription = null,
          alignment = Alignment.BottomCenter,
          contentScale = ContentScale.FillWidth,
          modifier = Modifier
            .fillMaxWidth()
            .offset(y = (-16).dp)
        )

        Column(
          modifier = Modifier.align(Alignment.BottomCenter)
        ) {
          val title = when (currentWelcomePage.titleKey) {
            "welcomeTitle1" -> strings.welcomeTitle1
            "welcomeTitle2" -> strings.welcomeTitle2
            "welcomeTitle3" -> strings.welcomeTitle3
            else -> currentWelcomePage.titleKey
          }

          Text(
            text = title,
            style = AppTheme.typography.h1.copy(
              fontSize = 30.sp,
              lineHeight = 34.sp,
              lineBreak = LineBreak.Paragraph.copy(strategy = LineBreak.Strategy.Balanced)
            ),
            modifier = Modifier.padding(horizontal = 28.dp)
          )

          BlankSpacer(36.dp)

          Row(
            modifier = Modifier
              .fillMaxWidth()
              .padding(horizontal = 28.dp),
            verticalAlignment = Alignment.CenterVertically,
          ) {
            PagerIndicator(
              index = index,
              count = welcomePages.size
            )

            Spacer(Modifier.weight(1f))

            if (index == welcomePages.lastIndex) {
              Button(
                onClick = { onFinish() },
                contentPadding = PaddingValues(vertical = 12.dp, horizontal = 48.dp)
              ) {
                Text(
                  text = strings.next,
                  style = AppTheme.typography.buttonLarge.copy(fontWeight = FontWeight.SemiBold)
                )
              }
            } else {
              Button(
                onClick = {
                  coroutineScope.launch {
                    pagerState.animateScrollToPage(
                      page = pagerState.currentPage + 1,
                      animationSpec = tween(200)
                    )
                  }
                },
                contentPadding = PaddingValues(vertical = 10.dp, horizontal = 42.dp)
              ) {
                Text(
                  text = strings.next,
                  style = AppTheme.typography.buttonLarge.copy(fontWeight = FontWeight.SemiBold)
                )
              }
            }
          }

          BlankSpacer(36.dp)
        }
      }
    }
  }
}

@Composable
private fun PagerIndicator(
  index: Int,
  count: Int,
  modifier: Modifier = Modifier,
) {
  Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
    for (i in 0 until count) {
      val color = if (index == i) {
        AppTheme.colors.primary
      } else {
        AppTheme.colors.primary.copy(.1f)
      }

      Box(
        modifier = Modifier
          .size(8.dp)
          .background(color, CircleShape)
      )
      if (i != count - 1) {
        BlankSpacer(width = 12.dp)
      }
    }
  }
}


@Preview(device = Devices.PIXEL_7)
@Composable
private fun WelcomeContentPreview() {
  AppTheme {
    WelcomeContent(
      onFinish = {},
      onBack = {}
    )
  }
}