package com.example.pdf.ui.feature.pdf

import android.graphics.pdf.PdfRenderer
import android.os.ParcelFileDescriptor
import com.example.pdf.kermit.debugLog
import java.io.File
import java.io.IOException

/**
 * Utility class to detect if a PDF file is encrypted/password-protected
 */
object PdfEncryptionDetector {
  private const val TAG = "PdfEncryptionDetector"

  /**
   * Check if a PDF file is encrypted/password-protected
   * 
   * @param pdfFile The PDF file to check
   * @return true if the PDF is encrypted, false otherwise
   */
  fun isEncrypted(pdfFile: File): Boolean {
    if (!pdfFile.exists() || !pdfFile.canRead()) {
      debugLog(tag = TAG) { "File doesn't exist or can't be read: ${pdfFile.absolutePath}" }
      return false
    }

    var fileDescriptor: ParcelFileDescriptor? = null
    var pdfRenderer: PdfRenderer? = null

    return try {
      // Try to open the PDF file with PdfRenderer
      fileDescriptor = ParcelFileDescriptor.open(
        pdfFile,
        ParcelFileDescriptor.MODE_READ_ONLY
      )
      
      // If the PDF is encrypted, PdfRenderer constructor will throw a SecurityException
      pdfRenderer = PdfRenderer(fileDescriptor)
      
      // If we get here, the PDF is not encrypted
      false
    } catch (e: SecurityException) {
      // SecurityException indicates the PDF is encrypted
      debugLog(tag = TAG) { "PDF is encrypted: ${pdfFile.absolutePath}" }
      true
    } catch (e: IOException) {
      // IOException could be due to various reasons, including encryption
      debugLog(tag = TAG, throwable = e) { "Error checking PDF encryption: ${pdfFile.absolutePath}" }
      // We can't be sure if it's encrypted, but we'll assume it might be
      true
    } finally {
      // Close resources
      try {
        pdfRenderer?.close()
        fileDescriptor?.close()
      } catch (e: IOException) {
        debugLog(tag = TAG, throwable = e) { "Error closing resources" }
      }
    }
  }
}
