package com.example.pdf.ui.feature.image_picker

import android.annotation.SuppressLint
import android.net.Uri
import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import com.example.pdf.PreviewComposable
import com.example.pdf.android.permission.FileAccessManager
import com.example.pdf.android.toast.showToast
import com.example.pdf.lyricist.globalStrings
import com.example.pdf.kermit.debugLog
import com.example.pdf.lumo.components.Button
import com.example.pdf.lumo.components.Text
import com.example.pdf.ui.node.image_picker.ImagePickerNode
import com.roudikk.guia.extensions.push
import com.roudikk.guia.extensions.requireLocalNavigator
import org.koin.compose.koinInject

/**
 * A container composable for the Custom Image Picker functionality that allows selecting multiple images
 * using our custom ImagePickerNode with album browsing and returns their Uris.
 *
 * This is a drop-in replacement for the system ImageUriPickerContainer that provides:
 * - Album/folder selection dropdown
 * - 3-column grid layout
 * - Multiple image selection with preview
 * - Selected images bottom bar with remove functionality
 * - Material Design 3 styling
 *
 * @param limit Maximum number of images that can be selected (not enforced in current implementation)
 * @param onPickStarted Called when image picking starts.
 * @param onPickComplete Called when image selection completes successfully with the list of selected image Uris. If the user cancels, the list will be empty.
 * @param modifier Modifier to be applied to the container.
 * @param content Composable content to be displayed as the trigger for image picking.
 */
@Composable
fun CustomImageUriPickerContainer(
  limit: Int = 99,
  onPickStarted: () -> Unit = {},
  onPickComplete: (List<Uri>) -> Unit,
  @SuppressLint("ModifierParameter") modifier: Modifier = Modifier,
  content: @Composable (startPicker: () -> Unit) -> Unit
) {
  val navigator = requireLocalNavigator()

  // Function to start the custom image picker that will be passed to the content
  val startPicker = remember {
    {
      onPickStarted()
      navigator.push(
        ImagePickerNode(
          onImagesSelected = { selectedUris ->
            onPickComplete(selectedUris)
          }
        )
      )
    }
  }

  Box(
    modifier = modifier,
    contentAlignment = Alignment.Center
  ) {
    // Pass the startPicker function to the content
    content(startPicker)
  }
}

/**
 * Enhanced version of ImageUriPickerContainer that uses the custom ImagePickerNode.
 * This is a direct replacement for the existing ImageUriPickerContainer with the same API.
 *
 * Usage example:
 * ```
 * // Replace this:
 * ImageUriPickerContainer(
 *   onPickComplete = { uris -> ... }
 * ) { startPicker ->
 *   Button(onClick = startPicker) { Text("Select Images") }
 * }
 *
 * // With this:
 * EnhancedImageUriPickerContainer(
 *   onPickComplete = { uris -> ... }
 * ) { startPicker ->
 *   Button(onClick = startPicker) { Text("Select Images") }
 * }
 * ```
 */
@Composable
fun EnhancedImageUriPickerContainer(
  limit: Int = 99,
  onPickStarted: () -> Unit = {},
  onPickComplete: (List<Uri>) -> Unit,
  @SuppressLint("ModifierParameter") modifier: Modifier = Modifier,
  content: @Composable (startPicker: () -> Unit) -> Unit
) {
  CustomImageUriPickerContainer(
    limit = limit,
    onPickStarted = onPickStarted,
    onPickComplete = onPickComplete,
    modifier = modifier,
    content = content
  )
}

/**
 * Fallback version that can switch between system picker and custom picker based on a flag.
 * Useful for gradual migration or A/B testing.
 */
@Composable
fun AdaptiveImageUriPickerContainer(
  useCustomPicker: Boolean,
  limit: Int = 99,
  onPickStarted: () -> Unit = {},
  onPickComplete: (List<Uri>) -> Unit,
  @SuppressLint("ModifierParameter") modifier: Modifier = Modifier,
  content: @Composable (startPicker: () -> Unit) -> Unit
) {
  if (useCustomPicker) {
    CustomImageUriPickerContainer(
      limit = limit,
      onPickStarted = onPickStarted,
      onPickComplete = onPickComplete,
      modifier = modifier,
      content = content
    )
  } else {
    ImageUriPickerContainer(
      limit = limit,
      onPickStarted = onPickStarted,
      onPickComplete = onPickComplete,
      modifier = modifier,
      content = content
    )
  }
}

@Composable
fun AdaptiveImageUriPickerContainer(
  limit: Int = 99,
  onPickStarted: () -> Unit = {},
  onPickComplete: (List<Uri>) -> Unit,
  @SuppressLint("ModifierParameter") modifier: Modifier = Modifier,
  content: @Composable (startPicker: () -> Unit) -> Unit
) {
  val fileAccessManager: FileAccessManager = koinInject()
  val useCustomPicker = fileAccessManager.hasFileAccessPermission()

  if (useCustomPicker) {
    CustomImageUriPickerContainer(
      limit = limit,
      onPickStarted = onPickStarted,
      onPickComplete = onPickComplete,
      modifier = modifier,
      content = content
    )
  } else {
    ImageUriPickerContainer(
      limit = limit,
      onPickStarted = onPickStarted,
      onPickComplete = onPickComplete,
      modifier = modifier,
      content = content
    )
  }
}

@Preview(showBackground = true)
@Composable
fun CustomImageUriPickerContainerPreview() {
  val context = LocalContext.current

  PreviewComposable {
    CustomImageUriPickerContainer(
      onPickStarted = { },
      onPickComplete = { uris ->
        if (uris.isNotEmpty()) {
          showToast(globalStrings.selectedImagesWithCustomPicker.format(uris.size))
          uris.forEach { uri ->
            debugLog(message = "Selected image URI: $uri")
          }
        } else {
          showToast(globalStrings.imageSelectionCancelled)
        }
      }
    ) { startPicker ->
      Button(onClick = startPicker) {
        Text("Select Images (Custom)")
      }
    }
  }
}

@Preview(showBackground = true)
@Composable
fun AdaptiveImageUriPickerContainerPreview() {
  val context = LocalContext.current

  PreviewComposable {
    AdaptiveImageUriPickerContainer(
      useCustomPicker = true,
      onPickStarted = { },
      onPickComplete = { uris ->
        if (uris.isNotEmpty()) {
          showToast(globalStrings.selectedImages.format(uris.size))
          uris.forEach { uri ->
            debugLog(message = "Selected image URI: $uri")
          }
        } else {
          showToast(globalStrings.imageSelectionCancelled)
        }
      }
    ) { startPicker ->
      Button(onClick = startPicker) {
        Text("Select Images (Adaptive)")
      }
    }
  }
}
