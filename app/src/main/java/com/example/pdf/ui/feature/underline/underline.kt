package com.example.pdf.ui.feature.underline

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.example.pdf.lumo.components.DividerDefaults


@Composable
fun Modifier.underline(
  color: Color = DividerDefaults.color,
  strokeWidth: Dp = DividerDefaults.Thickness,
  offsetY: Dp = 4.dp
): Modifier {
  return this.drawWithContent {
    drawContent()

    val strokeWidthPx = strokeWidth.toPx()
    val offsetYPx = offsetY.toPx()

    drawLine(
      color = color,
      start = Offset(0f, size.height + offsetYPx),
      end = Offset(size.width, size.height + offsetYPx),
      strokeWidth = strokeWidthPx
    )
  }
}