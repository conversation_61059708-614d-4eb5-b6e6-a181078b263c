package com.example.pdf.ui.node.document_rename

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.LifecycleStartEffect
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.PreviewComposable
import com.example.pdf.android.file.DocumentRepository
import com.example.pdf.android.file.documentRename
import com.example.pdf.android.toast.showToast
import com.example.pdf.guia.BottomSheetNode
import com.example.pdf.guia.previousKey
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Button
import com.example.pdf.lumo.components.ButtonVariant
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.IconButton
import com.example.pdf.lumo.components.IconButtonVariant
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.textfield.TextField
import com.example.pdf.lumo.components.textfield.TextFieldDefaults
import com.example.pdf.lyricist.globalStrings
import com.example.pdf.mmkv.UserSettingsKvStore
import com.example.pdf.mmkv.updateDocumentPathIfNeeded
import com.example.pdf.ui.feature.bottomsheet.BottomSheetSurface
import com.example.pdf.ui.node.document_more_action.DocumentMoreActionNode
import com.roudikk.guia.core.BottomSheet
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.parcelize.Parcelize
import org.koin.compose.koinInject
import java.io.File

@Parcelize
class DocumentRenameNode(
  private val document: File
) : BottomSheetNode("document_rename") {
  @Composable
  override fun Content(navigator: Navigator, bottomSheet: BottomSheet?) {
    val originalFilePath = remember { document.absolutePath }

    val scope = rememberCoroutineScope()
    val documentRepository: DocumentRepository = koinInject()
    val settingsKvStore: UserSettingsKvStore = koinInject()

    val (documentName, documentExtension) = remember(document) {
      document.nameWithoutExtension to document.extension
    }

    var newName by remember {
      mutableStateOf(
        TextFieldValue(
          text = documentName,
          selection = TextRange(0, documentName.length)
        )
      )
    }

    val onNameChange: (TextFieldValue) -> Unit = { input: TextFieldValue ->
      newName = input
    }

    val onConfirm = {
      scope.launch(Dispatchers.Default) {
        documentRename(
          document = document,
          newName = buildString {
            append(newName.text.trim())
            append(".")
            append(documentExtension)
          },
          onSuccess = { renamedDocument ->
            showToast(message = globalStrings.renamedSuccessfully)
            documentRepository.onFetchDocuments()
            scope.launch(Dispatchers.Default) {
              settingsKvStore.updateDocumentPathIfNeeded(
                oldPath = originalFilePath,
                newPath = renamedDocument.absolutePath
              )
              withContext(Dispatchers.Main.immediate) {
                if (navigator.previousKey is DocumentMoreActionNode) {
                  navigator.pop()
                }
                navigator.pop()
              }
            }
          },
        )
      }
    }

    DocumentRenameContent(
      newName = newName,
      isConfirmEnabled = newName.text.trim().isNotEmpty(),
      onNameChange = onNameChange,
      onNameClear = { onNameChange(TextFieldValue()) },
      onConfirm = { onConfirm() },
      onCancel = navigator::pop
    )
  }
}

@Composable
private fun DocumentRenameContent(
  newName: TextFieldValue,
  isConfirmEnabled: Boolean,
  onNameChange: (TextFieldValue) -> Unit,
  onNameClear: () -> Unit,
  onConfirm: () -> Unit,
  onCancel: () -> Unit
) {
  val focusRequester = remember { FocusRequester() }
  val keyboardController = LocalSoftwareKeyboardController.current
  val focusManager = LocalFocusManager.current

  LifecycleStartEffect(Unit) {
//    focusRequester.requestFocus()

    onStopOrDispose {
      focusManager.clearFocus()
      keyboardController?.hide()
    }
  }

  BottomSheetSurface(
    modifier = Modifier.imePadding(),
  ) {
    Text(text = LocalStrings.current.rename, style = AppTheme.typography.h2)

    TextField(
      value = newName,
      onValueChange = onNameChange,
      colors = TextFieldDefaults.colors().copy(
        unfocusedContainerColor = AppTheme.colors.secondary.copy(.25f),
        focusedContainerColor = AppTheme.colors.secondary.copy(.25f),
        cursorColor = AppTheme.colors.primary,
      ),
      modifier = Modifier.padding(24.dp).focusRequester(focusRequester),
      trailingIcon = {
        if (newName.text.isNotEmpty()) {
          IconButton(
            onClick = onNameClear,
            variant = IconButtonVariant.Secondary,
            shape = CircleShape,
            modifier = Modifier.size(22.dp)
          ) {
            Icon(imageVector = Icons.Filled.Clear)
          }
        }
      },
      singleLine = true,
      keyboardOptions = KeyboardOptions.Default.copy(imeAction = ImeAction.Done),
      keyboardActions = KeyboardActions(onDone = { onConfirm() })
    )

    Row(
      modifier = Modifier
        .fillMaxWidth()
        .padding(horizontal = 24.dp)
    ) {
      Button(
        onClick = onCancel,
        modifier = Modifier.weight(1f),
        variant = ButtonVariant.Secondary
      ) {
        Text(text = LocalStrings.current.cancel.uppercase(), style = AppTheme.typography.buttonLarge)
      }

      Spacer(Modifier.width(12.dp))

      Button(
        onClick = onConfirm,
        modifier = Modifier.weight(1f),
        enabled = isConfirmEnabled,
      ) {
        Text(text = LocalStrings.current.ok, style = AppTheme.typography.buttonLarge)
      }
    }

    Spacer(Modifier.height(24.dp))
  }
}

@Preview
@Composable
private fun DocumentRenameContentPreview() {
  PreviewComposable {
    DocumentRenameContent(
      newName = TextFieldValue("MyDocument"),
      isConfirmEnabled = false,
      onNameChange = {},
      onNameClear = {},
      onConfirm = {},
      onCancel = {}
    )
  }
}