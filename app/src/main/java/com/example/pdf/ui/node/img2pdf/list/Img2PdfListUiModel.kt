package com.example.pdf.ui.node.img2pdf.list

import android.content.Context
import android.graphics.pdf.PdfDocument
import android.net.Uri
import android.os.Environment
import androidx.core.graphics.withScale
import com.example.pdf.android.file.ImportDocumentFilesHelper
import com.example.pdf.android.permission.FileAccessManager
import com.example.pdf.android.toast.showToast
import com.example.pdf.lyricist.globalStrings
import com.example.pdf.biz.ad.interstitial.OnBackAction
import com.example.pdf.flow.MutableSingleEventFlow
import com.example.pdf.guia.GlobalNavigator
import com.example.pdf.mvi_ui_model.UiModel
import com.example.pdf.ui.node.convert_success.ConvertSuccessNode
import com.example.pdf.ui.node.img2pdf.img_cropper.clearImageCroppersCache
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.replaceLast
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.koin.android.annotation.KoinViewModel
import org.koin.core.annotation.InjectedParam
import org.orbitmvi.orbit.blockingIntent
import java.io.File
import java.io.FileOutputStream

val OnRemoveItemEventFlow = MutableSingleEventFlow<Img2PdfItemData>()

val OnCropItemEventFlow = MutableSingleEventFlow<Img2PdfItemData>()

typealias Uris = List<Uri>

@KoinViewModel
class Img2PdfListUiModel(
  @InjectedParam private val initImportUris: Uris,
  private val appContext: Context,
  private val importDocumentFilesHelper: ImportDocumentFilesHelper,
  private val fileAccessManager: FileAccessManager
) : UiModel<Img2PdfListUiState, Nothing>(Img2PdfListUiState()) {

  init {
    onAddUris(initImportUris)
    registerOnRemoveItemEventFlow()
    registerOnCropItemEventFlow()
  }

  private fun registerOnRemoveItemEventFlow() = intent {
    OnRemoveItemEventFlow.flow.onEach {
      delay(300)
      onRemove(it)
    }.launchIn(uiModelScope)
  }

  private fun registerOnCropItemEventFlow() = intent {
    OnCropItemEventFlow.flow.onEach {
      delay(300)
      onUpdate(it)
    }.launchIn(uiModelScope)
  }

  fun onTryToBack(
    onBackAction: OnBackAction = { GlobalNavigator.tryTransaction { pop() } }
  ) = intent {
    if (state.list.isEmpty()) {
      onBackAction()
    } else {
      onDisplayLeaveDialog()
    }
  }

  fun onDisplayLeaveDialog() = intent {
    reduce { state.copy(showLeaveDialog = true) }
  }

  fun onDismissLeaveDialog() = intent {
    reduce { state.copy(showLeaveDialog = false) }
  }

  fun onDisplayDeleteDialog(index: Int) = intent {
    reduce { state.copy(showDeleteDialogState = ShowDeleteDialogState(show = true, index = index)) }
  }

  fun onDismissDeleteDialog() = intent {
    reduce { state.copy(showDeleteDialogState = ShowDeleteDialogState.Empty) }
  }

  fun onAddUris(uris: List<Uri>) = intent {
    val pendingAdds = uris.map {
      Img2PdfItemData(
        uri = it,
        cropped = false,
        croppedBitmap = null
      )
    }
    reduce { state.copy(list = state.list + pendingAdds) }
  }

  fun onRemoveAt(index: Int) = intent {
    val deleteFinishList = state.list.toMutableList().apply {
      removeAt(index)
    }

    reduce { state.copy(list = deleteFinishList) }
  }

  fun onRemove(img2PdfItemData: Img2PdfItemData) = intent {
    val deleteFinishList = state.list.toMutableList()
    val index = deleteFinishList.indexOfFirst { it.id == img2PdfItemData.id }
    if (index != -1) {
      deleteFinishList.removeAt(index)
    }

    reduce { state.copy(list = deleteFinishList) }
  }

  fun onReorder(from: Int, to: Int) = blockingIntent {
    val originalList = state.list
    val reorderList = originalList.toMutableList().apply {
      add(to, removeAt(from))
    }

    reduce { state.copy(list = reorderList) }
  }

  fun onUpdate(newImg2PdfItemData: Img2PdfItemData) = intent {
    val updatedList = state.list.toMutableList().apply {
      val index = indexOfFirst { it.id == newImg2PdfItemData.id }
      if (index != -1) {
        this[index] = newImg2PdfItemData
      }
    }

    reduce { state.copy(list = updatedList) }
  }

  fun onConvertToPdf() = intent {
    reduce { state.copy(processing = true) }

    try {
      // 创建PDF文档
      val pdfDocument = PdfDocument()

      // 遍历所有图片
      state.list.forEachIndexed { index, imageData ->
        // 加载图片Bitmap
        val bitmap = if (imageData.cropped && imageData.croppedBitmap != null) {
          // 如果有裁剪的bitmap，直接使用
          imageData.croppedBitmap
        } else {
          // 否则从URI加载
          val inputStream = appContext.contentResolver.openInputStream(imageData.uri)
          val loadedBitmap = android.graphics.BitmapFactory.decodeStream(inputStream)
          inputStream?.close()
          loadedBitmap
        }

        if (bitmap != null) {
          // 创建页面信息
          val pageInfo = PdfDocument.PageInfo.Builder(
            bitmap.width,
            bitmap.height,
            index + 1
          ).create()

          // 开始页面
          val page = pdfDocument.startPage(pageInfo)

          // 将图片绘制到页面上
          val canvas = page.canvas
          canvas.drawBitmap(bitmap, 0f, 0f, null)

          // 结束页面
          pdfDocument.finishPage(page)

          // 如果不是裁剪后的bitmap，回收从URI加载的bitmap
          if (!imageData.cropped) {
            bitmap.recycle()
          }
        }
      }

      // 确定保存路径
      val internalDocumentDir = importDocumentFilesHelper.getInternalDocumentDirectory()
      val publicDocumentDir =
        Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)

      val hasFileAccessPermission = fileAccessManager.hasFileAccessPermission()

      val targetDir = if (hasFileAccessPermission) {
        publicDocumentDir
      } else {
        internalDocumentDir
      }

      // 确保目标目录存在
      if (!targetDir.exists()) {
        targetDir.mkdirs()
      }

      // 创建输出PDF文件
      val timestamp = System.currentTimeMillis()
      val newFileName = "PDF_Converted_$timestamp.pdf"
      val outputFile = File(targetDir, newFileName)

      // 保存PDF文件
      FileOutputStream(outputFile).use { outputStream ->
        pdfDocument.writeTo(outputStream)
      }

      // 关闭PDF文档
      pdfDocument.close()

      // 显示保存位置提示
      val savedLocationMessage = if (hasFileAccessPermission) {
        globalStrings.savedToDocuments.format(outputFile.absolutePath)
      } else {
        globalStrings.savedToInternal.format(outputFile.absolutePath)
      }
      showToast(savedLocationMessage)

      // 转换成功，导航到成功页面
      GlobalNavigator.transaction {
        replaceLast(ConvertSuccessNode(outputFile))
      }

    } catch (e: Exception) {
      e.printStackTrace()
      showToast(globalStrings.failedToSavePdf.format(e.localizedMessage))
    } finally {
      reduce { state.copy(processing = false) }
    }
  }

  fun onConvertToPdfWithConsistentWidth() = intent {
    reduce { state.copy(processing = true) }

    try {
      // 创建PDF文档
      val pdfDocument = PdfDocument()

      // 首先收集所有图片的bitmap并计算最大宽度
      val bitmapList = mutableListOf<android.graphics.Bitmap>()
      var maxWidth = 0

      // 加载所有图片并找出最大宽度
      state.list.forEach { imageData ->
        val bitmap = if (imageData.cropped && imageData.croppedBitmap != null) {
          imageData.croppedBitmap
        } else {
          val inputStream = appContext.contentResolver.openInputStream(imageData.uri)
          val loadedBitmap = android.graphics.BitmapFactory.decodeStream(inputStream)
          inputStream?.close()
          loadedBitmap
        }

        bitmap?.let {
          bitmapList.add(it)
          if (it.width > maxWidth) {
            maxWidth = it.width
          }
        }
      }

      // 使用一致的宽度创建PDF页面
      bitmapList.forEachIndexed { index, bitmap ->
        // 计算保持宽高比的新高度
        val scaleFactor = maxWidth.toFloat() / bitmap.width
        val newHeight = (bitmap.height * scaleFactor).toInt()

        // 创建页面信息，所有页面宽度一致
        val pageInfo = PdfDocument.PageInfo.Builder(
          maxWidth,
          newHeight,
          index + 1
        ).create()

        // 开始页面
        val page = pdfDocument.startPage(pageInfo)

        // 绘制图片到页面上，进行适当缩放
        val canvas = page.canvas
        canvas.withScale(scaleFactor, scaleFactor) {
          drawBitmap(bitmap, 0f, 0f, null)
        }

        // 结束页面
        pdfDocument.finishPage(page)

        // 如果不是裁剪后的bitmap，回收从URI加载的bitmap
        if (index < state.list.size && !state.list[index].cropped) {
          bitmap.recycle()
        }
      }

      // 确定保存路径
      val internalDocumentDir = importDocumentFilesHelper.getInternalDocumentDirectory()
      val publicDocumentDir =
        Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)

      val hasFileAccessPermission = fileAccessManager.hasFileAccessPermission()

      val targetDir = if (hasFileAccessPermission) {
        publicDocumentDir
      } else {
        internalDocumentDir
      }

      // 确保目标目录存在
      if (!targetDir.exists()) {
        targetDir.mkdirs()
      }

      // 创建输出PDF文件
      val timestamp = System.currentTimeMillis()
      val newFileName = "PDF_Converted_$timestamp.pdf"
      val outputFile = File(targetDir, newFileName)

      // 保存PDF文件
      FileOutputStream(outputFile).use { outputStream ->
        pdfDocument.writeTo(outputStream)
      }

      // 关闭PDF文档
      pdfDocument.close()

      // 显示保存位置提示
      val savedLocationMessage = if (hasFileAccessPermission) {
        globalStrings.savedToDocuments.format(outputFile.absolutePath)
      } else {
        globalStrings.savedToInternal.format(outputFile.absolutePath)
      }
      showToast(savedLocationMessage)

      // 转换成功，导航到成功页面
      GlobalNavigator.transaction {
        replaceLast(ConvertSuccessNode(outputFile))
      }

    } catch (e: Exception) {
      e.printStackTrace()
      showToast(globalStrings.failedToSavePdf.format(e.localizedMessage))
    } finally {
      reduce { state.copy(processing = false) }
    }
  }

  override fun onCleared() {
    super.onCleared()
    clearImageCroppersCache()
  }
}