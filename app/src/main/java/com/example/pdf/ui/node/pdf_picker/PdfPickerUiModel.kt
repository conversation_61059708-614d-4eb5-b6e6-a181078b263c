package com.example.pdf.ui.node.pdf_picker

import androidx.compose.ui.text.input.TextFieldValue
import com.example.pdf.android.file.DocumentRepository
import com.example.pdf.flow.MutableSingleEventFlow
import com.example.pdf.kermit.debugLog
import com.example.pdf.mmkv.UserSettingsKvStore
import com.example.pdf.mvi_ui_model.UiModel
import com.example.pdf.ui.feature.pdf.PdfEncryptionDetector
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.android.annotation.KoinViewModel
import java.io.File

private val pdfFilesStateFlow = MutableStateFlow(emptyList<File>())
private val encryptedPdfPathStateFlow = MutableStateFlow(emptySet<String>())

val PdfPasswordActionEventFlow = MutableSingleEventFlow<PdfPasswordAction>()

sealed class PdfPasswordAction(open val path: String) {
  data class SetPassword(override val path: String) : PdfPasswordAction(path)
  data class RemovePassword(override val path: String) : PdfPasswordAction(path)
}

@KoinViewModel
class PdfPickerUiModel(
  private val listType: PdfPickerListType,
  private val documentRepository: DocumentRepository,
  private val userSettingsKvStore: UserSettingsKvStore
) : UiModel<PdfPickerUiState, Nothing>(
  PdfPickerUiState(
    listType = listType,
    pdfFiles = pdfFilesStateFlow.value,
    encryptedPdfFiles = encryptedPdfPathStateFlow.value
  )
) {

  init {
    intent {
      val filteredFiles =
        filterPdfFiles(pdfFilesStateFlow.value, encryptedPdfPathStateFlow.value, "", listType)

      reduce { state.copy(filteredPdfFiles = filteredFiles) }
    }

    registerBookmarkedDocumentPathsFlow()
    loadPdfFiles()

    PdfPasswordActionEventFlow.flow.onEach { event ->
      intent {
        when (event) {
          is PdfPasswordAction.RemovePassword -> updatePdfFiles(event.path, isRemovePassword = true)
          is PdfPasswordAction.SetPassword -> updatePdfFiles(event.path, isRemovePassword = false)
        }
      }
    }.launchIn(uiModelScope)

  }

  private fun registerBookmarkedDocumentPathsFlow() {
    userSettingsKvStore.bookmarkedDocumentPathsFlow.onEach { bookmarkedPaths ->
      intent {
        reduce { state.copy(bookmarkedFilePaths = bookmarkedPaths) }
      }
    }.launchIn(uiModelScope)
  }

  private fun loadPdfFiles() = intent {
    debugLog(tag = "PdfPickerUiModel") { "loadPdfFiles start: ${System.currentTimeMillis()}" }

    if (state.pdfFiles.isEmpty()) {
      reduce { state.copy(isLoading = true) }
    }

    try {
      // Get all documents from repository
      val allFiles = documentRepository.documentsFlow.first()

      // Filter to only include PDF files
      val pdfFiles = allFiles.filter { it.extension.lowercase() == "pdf" }

      // Cache pdfFiles
      pdfFilesStateFlow.emit(pdfFiles)

      // Check which PDFs are encrypted
      val encryptedPdfFiles = withContext(Dispatchers.IO) {
        pdfFiles
          .filter { PdfEncryptionDetector.isEncrypted(it) }
          .map { it.absolutePath }
          .toSet()
      }
      debugLog(tag = "PdfPickerUiModel") { "encryptedPdfFiles finish: ${System.currentTimeMillis()}" }

      // Cache encryptedPdfFiles
      encryptedPdfPathStateFlow.emit(encryptedPdfFiles)

      // Apply initial filter based on listType
      val filteredFiles = filterPdfFiles(pdfFiles, encryptedPdfFiles, state.query.text, listType)
      debugLog(tag = "PdfPickerUiModel") { "filteredFiles: ${System.currentTimeMillis()}" }

      reduce {
        state.copy(
          pdfFiles = pdfFiles,
          filteredPdfFiles = filteredFiles,
          encryptedPdfFiles = encryptedPdfFiles,
          isLoading = false
        )
      }
    } catch (e: Exception) {
      debugLog(tag = "PdfPickerUiModel", throwable = e) { "Error loading PDF files" }
      reduce { state.copy(isLoading = false) }
    }
  }

  private fun updatePdfFiles(
    handlePdfPath: String,
    isRemovePassword: Boolean
  ) = intent {
    debugLog(tag = "PdfPickerUiModel") { "updatePdfFiles start: ${System.currentTimeMillis()}" }

    if (state.pdfFiles.isEmpty()) {
      reduce { state.copy(isLoading = true) }
    }

    try {
      // Get all documents from repository
      val allFiles = documentRepository.documentsFlow.first()

      // Filter to only include PDF files
      val pdfFiles = allFiles.filter { it.extension.lowercase() == "pdf" }

      // Cache pdfFiles
      pdfFilesStateFlow.emit(pdfFiles)

      val encryptedPdfFiles = if (state.encryptedPdfFiles.isNotEmpty()) {
         if (isRemovePassword) {
          state.encryptedPdfFiles - handlePdfPath
        } else {
          state.encryptedPdfFiles + handlePdfPath
        }
      } else {
        withContext(Dispatchers.IO) {
          pdfFiles
            .filter { PdfEncryptionDetector.isEncrypted(it) }
            .map { it.absolutePath }
            .toSet()
        }
      }

      // Cache encryptedPdfFiles
      encryptedPdfPathStateFlow.emit(encryptedPdfFiles)

      // Apply initial filter based on listType
      val filteredFiles = filterPdfFiles(pdfFiles, encryptedPdfFiles, state.query.text, listType)
      debugLog(tag = "PdfPickerUiModel") { "filteredFiles: ${System.currentTimeMillis()}" }

      reduce {
        state.copy(
          pdfFiles = pdfFiles,
          filteredPdfFiles = filteredFiles,
          encryptedPdfFiles = encryptedPdfFiles,
          isLoading = false
        )
      }
    } catch (e: Exception) {
      debugLog(tag = "PdfPickerUiModel", throwable = e) { "Error loading PDF files" }
      reduce { state.copy(isLoading = false) }
    }
  }

  fun onBookmarkClick(file: File, bookmarked: Boolean) = intent {
    val needHandleDocumentPath = file.absolutePath
    val bookmarkedPaths = userSettingsKvStore.bookmarkedDocumentPathsFlow.value

    val updatedBookmarkedDocumentPaths = if (bookmarked) {
      bookmarkedPaths + needHandleDocumentPath
    } else {
      bookmarkedPaths - needHandleDocumentPath
    }

    userSettingsKvStore.bookmarkedDocumentPathsFlow.value = updatedBookmarkedDocumentPaths
  }

  fun onListTypeChange(type: PdfPickerListType) = intent {
    val filteredFiles = filterPdfFiles(
      state.pdfFiles,
      state.encryptedPdfFiles,
      state.query.text,
      type
    )
    reduce {
      state.copy(
        listType = type,
        filteredPdfFiles = filteredFiles
      )
    }
  }

  private var searchJob: Job? = null
  fun onSearch(query: TextFieldValue) = intent {
    searchJob?.cancel()

    reduce { state.copy(query = query) }

    searchJob = uiModelScope.launch {
      val filteredFiles = filterPdfFiles(
        state.pdfFiles,
        state.encryptedPdfFiles,
        query.text,
        state.listType
      )

      reduce {
        state.copy(filteredPdfFiles = filteredFiles)
      }
    }
  }

  fun onQueryClear() = intent {
    searchJob?.cancel()
    val filteredFiles = filterPdfFiles(
      state.pdfFiles,
      state.encryptedPdfFiles,
      "",
      state.listType
    )
    reduce {
      state.copy(
        query = TextFieldValue(),
        filteredPdfFiles = filteredFiles
      )
    }
  }

  private fun filterPdfFiles(
    pdfFiles: List<File>,
    encryptedPdfFiles: Set<String>,
    searchQuery: String,
    listType: PdfPickerListType
  ): List<File> {
    // First filter by encryption status based on listType
    val typeFilteredFiles = when (listType) {
      PdfPickerListType.ALL -> pdfFiles
      PdfPickerListType.Encrypted -> pdfFiles.filter { encryptedPdfFiles.contains(it.absolutePath) }
      PdfPickerListType.Unencrypted -> pdfFiles.filter { !encryptedPdfFiles.contains(it.absolutePath) }
    }

    // Then filter by search query if provided
    return if (searchQuery.isBlank()) {
      typeFilteredFiles
    } else {
      typeFilteredFiles.filter {
        it.name.contains(searchQuery, ignoreCase = true)
      }
    }
  }
}
