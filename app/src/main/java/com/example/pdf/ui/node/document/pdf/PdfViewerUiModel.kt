package com.example.pdf.ui.node.document.pdf

import android.net.Uri
import android.os.SystemClock
import android.view.MotionEvent
import android.view.View
import android.webkit.ValueCallback
import android.webkit.WebChromeClient.FileChooserParams
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.ui.graphics.Color
import com.bhuvaneshw.pdf.PdfEditor
import com.bhuvaneshw.pdf.PdfViewer.PageAlignMode
import com.bhuvaneshw.pdf.PdfViewer.ScrollSpeedLimit
import com.bhuvaneshw.pdf.PdfViewer.Zoom
import com.bhuvaneshw.pdf.addListener
import com.bhuvaneshw.pdf.compose.PdfState
import com.example.pdf.android.file.ImportDocumentFilesHelper
import com.example.pdf.android.file.nameWithoutExtension
import com.example.pdf.android.permission.FileAccessManager
import com.example.pdf.android.toast.showToast
import com.example.pdf.guia.GlobalNavigator
import com.example.pdf.kermit.debugLog
import com.example.pdf.lyricist.globalStrings
import com.example.pdf.mvi_ui_model.UiModel
import com.example.pdf.ui.feature.pdf.PdfThumbnailGenerator
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.replaceLast
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.android.annotation.KoinViewModel
import org.koin.core.annotation.InjectedParam
import java.io.File
import java.io.FileOutputStream

val DefaultScrollSpeedLimit = ScrollSpeedLimit.AdaptiveFling(flingThreshold = 0.2f)

@KoinViewModel
class PdfViewerUiModel(
  @InjectedParam private val document: File,
  @InjectedParam private val initEditMode: PdfViewerInitEditMode?,
  private val fileAccessManager: FileAccessManager,
  private val importDocumentFilesHelper: ImportDocumentFilesHelper,
) : UiModel<PdfViewerUiState, Nothing>(PdfViewerUiState()) {

  init {
    onConfigure()
  }

  private var configured = false
  private var pendingAddToPdfSignature: Uri? = null

  @Composable
  fun ConfigurePdfViewer(pdfState: PdfState?) {
    if (configured) return

    val pdfViewer = try {
      pdfState?.pdfViewer
    } catch (e: IllegalStateException) {
      e.printStackTrace()
      onBack()
      null
    }

    DisposableEffect(pdfViewer) {
      debugLog(tag = "PdfViewerUiModel", message = "pdfViewer: LaunchedEffect $pdfViewer")

      pdfViewer?.apply {
        debugLog(tag = "PdfViewerUiModel", message = "pdfViewer: apply $this")

        onReady {
          debugLog(tag = "PdfViewerUiModel", message = "pdfViewer: onReady $this")
          addListener(
            onEditorHighlightColorChange = {
              debugLog(tag = "PdfViewerUiModel", message = "onEditorHighlightColorChange: $it")
            },
            onSavePdf = { pdfBytes ->
              debugLog(tag = "PdfViewerUiModel", message = "onSavePdf: ${pdfBytes.size} bytes")
              savePdfFile(pdfBytes)
            },
            onShowFileChooser = { vcb: ValueCallback<Array<out Uri?>?>?, params: FileChooserParams? ->
              debugLog(tag = "PdfViewerUiModel", message = "onShowFileChooser")

              pendingAddToPdfSignature?.let { signatureUri ->
                vcb?.onReceiveValue(arrayOf(signatureUri))
                pendingAddToPdfSignature = null
              }

              true
            },
            onScaleChange = {
              debugLog(tag = "PdfViewerUiModel", message = "onScaleChange: $it")
              if (configured.not()) {
                minPageScale = it
                maxPageScale = it * 3
              }

              if (configured) {
                intent { reduce { state.copy(showPageTag = it <= minPageScale) } }
              }
            },
            onDoubleClick = {
              if (currentPageScale == maxPageScale) {
                zoomTo(Zoom.PAGE_WIDTH)
              } else {
                zoomIn()
                zoomIn()
                zoomIn()

                pageAlignMode = PageAlignMode.CENTER_BOTH
              }
            },
            onPageLoadFailed = {
              configured = true
            },
            onPasswordDialogChange = { showPasswordDialog ->
              if (showPasswordDialog) {
                intent {
                  reduce { state.copy(submitPasswordTimes = state.submitPasswordTimes + 1) }
                }
              }
            },
            onPageLoadSuccess = {
              val currentPage = container.stateFlow.value.currentPage.takeIf { it != 0 } ?: 1
              debugLog(tag = "PdfViewerUiModel") { "onPageLoadSuccess currentPage: $currentPage" }

//              goToPage(1)
              singlePageArrangement = true
              pageAlignMode = PageAlignMode.CENTER_BOTH
              scrollSpeedLimit =
                if (container.stateFlow.value.editMode != null) ScrollSpeedLimit.None else DefaultScrollSpeedLimit

              configured = true

              goToPage(currentPage)
              intent {
                reduce { state.copy(pageCount = it, currentPage = currentPage, submitPasswordTimes = -1) }
              }
            },
            onPageChange = {
              intent { reduce { state.copy(showPageTag = true, currentPage = it) } }
            },
          )

          snapPage = true
        }
      }

      onDispose {
        configured = false
      }
    }
  }

  private fun onConfigure() = intent {
    val pdfState = PdfState(
      source = document.absolutePath,
      highlightEditorColors = listOf(
        "lb" to Color(0xFF9FE1FF),
      )
    )

    reduce {
      state.copy(
        pdfState = pdfState
      )
    }

    when (initEditMode) {
      PdfViewerInitEditMode.Annotate -> turnOnEditMode()
      PdfViewerInitEditMode.Signature -> {
        reduce { state.copy(enabledEditMode = true, showSignatureChooser = true) }
      }

      null -> {}
    }

    onLoadPdfThumbnailsIfNeededInternal()
  }

  private suspend fun onLoadPdfThumbnailsIfNeededInternal(password: String? = null) = subIntent {
    if (state.pdfThumbnailItems.isNotEmpty()) return@subIntent

    val generateThumbnails = PdfThumbnailGenerator.generateThumbnails(document, password)

    reduce { state.copy(pdfThumbnailItems = generateThumbnails) }
  }

  fun onSubmitPdfPassword(password: String? = null) = intent {
    if (password == null) return@intent

    uiModelScope.launch {
      onLoadPdfThumbnailsIfNeededInternal(password)
    }
    withContext(Dispatchers.Main.immediate) {
      state.pdfState?.pdfViewer?.ui?.passwordDialog?.submitPassword(password)
    }
  }

  fun onCancelPdfPasswordInput() = intent {
    withContext(Dispatchers.Main.immediate) {
      state.pdfState?.pdfViewer?.ui?.passwordDialog?.cancel()
    }
    GlobalNavigator.transaction { pop() }
  }

  fun turnOnEditMode() = intent {
    reduce { state.copy(enabledEditMode = true) }
  }

  fun turnOffEditMode() = intent {
    val editMode = state.editMode
    val delayDurationMillis =
      if (editMode == PdfViewerEditMode.Signature)
        300L
      else 0L
    onEditModeChangeInternal(null)
    delay(delayDurationMillis)
    reduce { state.copy(enabledEditMode = false) }
  }

  private suspend fun onEditModeChangeInternal(editMode: PdfViewerEditMode?) = subIntent {
    if (editMode != null) {
      state.pdfState?.pdfViewer?.apply {
        post { scrollSpeedLimit = ScrollSpeedLimit.None }
      }
    } else {
      state.pdfState?.pdfViewer?.apply {
        post { scrollSpeedLimit = DefaultScrollSpeedLimit }
      }
    }

    state.pdfState?.pdfViewer?.editor?.selectMode(editMode)
    reduce { state.copy(editMode = editMode) }
  }

  fun onEditModeChange(editMode: PdfViewerEditMode?) = intent {
    onEditModeChangeInternal(editMode)
  }

  fun onBack(
    onBackAction: () -> Unit = { GlobalNavigator.tryTransaction { pop() } }
  ) = intent {
    if (state.showSignatureChooser) {
      onDismissSignatureChooser()
    } else if (state.enabledEditMode) {
      reduce { state.copy(showLeaveEditModeBottomSheet = true) }
    } else {
      onBackAction()
    }
  }

  fun onDismissLeaveEditModeBottomSheet() = intent {
    reduce { state.copy(showLeaveEditModeBottomSheet = false) }
  }

  fun onClearChangeAndTurnOffEditMode() = intent {
    reduce { state.copy(enabledEditMode = false) }
    onEditModeChangeInternal(PdfViewerEditMode.INK)
    delay(200)
    withContext(Dispatchers.Main) {
      repeat(1000) {
        state.pdfState?.pdfViewer?.editor?.undo()
        debugLog(tag = "PdfViewerUiModel", message = "onBack: undo $it")
      }
    }

    turnOffEditMode()
  }

  fun onShowSignatureChooser() = intent {
    reduce { state.copy(showSignatureChooser = true) }
  }

  fun onDismissSignatureChooser() = intent {
//    onEditModeChangeInternal(null)
    reduce { state.copy(showSignatureChooser = false) }
  }

  fun onAddSignatureToChooserList(uri: Uri) = intent {
    reduce { state.copy(signatureList = listOf(uri) + state.signatureList) }
  }

  /**
   * Remove a signature from the chooser list
   */
  fun onRemoveSignatureFromChooserList(uri: Uri) = intent {
    reduce { state.copy(signatureList = state.signatureList.filter { it != uri }) }
  }

  fun onAddSignatureToPdf(uri: Uri) = intent {
    withContext(Dispatchers.Main.immediate) {
      pendingAddToPdfSignature = uri
    }
    onEditModeChangeInternal(PdfViewerEditMode.Signature)
  }

  private suspend fun PdfEditor.selectMode(editMode: PdfViewerEditMode?) = subIntent {
    reduce { state.copy(editMode = editMode) }

    withContext(Dispatchers.Main.immediate) {
      textHighlighterOn = false
      freeTextOn = false
      inkOn = false
      stampOn = false

      when (editMode) {
        PdfViewerEditMode.HIGHLIGHT -> textHighlighterOn = true
        PdfViewerEditMode.INK -> {
          inkOn = true
          inkThickness = 3
        }
        is PdfViewerEditMode.Signature -> {
          state.pdfState?.pdfViewer?.let {
            simulateClickOnView(it, 1f, 1f)
          }
          stampOn = true
        }

        null -> {}
      }
    }
  }

  private fun simulateClickOnView(view: View, x: Float, y: Float) {
    val downTime = SystemClock.uptimeMillis()
    val eventTime = SystemClock.uptimeMillis()

    val downEvent = MotionEvent.obtain(
      downTime, eventTime, MotionEvent.ACTION_DOWN, x, y, 0
    )
    val upEvent = MotionEvent.obtain(
      downTime, eventTime, MotionEvent.ACTION_UP, x, y, 0
    )

    view.dispatchTouchEvent(downEvent)
    view.dispatchTouchEvent(upEvent)

    downEvent.recycle()
    upEvent.recycle()
  }

  /**
   * Save the PDF file based on file access permissions
   * @param pdfBytes The PDF content as ByteArray
   */
  private fun savePdfFile(pdfBytes: ByteArray) = intent {
    val hasFileAccessPermission = fileAccessManager.hasFileAccessPermission()

    try {
      if (hasFileAccessPermission) {
        // Save to the original document location (overwrite)
        FileOutputStream(document).use { outputStream ->
          outputStream.write(pdfBytes)
        }
        showToast(globalStrings.savedToDocuments.format(document.absolutePath))
        GlobalNavigator.transaction { replaceLast(PdfViewerNode(document)) }
      } else {
        // Save to internal documents directory with timestamp
        val internalDocumentDir = importDocumentFilesHelper.getInternalDocumentDirectory()

        // Ensure directory exists
        if (!internalDocumentDir.exists()) {
          internalDocumentDir.mkdirs()
        }

        // Create filename with timestamp
        val timestamp = System.currentTimeMillis()
        val originalName = document.nameWithoutExtension
        val newFileName = "${originalName}_${timestamp}.pdf"
        val outputFile = File(internalDocumentDir, newFileName)

        // Write the PDF content to the file
        FileOutputStream(outputFile).use { outputStream ->
          outputStream.write(pdfBytes)
        }

        showToast(globalStrings.savedToInternal.format(outputFile.absolutePath))

        GlobalNavigator.transaction { replaceLast(PdfViewerNode(outputFile)) }
      }
    } catch (e: Exception) {
      debugLog(tag = "PdfViewerUiModel", throwable = e) { "Error saving PDF: ${e.message}" }
      showToast(globalStrings.failedToSavePdf.format(e.message))
    }
  }

}

