package com.example.pdf.ui.node.pdf_picker

import androidx.compose.ui.text.input.TextFieldValue
import java.io.File

data class PdfPickerUiState(
  val pdfFiles: List<File> = emptyList(),
  val filteredPdfFiles: List<File> = emptyList(),
  val encryptedPdfFiles: Set<String> = emptySet(), // Paths of encrypted PDFs
  val bookmarkedFilePaths: Set<String> = emptySet(), // Paths of bookmarked PDFs
  val listType: PdfPickerListType = PdfPickerListType.ALL,
  val query: TextFieldValue = TextFieldValue(),
  val isLoading: Boolean = false,
)
