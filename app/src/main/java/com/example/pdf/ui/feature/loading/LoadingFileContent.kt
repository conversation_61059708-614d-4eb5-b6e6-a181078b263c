package com.example.pdf.ui.feature.loading

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition

@Composable
fun LoadingFileContent(modifier: Modifier = Modifier) {
  val composition by rememberLottieComposition(
    spec = LottieCompositionSpec.Asset("loading.json"),
  )
  LottieAnimation(
    composition = composition,
    modifier = modifier,
    iterations = Int.MAX_VALUE,
    contentScale = ContentScale.FillBounds
  )
}