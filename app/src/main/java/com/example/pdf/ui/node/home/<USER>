package com.example.pdf.ui.node.home

import android.net.Uri
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.exclude
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalWindowInfo
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.LifecycleStartEffect
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.R
import com.example.pdf.android.context.findActivity
import com.example.pdf.android.permission.FileAccessManager
import com.example.pdf.biz.ad.banner.BannerAd
import com.example.pdf.biz.ad.banner.BannerAdPlace
import com.example.pdf.biz.ad.interstitial.OnTryToShowInterAdAndNavAction
import com.example.pdf.biz.ad.interstitial.interstitialAdRegister
import com.example.pdf.biz.rating.RatingHelper
import com.example.pdf.biz.skipSplash
import com.example.pdf.guia.ScreenNode
import com.example.pdf.kermit.debugLog
import com.example.pdf.lumo.AppColors
import com.example.pdf.lumo.components.HorizontalDivider
import com.example.pdf.lumo.components.Icon
import com.example.pdf.lumo.components.NavigationBarDefaults.NavigationBarHeight
import com.example.pdf.lumo.components.NavigationBarItem
import com.example.pdf.lumo.components.NavigationBarItemDefaults
import com.example.pdf.lumo.components.Scaffold
import com.example.pdf.lumo.components.ScaffoldDefaults
import com.example.pdf.lumo.components.Text
import com.example.pdf.mvi_ui_model.koinUiModel
import com.example.pdf.ui.feature.document_scanner.DocumentScannerContainer
import com.example.pdf.ui.feature.loading.LoadingDialog
import com.example.pdf.ui.node.home.all.AllDocumentsScreen
import com.example.pdf.ui.node.home.bookmarks.BookmarksScreen
import com.example.pdf.ui.node.home.recent.RecentDocumentsScreen
import com.example.pdf.ui.node.home.tools.ToolsScreen
import com.example.pdf.ui.node.home.tools.ToolsUiModel
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.roudikk.guia.backstack.NavBackHandler
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.currentKey
import kotlinx.coroutines.delay
import kotlinx.parcelize.Parcelize
import org.koin.compose.koinInject
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState

enum class HomeNodeAction {
  Scan2Pdf
}

@Parcelize
class HomeNode(
  private val tab: HomeTab = HomeTab.ALL,
  var action: HomeNodeAction? = null
) : ScreenNode("home") {

  @Composable
  private fun ConfigureStatusBarLightIcons() {
    val systemUiController = rememberSystemUiController()

    LifecycleStartEffect(Unit) {
      systemUiController.setStatusBarColor(
        color = Color.Transparent,
        darkIcons = false
      )

      onStopOrDispose {}
    }
  }

  @Composable
  override fun Content(navigator: Navigator) {
    ConfigureStatusBarLightIcons()
    val context = LocalContext.current
    val windowInfo = LocalWindowInfo.current

    val homeUiModel: HomeUiModel = koinUiModel { parametersOf(tab) }
    val homeUiState by homeUiModel.collectAsState()

    val toolsUiModel: ToolsUiModel = koinUiModel()
    val toolsUiState by toolsUiModel.collectAsState()

    val fileAccessManager: FileAccessManager = koinInject()

    LaunchedEffect(windowInfo.isWindowFocused) {
      delay(100)
      if (windowInfo.isWindowFocused && navigator.currentKey is HomeNode) {
        RatingHelper.tryToOpenReviews()
      }
    }

    LaunchedEffect(homeUiState.currentTab) {
      fileAccessManager.checkAndRequestFileAccess(navigator)
    }

    LifecycleStartEffect(Unit) {
      debugLog(tag = "HomeNode") { "Lifecycle started for HomeNode" }
      homeUiModel.onFetchDocumentFiles()
      onStopOrDispose { }
    }

    if (toolsUiState.processing) {
      LoadingDialog()
    }

    var rememberAction by remember { mutableStateOf(action) }
    val handleActionFinish = {
      action = null
      rememberAction = null
    }

    val (onTryToShowInterAdAndNavAction, onBackAction)
      = interstitialAdRegister(navigator)

    HomeContent(
      rememberAction = rememberAction,
      uiState = homeUiState,
      onTabSelected = homeUiModel::onSelectTab,
      onScanToPdfComplete = toolsUiModel::onScanPdfComplete,
      onTryToShowInterAdAndNavAction = onTryToShowInterAdAndNavAction,
      handleActionFinish = handleActionFinish,
    )

    NavBackHandler {
      context.findActivity().moveTaskToBack(true)
    }
  }
}

@Composable
private fun HomeContent(
  rememberAction: HomeNodeAction?,
  uiState: HomeUiState,
  onTabSelected: (HomeTab) -> Unit,
  onScanToPdfComplete: (Uri) -> Unit,
  onTryToShowInterAdAndNavAction: OnTryToShowInterAdAndNavAction,
  handleActionFinish: () -> Unit
) {
  Scaffold(
    floatingActionButton = {
      if (uiState.currentTab != HomeTab.TOOLS) {
        DocumentScannerContainer(
          onScanComplete = {
            it.firstOrNull()?.let { uri ->
              onScanToPdfComplete(uri)
            }
          },
        ) { startScanner ->
          LaunchedEffect(rememberAction == HomeNodeAction.Scan2Pdf) {
            if (rememberAction == HomeNodeAction.Scan2Pdf) {
              skipSplash()
              startScanner()
            }
            handleActionFinish()
          }

          Image(
            painter = painterResource(R.drawable.fab_scan_to_pdf),
            contentDescription = null,
            modifier = Modifier
              .padding(bottom = 24.dp, end = 4.dp)
              .size(60.dp)
              .clip(CircleShape)
              .clickable {
                onTryToShowInterAdAndNavAction("open_scan2pdf") {
                  skipSplash()
                  startScanner()
                }
              }
          )
        }
      }
    },
    bottomBar = {
      HomeBottomNavigation(
        currentTab = uiState.currentTab,
        onTabSelected = onTabSelected,
        modifier = Modifier.navigationBarsPadding()
      )
    },
    contentWindowInsets = ScaffoldDefaults.contentWindowInsets.exclude(WindowInsets.statusBars)
  ) { paddingValues ->
    Box(
      modifier = Modifier
        .fillMaxSize()
        .padding(paddingValues),
      contentAlignment = Alignment.Center
    ) {
      when (uiState.currentTab) {
        HomeTab.ALL -> AllDocumentsScreen(onTryToShowInterAdAndNavAction = onTryToShowInterAdAndNavAction)
        HomeTab.RECENT -> RecentDocumentsScreen(onTryToShowInterAdAndNavAction = onTryToShowInterAdAndNavAction)
        HomeTab.BOOKMARKS -> BookmarksScreen(onTryToShowInterAdAndNavAction = onTryToShowInterAdAndNavAction)
        HomeTab.TOOLS -> ToolsScreen(onTryToShowInterAdAndNavAction = onTryToShowInterAdAndNavAction)
      }
    }
  }
}

@Composable
private fun HomeBottomNavigation(
  currentTab: HomeTab,
  onTabSelected: (HomeTab) -> Unit,
  modifier: Modifier = Modifier
) {
  val strings = LocalStrings.current

  val navigationBarItemColors = NavigationBarItemDefaults.colors(
    selectedTextColor = AppColors.primary,
    selectedIconColor = AppColors.primary,
    unselectedTextColor = AppColors.secondary,
    unselectedIconColor = AppColors.secondary,
  )


  Column(modifier) {
    Row(
      modifier = Modifier
        .height(NavigationBarHeight)
        .selectableGroup()
    ) {
      NavigationBarItem(
        selected = currentTab == HomeTab.ALL,
        onClick = { onTabSelected(HomeTab.ALL) },
        icon = {
          Icon(
            painter = painterResource(R.drawable.ic_nav_all),
            contentDescription = "All Files"
          )
        },
        label = { Text(text = strings.all) },
        colors = navigationBarItemColors,
      )
      NavigationBarItem(
        selected = currentTab == HomeTab.RECENT,
        onClick = { onTabSelected(HomeTab.RECENT) },
        icon = {
          Icon(
            painter = painterResource(R.drawable.ic_nav_recents),
            contentDescription = "Recent Files"
          )
        },
        label = { Text(text = strings.recent) },
        colors = navigationBarItemColors,
      )
      NavigationBarItem(
        selected = currentTab == HomeTab.BOOKMARKS,
        onClick = { onTabSelected(HomeTab.BOOKMARKS) },
        icon = {
          Icon(
            painter = painterResource(R.drawable.ic_nav_bookmarks),
            contentDescription = "Bookmarks"
          )
        },
        label = { Text(text = strings.bookmarks) },
        colors = navigationBarItemColors,
      )
      NavigationBarItem(
        selected = currentTab == HomeTab.TOOLS,
        onClick = { onTabSelected(HomeTab.TOOLS) },
        icon = {
          Icon(
            painter = painterResource(R.drawable.ic_nav_tools),
            contentDescription = "Tools"
          )
        },
        label = { Text(text = strings.tools) },
        colors = navigationBarItemColors,
      )
    }

    BannerAd(adPlace = BannerAdPlace.Home)
  }


  HorizontalDivider()
}
