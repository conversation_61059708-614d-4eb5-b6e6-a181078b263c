package com.example.pdf.ui.node.home

import com.example.pdf.android.file.DocumentFilesManager
import com.example.pdf.android.file.DocumentRepository
import com.example.pdf.android.file.ImportDocumentFilesHelper
import com.example.pdf.mvi_ui_model.UiModel
import kotlinx.coroutines.flow.StateFlow
import org.koin.android.annotation.KoinViewModel
import org.koin.core.annotation.InjectedParam

var HomeStateFlow: StateFlow<HomeUiState>? = null

@KoinViewModel
class HomeUiModel(
  @InjectedParam tab: HomeTab,
  private val documentFilesManager: DocumentFilesManager,
  private val importDocumentFilesHelper: ImportDocumentFilesHelper,
  private val documentRepository: DocumentRepository,
) : UiModel<HomeUiState, HomeSideEffect>(HomeUiState()) {

  init {
    onSelectTab(tab)
    HomeStateFlow = container.stateFlow
  }

  fun onSelectTab(tab: HomeTab) = intent {
    reduce {
      state.copy(currentTab = tab)
    }
  }

  fun onFetchDocumentFiles() {
    documentRepository.onFetchDocuments()
  }

}