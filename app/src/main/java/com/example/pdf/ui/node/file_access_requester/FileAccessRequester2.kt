package com.example.pdf.ui.node.file_access_requester

import android.Manifest
import android.os.Build
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.LifecycleStartEffect
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.PreviewComposable
import com.example.pdf.R
import com.example.pdf.android.permission.FileAccessManager
import com.example.pdf.biz.skipSplash
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Button
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.card.CardDefaults
import com.example.pdf.ui.composable.BlankSpacer
import com.google.accompanist.permissions.rememberMultiplePermissionsState
import org.koin.compose.koinInject

enum class FileAccessTipsPanelType {
  Layer, Item
}

@Composable
fun FileAccessTipsPanel(
  modifier: Modifier = Modifier,
  panelType: FileAccessTipsPanelType = FileAccessTipsPanelType.Layer,
) {
  val fileAccessManager: FileAccessManager = koinInject()

  var showTips by remember { mutableStateOf(fileAccessManager.hasFileAccessPermission().not()) }

  LifecycleStartEffect(Unit) {
    showTips = fileAccessManager.hasFileAccessPermission().not()
    onStopOrDispose { }
  }

  val fileAccessPermissionLauncher = rememberLauncherForActivityResult(
    ActivityResultContracts.StartActivityForResult()
  ) { _ ->
    if (fileAccessManager.hasFileAccessPermission()) {
      showTips = false
    }
  }

  val externalStoragePermissionState = rememberMultiplePermissionsState(
    listOf(
      Manifest.permission.READ_EXTERNAL_STORAGE,
      Manifest.permission.WRITE_EXTERNAL_STORAGE,
    )
  ) {
    if (it.values.all { isGranted -> isGranted }) {
      showTips = false
    }
  }

  val onGrantClick = remember {
    {
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
        val intent = fileAccessManager.createFileAccessPermissionIntent()
        if (intent != null) {
          skipSplash()
          fileAccessPermissionLauncher.launch(intent)
        }
      } else {
        externalStoragePermissionState.launchMultiplePermissionRequest()
      }
    }
  }

  if (showTips) {
    when (panelType) {
      FileAccessTipsPanelType.Layer -> {
        FileAccessRequester2Content(
          modifier = modifier,
          onGrantClick = onGrantClick,
        )
      }

      FileAccessTipsPanelType.Item -> {
        FileAccessRequesterItemContent(
          modifier = modifier,
          onGrantClick = onGrantClick,
        )
      }
    }
  }
}

@Composable
private fun FileAccessRequester2Content(
  modifier: Modifier = Modifier,
  onGrantClick: () -> Unit = {},
) {
  val strings = LocalStrings.current

  Column(
    modifier = modifier
      .background(color = AppTheme.colors.surface),
    verticalArrangement = Arrangement.Center,
    horizontalAlignment = Alignment.CenterHorizontally
  ) {
    Image(
      painter = painterResource(R.drawable.ic_permission_access_file_locked),
      contentDescription = null,
      modifier = Modifier.size(80.dp)
    )
    BlankSpacer(height = 16.dp)

    Text(
      text = getFileAccessRequesterTipsString(),
      style = AppTheme.typography.body2,
      color = AppTheme.colors.textSecondary,
      fontWeight = FontWeight.Medium,
      modifier = Modifier
        .fillMaxWidth()
        .padding(horizontal = 40.dp),
      textAlign = TextAlign.Center
    )

    BlankSpacer(height = 22.dp)

    Button(
      onClick = onGrantClick,
      modifier = Modifier
        .fillMaxWidth()
        .padding(horizontal = 80.dp)
    ) {
      Text(text = strings.grant, style = AppTheme.typography.buttonLarge)
    }
  }
}

@Composable
private fun FileAccessRequesterItemContent(
  modifier: Modifier = Modifier,
  onGrantClick: () -> Unit = {}
) {
  val strings = LocalStrings.current

  Row(
    modifier = modifier
      .fillMaxWidth()
      .clip(CardDefaults.Shape)
      .background(AppTheme.colors.disabled.copy(.4f))
      .padding(horizontal = 16.dp, vertical = 12.dp),
    verticalAlignment = Alignment.CenterVertically,
    horizontalArrangement = Arrangement.SpaceBetween
  ) {
    Column(modifier = Modifier.weight(1f)) {
      Text(
        text = strings.notice,
        style = AppTheme.typography.h3,
      )
      BlankSpacer(height = 2.dp)
      Text(
        text = getFileAccessRequesterTipsShortString(),
        style = AppTheme.typography.body3,
        color = AppTheme.colors.textSecondary
      )
    }
    BlankSpacer(width = 16.dp)

    Button(
      onClick = onGrantClick,
      contentPadding = PaddingValues(),
      modifier = Modifier
        .height(32.dp)
        .clip(CardDefaults.Shape)
    ) {
      Text(
        text = strings.go,
        style = AppTheme.typography.button,
        modifier = Modifier.padding(horizontal = 20.dp)
      )
    }
  }
}

@Preview(showBackground = true)
@Composable
private fun FileAccessRequesterContent2Preview() {
  PreviewComposable {
    FileAccessRequester2Content()
  }
}

@Preview(showBackground = true)
@Composable
private fun FileAccessRequesterItemContentPreview() {
  PreviewComposable {
    FileAccessRequesterItemContent(modifier = Modifier.padding(16.dp))
  }
}