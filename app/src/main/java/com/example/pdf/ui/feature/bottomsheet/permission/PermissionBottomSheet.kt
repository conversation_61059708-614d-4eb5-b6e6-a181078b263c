package com.example.pdf.ui.feature.bottomsheet.permission

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Button
import com.example.pdf.lumo.components.ButtonVariant
import com.example.pdf.lumo.components.Text
import com.example.pdf.ui.feature.bottomsheet.BottomSheetSurface

@Composable
fun PermissionBottomSheet(
  iconPainter: Painter,
  title: String,
  description: String,
  buttonText: String,
  onButtonClick: () -> Unit = {}
) {
  BottomSheetSurface {
    Column(
      modifier = Modifier
        .fillMaxWidth()
        .padding(24.dp),
      horizontalAlignment = Alignment.CenterHorizontally
    ) {
      Row {
        Column(modifier = Modifier.weight(1f)) {
          Text(
            text = title,
            style = AppTheme.typography.h2,
            color = AppTheme.colors.onBackground
          )

          Spacer(modifier = Modifier.height(8.dp))

          Text(
            text = description,
            style = AppTheme.typography.body1.copy(
              fontWeight = FontWeight.Medium,
              lineHeight = 19.sp
            ),
            color = AppTheme.colors.onBackground.copy(alpha = 0.7f)
          )
        }

        Spacer(modifier = Modifier.width(16.dp))

        Image(
          painter = iconPainter,
          contentDescription = null,
          modifier = Modifier.size(44.dp)
        )
      }

      Spacer(modifier = Modifier.height(24.dp))

      Button(
        onClick = onButtonClick,
        modifier = Modifier
          .fillMaxWidth()
          .height(48.dp),
        variant = ButtonVariant.Primary
      ) {
        Text(
          text = buttonText,
          style = AppTheme.typography.buttonLarge,
        )
      }
    }
  }
}