package com.example.pdf.ui.node.document.pdf

import androidx.compose.foundation.layout.Column
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.painterResource
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.android.res.ResDrawable
import com.example.pdf.biz.ad.nat1ve.NativeAd
import com.example.pdf.biz.ad.nat1ve.NativeAdPlace
import com.example.pdf.ui.feature.bottomsheet.permission.TipsBottomSheetContent
import com.holix.android.bottomsheetdialog.compose.BottomSheetDialog

@Composable
fun LeaveEditModeBottomSheet(
  onConfirm: () -> Unit,
  onDismiss: () -> Unit
) {
  BottomSheetDialog(onDismissRequest = onDismiss) {

    Column {
      TipsBottomSheetContent(
        title = LocalStrings.current.leaveNow,
        description = LocalStrings.current.leaveEditModeWarning,
        iconPainter = painterResource(ResDrawable.img_editor_leave),
        confirmText = LocalStrings.current.leave,
        onConfirm = onConfirm,
        onCancel = onDismiss
      )

      NativeAd(adPlace = NativeAdPlace.EditPdfLeaveConfirm)
    }
  }

}