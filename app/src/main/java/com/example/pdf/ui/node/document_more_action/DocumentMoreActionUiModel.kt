package com.example.pdf.ui.node.document_more_action

import com.example.pdf.android.file.DocumentRepository
import com.example.pdf.android.file.documentsDelete
import com.example.pdf.biz.rating.RatingHelper
import com.example.pdf.guia.GlobalNavigator
import com.example.pdf.mmkv.UserSettingsKvStore
import com.example.pdf.mvi_ui_model.UiModel
import com.roudikk.guia.extensions.pop
import org.koin.android.annotation.KoinViewModel
import java.io.File

@KoinViewModel
class DocumentMoreActionUiModel(
  private val document: File,
  private val userSettingsKvStore: UserSettingsKvStore,
  private val documentRepository: DocumentRepository
) : UiModel<DocumentMoreActionUiState, Nothing>(DocumentMoreActionUiState()) {

  init {
    reduceIsBookmarked()
  }

  private fun reduceIsBookmarked() = intent {
    reduce {
      state.copy(isBookmarked = document.absolutePath in userSettingsKvStore.bookmarkedDocumentPathsFlow.value)
    }
  }

  fun onBookmarkClick(file: File, bookmarked: Boolean) = intent {
    val needHandleDocumentPath = file.absolutePath
    val bookmarkedPaths = userSettingsKvStore.bookmarkedDocumentPathsFlow.value

    val updatedBookmarkedDocumentPaths = if (bookmarked) {
      RatingHelper.setCanRatting(true)
      bookmarkedPaths + needHandleDocumentPath
    } else {
      bookmarkedPaths - needHandleDocumentPath
    }

    userSettingsKvStore.bookmarkedDocumentPathsFlow.value = updatedBookmarkedDocumentPaths

    reduce { state.copy(isBookmarked = file.absolutePath in updatedBookmarkedDocumentPaths) }
  }

  fun onShowDeleteConfirmationDialog() = intent {
    reduce { state.copy(showDeleteConfirmationDialog = true) }
  }

  fun onDismissDeleteConfirmationDialog() = intent {
    reduce { state.copy(showDeleteConfirmationDialog = false) }
  }

  fun onDeleteConfirm() = intent {
    documentsDelete(
      documents = listOf(document),
      onSuccess = {
        intent {
          documentRepository.onFetchDocuments()
          reduce { state.copy(showDeleteConfirmationDialog = false) }
          GlobalNavigator.transaction { pop() }
        }
      },
    )
  }
}