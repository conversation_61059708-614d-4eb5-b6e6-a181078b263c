package com.example.pdf.ui.feature.pdf

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import com.example.pdf.lumo.AppTheme
import com.example.pdf.ui.feature.PageIndexTag

/**
 * Data class representing a PDF thumbnail item
 */
data class PdfThumbnailItem(
  val pageIndex: Int,
  val thumbnail: ImageBitmap?
)

/**
 * A horizontal list of PDF thumbnails that can be scrolled horizontally.
 * Each thumbnail displays the page index in the top-left corner and shows a colored border when selected.
 *
 * @param thumbnails List of PDF thumbnail items to display
 * @param selectedPageIndex The currently selected page index
 * @param onThumbnailSelected Callback when a thumbnail is selected
 * @param modifier Modifier for the LazyRow
 * @param contentPadding Padding values for the content
 * @param thumbnailHeight Height of each thumbnail
 */
@Composable
fun PdfThumbnailHorizontalList(
  thumbnails: List<PdfThumbnailItem>,
  selectedPageIndex: Int,
  onThumbnailSelected: (Int) -> Unit,
  modifier: Modifier = Modifier,
  contentPadding: PaddingValues = PaddingValues(horizontal = 16.dp),
  thumbnailHeight: Int = 96
) {
  val listState = rememberLazyListState()

  LaunchedEffect(selectedPageIndex) {
    if (selectedPageIndex < 0) return@LaunchedEffect
    listState.animateScrollToItem(selectedPageIndex)
  }

  LazyRow(
    state = listState,
    contentPadding = contentPadding,
    modifier = modifier
  ) {
    itemsIndexed(thumbnails) { _, item ->
      PdfThumbnailItem(
        thumbnail = item.thumbnail,
        pageIndex = item.pageIndex,
        isSelected = item.pageIndex == selectedPageIndex,
        onClick = { onThumbnailSelected(item.pageIndex) },
        modifier = Modifier
          .height(thumbnailHeight.dp)
          .padding(horizontal = 4.dp, vertical = 8.dp)
      )
    }
  }
}

/**
 * A single PDF thumbnail item with page index tag and selection indicator
 *
 * @param thumbnail The thumbnail image bitmap
 * @param pageIndex The page index to display
 * @param isSelected Whether this thumbnail is currently selected
 * @param onClick Callback when this thumbnail is clicked
 * @param modifier Modifier for the thumbnail
 */
@Composable
private fun PdfThumbnailItem(
  thumbnail: ImageBitmap?,
  pageIndex: Int,
  isSelected: Boolean,
  onClick: () -> Unit,
  modifier: Modifier = Modifier
) {
  val shape = RoundedCornerShape(8.dp)
  val borderColor = if (isSelected) AppTheme.colors.primary else Color.Transparent
  val borderWidth = if (isSelected) 2.dp else 0.dp

  Box(
    modifier = modifier
      .aspectRatio(0.7f) // Typical PDF aspect ratio
      .clip(shape)
      .border(BorderStroke(borderWidth, borderColor), shape)
      .background(Color.White)
      .clickable(onClick = onClick)
  ) {
    // Thumbnail content
    if (thumbnail != null) {
      androidx.compose.foundation.Image(
        bitmap = thumbnail,
        contentDescription = "Page $pageIndex",
        contentScale = ContentScale.Crop,
        modifier = Modifier.fillMaxSize()
      )
    } else {
      // Placeholder when thumbnail is not available
      Box(
        modifier = Modifier
          .fillMaxSize()
          .background(AppTheme.colors.disabled.copy(alpha = 0.3f))
      )
    }

    // Page index tag in the top-left corner
    PageIndexTag(
      indexText = "${pageIndex + 1}",
      modifier = Modifier
        .alpha(.8f)
        .scale(.65f)
        .align(Alignment.TopStart),
      contentPaddingValues = PaddingValues(horizontal = 8.dp, vertical = 4.dp)
    )
  }
}

