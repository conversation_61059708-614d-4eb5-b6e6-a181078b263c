package com.example.pdf.ui.node.home.tools

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.compose.LifecycleStartEffect
import cafe.adriel.lyricist.LocalStrings
import com.example.pdf.R
import com.example.pdf.android.context.findActivity
import com.example.pdf.android.context.openNotificationSettings
import com.example.pdf.biz.notification.NotificationPermissionRequestDialogNode
import com.example.pdf.biz.notification.NotificationPermissionRequester
import com.example.pdf.guia.GlobalNavigator
import com.example.pdf.lumo.components.Switch
import com.example.pdf.mmkv.UserSettingsKvStore
import com.example.pdf.ui.node.settings.SettingsItem
import com.roudikk.guia.extensions.push
import org.koin.compose.koinInject


fun Context.notificationPermissionGranted(): Boolean {
  return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
    ContextCompat.checkSelfPermission(
      this,
      Manifest.permission.POST_NOTIFICATIONS
    ) == PackageManager.PERMISSION_GRANTED
  } else {
    true
  }
}

fun Context.areNotificationsEnabled(): Boolean {
  return NotificationManagerCompat.from(this).areNotificationsEnabled()
}

@Composable
fun NotificationItem(
  modifier: Modifier = Modifier
) {
  val context = LocalContext.current
  val userSettingsKvStore: UserSettingsKvStore = koinInject()
  val strings = LocalStrings.current

  val enabledNotification by userSettingsKvStore.enabledNotification.collectAsState()
  var notificationPermissionGranted by remember { mutableStateOf(context.notificationPermissionGranted()) }

  LifecycleStartEffect(Unit) {
    notificationPermissionGranted = context.notificationPermissionGranted()
    onStopOrDispose { }
  }

  val enabled = enabledNotification && notificationPermissionGranted

  val onSwitchNotification: (switchTo: Boolean) -> Unit = { switchTo ->
    if (notificationPermissionGranted) {
      userSettingsKvStore.enabledNotification.value = switchTo
    } else {
      GlobalNavigator.tryTransaction {
        push(
          NotificationPermissionRequestDialogNode(
            useSystemPermissionDialog = false,
            autoIncrementRequestCounter = false
          )
        )
      }
      userSettingsKvStore.enabledNotification.value = true
    }
  }

  SettingsItem(
    icon = painterResource(R.drawable.ic_notification),
    title = strings.notification,
    onClick = {
      if (notificationPermissionGranted.not()) {
        GlobalNavigator.tryTransaction {
          push(
            NotificationPermissionRequestDialogNode(
              useSystemPermissionDialog = false,
              autoIncrementRequestCounter = false
            )
          )
        }
        userSettingsKvStore.enabledNotification.value = true
      }
    },
    modifier = modifier,
    suffix = {
      Switch(checked = enabled, onCheckedChange = onSwitchNotification)
    }
  )
}