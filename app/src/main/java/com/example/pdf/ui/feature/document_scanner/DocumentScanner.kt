package com.example.pdf.ui.feature.document_scanner

import android.annotation.SuppressLint
import android.app.Activity
import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.pdf.android.context.findActivity
import com.example.pdf.kermit.debugLog
import com.example.pdf.lumo.AppTheme
import com.example.pdf.lumo.components.Button
import com.example.pdf.lumo.components.Text
import com.example.pdf.lumo.components.card.Card
import com.google.android.gms.common.api.ResolvableApiException
import com.google.mlkit.vision.documentscanner.GmsDocumentScannerOptions
import com.google.mlkit.vision.documentscanner.GmsDocumentScanning
import com.google.mlkit.vision.documentscanner.GmsDocumentScanningResult

/**
 * A container composable for the document scanner functionality.
 *
 * @param options GmsDocumentScannerOptions for configuring the document scanner
 * @param onScanStarted Called when document scanning starts
 * @param onScanComplete Called when document scanning completes successfully with the result
 * @param onScanCanceled Called when document scanning is canceled by the user
 * @param onScanFailed Called when document scanning fails with an error message
 * @param content Composable content to be displayed as the trigger for document scanning
 */
@Composable
fun DocumentScannerContainer(
  options: GmsDocumentScannerOptions = rememberGmsDocumentScannerOptions(),
  onScanStarted: () -> Unit = {},
  onScanComplete: (List<Uri>) -> Unit = {},
  onScanCanceled: () -> Unit = {},
  onScanFailed: (String) -> Unit = {},
  @SuppressLint("ModifierParameter") modifier: Modifier = Modifier,
  content: @Composable (startScanner: () -> Unit) -> Unit
) {
  val context = LocalContext.current
  val scanner = GmsDocumentScanning.getClient(options)

  // Create an activity result launcher for handling the document scanner result
  val scannerLauncher = rememberLauncherForActivityResult(
    contract = ActivityResultContracts.StartIntentSenderForResult()
  ) { result ->
    when (result.resultCode) {
      Activity.RESULT_OK -> {
        // Handle successful scan
        val scanningResult = GmsDocumentScanningResult.fromActivityResultIntent(result.data)
        scanningResult?.let { gmsResult ->
          val pageUris = gmsResult.pages?.mapNotNull { page ->
            page.imageUri
          }
          val pdfUri = gmsResult.pdf?.uri

          if (!pageUris.isNullOrEmpty()) {
            onScanComplete(pageUris)
          } else if (pdfUri != null) {
            onScanComplete(listOf(pdfUri))
          } else {
            onScanFailed("No pages were scanned")
          }
        } ?: onScanFailed("Failed to process scanning result")
      }

      Activity.RESULT_CANCELED -> {
        // Handle canceled scan
        onScanCanceled()
      }

      else -> {
        // Handle failed scan
        onScanFailed("Document scanning failed with result code: ${result.resultCode}")
      }
    }
  }

  // Function to start the scanner that will be passed to the content
  val startScanner: () -> Unit = {
    onScanStarted()
    // Launch the document scanner
    scanner.getStartScanIntent(context.findActivity())
      .addOnSuccessListener { intentSender ->
        try {
          val request = IntentSenderRequest.Builder(intentSender).build()
          scannerLauncher.launch(request)
        } catch (e: Exception) {
          onScanFailed("Failed to launch scanner: ${e.message}")
        }
      }
      .addOnFailureListener { exception ->
        if (exception is ResolvableApiException) {
          try {
            val request = IntentSenderRequest.Builder(exception.resolution.intentSender).build()
            scannerLauncher.launch(request)
          } catch (e: Exception) {
            onScanFailed("Failed to resolve scanner API issue: ${e.message}")
          }
        } else {
          onScanFailed("Failed to get scanner intent: ${exception.message}")
        }
      }
  }

  Box(
    modifier = modifier,
    contentAlignment = Alignment.Center
  ) {
    // Pass the startScanner function to the content
    content(startScanner)
  }
}

@Composable
fun rememberGmsDocumentScannerOptions() = remember {
  GmsDocumentScannerOptions.Builder()
    .setGalleryImportAllowed(true)
    .setPageLimit(99)
    .setResultFormats(GmsDocumentScannerOptions.RESULT_FORMAT_PDF)
    .build()
}

/**
 * Usage example of DocumentScannerContainer.
 */
@Composable
private fun DocumentScannerExample(
  onScanPdfComplete: (Uri) -> Unit,
  onScanFailed: (String) -> Unit = {}
) {
  // Configure scanner options
  val scannerOptions = remember {
    GmsDocumentScannerOptions.Builder()
      .setGalleryImportAllowed(true)
      .setPageLimit(5)
      .setResultFormats(GmsDocumentScannerOptions.RESULT_FORMAT_PDF)
      .build()
  }

  // Use the DocumentScannerContainer with a Card content
  DocumentScannerContainer(
    options = scannerOptions,
    onScanComplete = { onScanPdfComplete(it.first()) },
    onScanFailed = onScanFailed,
    modifier = Modifier.padding(16.dp)
  ) { startScanner ->
    Card(
      modifier = Modifier
        .fillMaxWidth()
        .padding(16.dp),
    ) {
      Column(
        modifier = Modifier
          .fillMaxWidth()
          .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
      ) {
        Text(
          text = "Scan Document",
          modifier = Modifier.padding(bottom = 8.dp)
        )

        Button(
          onClick = startScanner
        ) {
          Text("Scan")
        }
      }
    }
  }
}


@Preview
@Composable
fun DocumentScannerExamplePreview() {
  AppTheme {
    DocumentScannerExample(onScanPdfComplete = {
      debugLog("Scan complete with $it")
    })
  }
}