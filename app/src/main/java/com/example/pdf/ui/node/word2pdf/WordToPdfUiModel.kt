package com.example.pdf.ui.node.word2pdf

import android.net.Uri
import android.os.Environment
import com.example.pdf.android.file.ImportDocumentFilesHelper
import com.example.pdf.android.file.document_converter.DocumentConverter
import com.example.pdf.android.permission.FileAccessManager
import com.example.pdf.android.toast.showToast
import com.example.pdf.appContext
import com.example.pdf.biz.rating.RatingHelper
import com.example.pdf.guia.GlobalNavigator
import com.example.pdf.kermit.debugLog
import com.example.pdf.lyricist.globalStrings
import com.example.pdf.mvi_ui_model.UiModel
import com.example.pdf.ui.node.convert_success.ConvertSuccessNode
import com.roudikk.guia.extensions.replaceLast
import org.koin.android.annotation.KoinViewModel
import java.io.File

@KoinViewModel
class WordToPdfUiModel(
  private val wordUri: Uri,
  private val converter: DocumentConverter,
  private val importDocumentFilesHelper: ImportDocumentFilesHelper,
  private val fileAccessManager: FileAccessManager,
) : UiModel<WordToPdfUiState, Nothing>(WordToPdfUiState()) {

  init {
    onExecuteConvert()
    RatingHelper.setCanRatting(true)
  }

  private fun onExecuteConvert() = intent {
    reduce { state.copy(isLoadingPreview = true) }

    val previewPdf = File(appContext.cacheDir, "converted_preview.pdf")
    converter.convertDocToPdfByAspose(wordUri, previewPdf.absolutePath).let { successful ->
      if (successful) {
        reduce { state.copy(isLoadingPreview = false, previewPdf = previewPdf) }
      } else {
        showToast(globalStrings.failedToLoad)
        reduce { state.copy(isLoadingPreview = false) }
      }
    }

  }

  fun onConvert() = intent {
    reduce { state.copy(converting = true) }

    val previewPdf = state.previewPdf

    if (previewPdf != null) {
      val internalDocumentDir = importDocumentFilesHelper.getInternalDocumentDirectory()
      val publicDocumentDir =
        Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)

      val hasFileAccessPermission = fileAccessManager.hasFileAccessPermission()

      val targetDir = if (hasFileAccessPermission) {
        publicDocumentDir
      } else {
        internalDocumentDir
      }

      try {
        // Ensure the target directory exists
        if (!targetDir.exists()) {
          targetDir.mkdirs()
        }

        val timestamp = System.currentTimeMillis()
        val newFileName = "PDF_Converted_$timestamp.pdf"
        val destinationFile = File(targetDir, newFileName)

        previewPdf.copyTo(destinationFile, overwrite = true)
        val savedLocationMessage = if (hasFileAccessPermission) {
          globalStrings.savedToDocuments.format(destinationFile.absolutePath)
        } else {
          globalStrings.savedToInternal.format(destinationFile.absolutePath)
        }
        showToast(savedLocationMessage)

        GlobalNavigator.transaction {
          replaceLast(ConvertSuccessNode(destinationFile))
        }

      } catch (e: Exception) {
        debugLog(tag = "WordToPdfUiModel", message = e.stackTraceToString())
        showToast(globalStrings.failedToSavePdf.format(e.localizedMessage))
      } finally {
        // Always update the processing state, regardless of success or failure
        reduce { state.copy(converting = false) }
      }

    } else {
      showToast(globalStrings.convertFailed)
      reduce { state.copy(converting = false) }
    }
  }

}