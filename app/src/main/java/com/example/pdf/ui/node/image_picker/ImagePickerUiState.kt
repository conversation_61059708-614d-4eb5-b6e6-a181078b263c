package com.example.pdf.ui.node.image_picker

import android.net.Uri

data class ImagePickerUiState(
  val isLoading: Boolean = false,
  val albums: List<ImageAlbum> = emptyList(),
  val selectedAlbum: ImageAlbum? = null,
  val images: List<ImageItem> = emptyList(),
  val selectedImages: List<ImageItem> = emptyList(),
  val showAlbumDropdown: Boolean = false,
)

data class ImageAlbum(
  val id: String,
  val name: String,
  val coverUri: Uri?,
  val imageCount: Int
)

data class ImageItem(
  val id: String,
  val uri: Uri,
  val displayName: String,
  val dateAdded: Long,
  val size: Long,
  val bucketId: String? = null,
  val bucketName: String? = null
)
