package com.example.pdf

import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.WindowInsets
import android.view.WindowInsetsController
import androidx.activity.ComponentActivity

typealias ActivityStack = MutableList<BaseActivity>

inline fun <reified T : BaseActivity> ActivityStack.finish() {
  var needFinishActivity: BaseActivity? = null

  forEach {
    if (it is T) {
      needFinishActivity = it
    }
  }

  needFinishActivity?.let {
    remove(it)
    it.finish()
  }
}

fun ActivityStack.top(): BaseActivity? = firstOrNull()

val activityStack: ActivityStack = mutableListOf<BaseActivity>()

abstract class BaseActivity : ComponentActivity() {

  override fun onCreate(savedInstanceState: Bundle?) {
    activityStack.add(0, this)
    super.onCreate(savedInstanceState)
  }

  override fun onResume() {
    activityStack.remove(this)
    activityStack.add(0, this)
    super.onResume()
  }

  override fun onDestroy() {
    activityStack.remove(this)
    super.onDestroy()
  }


  protected fun setFullScreen() {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
      // Android 11+ 使用新API
      window.insetsController?.let { controller ->
        controller.hide(WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars())
        controller.systemBarsBehavior = WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
      }
    } else {
      // Android 11以下仍使用旧API（虽然已弃用但仍可用）
      @Suppress("DEPRECATION")
      window.decorView.systemUiVisibility = (
        View.SYSTEM_UI_FLAG_FULLSCREEN or
          View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
          View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        )
    }
  }

}