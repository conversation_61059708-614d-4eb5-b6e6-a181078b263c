package com.example.pdf.serialization

import kotlinx.serialization.json.Json
import kotlinx.serialization.serializer
import kotlin.reflect.KType


val defaultJsonSerializer = Json {
  isLenient = true
  ignoreUnknownKeys = true
}

inline fun <reified T> T.toJson(): String {
  return defaultJsonSerializer.encodeToString(this)
}

inline fun <reified T> T.toJsonWithoutQuotesSurround(): String {
  return defaultJsonSerializer.encodeToString(this).trim('\"')
}

inline fun <reified T> T.toJsonQuotesSurround(): String {
  return "\"" + defaultJsonSerializer.encodeToString(this) + "\""
}

inline fun <reified T> String.toObj(): T? {
  return try {
    defaultJsonSerializer.decodeFromString(this)
  } catch (e: Exception) {
    e.printStackTrace()
//    debugLog("catch exception, current text: $this")
    null
  }
}

fun String.toObj(kType: KType): Any? {
  return try {
    defaultJsonSerializer.decodeFromString(serializer(kType), this)
  } catch (e: Exception) {
    e.printStackTrace()
//    debugLog("catch exception, current text: $this")
    null
  }
}
