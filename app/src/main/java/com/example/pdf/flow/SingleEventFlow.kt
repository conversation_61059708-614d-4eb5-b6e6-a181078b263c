package com.example.pdf.flow

import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow

/**
 * 类似于SingleLiveEvent的Flow实现
 * 只会向活跃的观察者发送一次事件
 */
interface SingleEventFlow<T> {
  val flow: SharedFlow<T>

  suspend fun emit(value: T)

  companion object {
    fun <T> create(): SingleEventFlow<T> = SingleEventFlowImpl()
  }
}

/**
 * SingleEventFlow的默认实现
 */
private class SingleEventFlowImpl<T> : SingleEventFlow<T> {
  private val _flow = MutableSharedFlow<T>(replay = 0)
  override val flow: SharedFlow<T> = _flow.asSharedFlow()

  override suspend fun emit(value: T) {
    _flow.emit(value)
  }
}

/**
 * 创建SingleEventFlow的扩展函数
 */
fun <T> MutableSingleEventFlow(): SingleEventFlow<T> = SingleEventFlow.create()

/**
 * 创建带初始值的SingleEventFlow的扩展函数
 */
fun <T> MutableSingleEventFlow(value: T): SingleEventFlow<T> {
  val eventFlow = SingleEventFlow.create<T>()
  // 注意：这里不会立即发射初始值，需要在协程中调用
  return eventFlow
}