<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="#89E6FF"
    app:cardElevation="0dp">

    <FrameLayout
        android:id="@+id/native_ad"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/ll_ad_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|top"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="4dp"
            android:elevation="1px"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <androidx.cardview.widget.CardView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:cardCornerRadius="2dp"
                app:cardElevation="0dp"
                app:cardUseCompatPadding="false">

                <TextView
                    android:id="@+id/tvAd"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="#999"
                    android:paddingHorizontal="1.5dp"
                    android:paddingVertical="0.3dp"
                    android:text=" AD "
                    android:textAppearance="@style/TextAppearance.AppCompat.Body2"
                    android:textColor="@android:color/white"
                    android:textSize="9sp" />
            </androidx.cardview.widget.CardView>


            <TextView
                android:id="@+id/ad_advertiser"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp"
                android:textAppearance="@style/TextAppearance.AppCompat.Body1"
                android:textSize="10sp"
                android:visibility="gone"
                tools:text="Advertiser" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="12dp"
            android:layout_marginVertical="16dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <androidx.cardview.widget.CardView
                android:id="@+id/mcv_icon_image_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:cardCornerRadius="4dp"
                app:cardElevation="0dp"
                app:cardUseCompatPadding="false">

                <ImageView
                    android:id="@+id/ad_app_icon"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    app:layout_constraintDimensionRatio="H,1:1"
                    tools:src="@drawable/logo" />

            </androidx.cardview.widget.CardView>


            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="5dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/ad_headline"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:textAppearance="@style/TextAppearance.AppCompat.Title"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    tools:text="Title" />

                <TextView
                    android:id="@+id/ad_body"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:textAppearance="@style/TextAppearance.AppCompat.Body1"
                    android:textSize="10sp"
                    tools:text="BodyBodyBodyBodyBodyBodyBBodyBodBodyBodyBodyBodyBodyBodyBBodyBodBodyBodyBodyBodyBodyBodyBBodyBodBodyBodyBodyBodyBodyBodyBBodyBod" />
            </LinearLayout>


            <androidx.cardview.widget.CardView
                android:id="@+id/card_ad_call_to_action_container"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                app:cardCornerRadius="100dp"
                app:cardElevation="0dp">

                <Button
                    android:id="@+id/ad_call_to_action"
                    android:layout_width="match_parent"
                    android:layout_height="28dp"
                    android:background="#FFF"
                    android:elevation="0dp"
                    android:minWidth="80dp"
                    android:textColor="#000"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    tools:text="Click" />
            </androidx.cardview.widget.CardView>

        </LinearLayout>
    </FrameLayout>
</androidx.cardview.widget.CardView>