<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    tools:background="@color/white">

    <LinearLayout
        android:gravity="center_vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="4dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 标题 -->
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#000"
                android:gravity="bottom"
                android:textSize="13sp"
                android:textStyle="bold"
                tools:text="lalalalallalalallala" />

            <!-- 副标题 -->
            <TextView
                android:id="@+id/tv_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="top"
                android:textColor="#000"
                android:textSize="11sp"
                tools:text="lalalalalalalalalalallalalalalalalalalalalalalalalalalala" />
        </LinearLayout>

        <ImageView
            android:id="@+id/iv_img"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="center"
            tools:src="@drawable/img_fcm_service_message_0" />

    </LinearLayout>

    <TextView
        android:id="@+id/btn_action"
        android:layout_width="match_parent"
        android:layout_height="28dp"
        android:layout_gravity="center"
        android:layout_marginHorizontal="32dp"
        android:layout_marginTop="6dp"
        android:background="@drawable/bg_rounded_button"
        android:gravity="center"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        android:textStyle="bold"
        tools:text="Try now" />

</LinearLayout>