<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:minHeight="50dp"
    tools:background="@color/white">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginEnd="4dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="1dp"
            android:layout_weight="1"
            android:gravity="bottom"
            android:maxLines="2"
            android:textColor="#000"
            android:textSize="13sp"
            android:textStyle="bold"
            tools:ignore="NestedWeights"
            tools:text="lalalalallalalallala" />

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="top"
            android:maxLines="1"
            android:textColor="#000"
            android:textSize="11sp"
            tools:text="lalalalallalalallalalalalalallalalallalalalalalallalalallala" />
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_img"
        android:layout_width="44dp"
        android:layout_height="44dp" />

</LinearLayout>