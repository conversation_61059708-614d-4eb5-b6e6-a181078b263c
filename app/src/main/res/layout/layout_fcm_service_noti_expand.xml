<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp"
    tools:background="@color/white">

    <!-- 标题 -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="#000"
        android:textSize="15sp"
        android:textStyle="bold"
        tools:text="lalalalallalalallala" />

    <!-- 副标题 -->
    <TextView
        android:id="@+id/tv_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:gravity="center"
        android:textColor="#000"
        android:textSize="13sp"
        tools:text="lalalalallalalallalalalalalallalalallalalalalalallalalallalalalalalallalalallala" />

    <!-- 中间的图片 -->
    <ImageView
        android:id="@+id/iv_img"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:layout_gravity="center"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="8dp"
        tools:src="@drawable/img_fcm_service_message_0" />

    <!-- GO 按钮 -->
    <TextView
        android:id="@+id/btn_action"
        android:layout_width="match_parent"
        android:layout_marginHorizontal="32dp"
        android:layout_height="36dp"
        android:layout_gravity="center"
        android:background="@drawable/bg_rounded_button"
        android:gravity="center"
        android:textColor="@android:color/white"
        android:textSize="15sp"
        android:textStyle="bold"
        tools:text="Try now" />

</LinearLayout>