<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:background="@android:color/transparent"
    android:gravity="center_vertical"
    android:paddingTop="1dp">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/iv_home"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:src="@drawable/ic_fixed_noti_home" />

        <TextView
            android:id="@+id/tv_home"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="Home"
            android:textColor="#999"
            android:textSize="11sp"
            android:textStyle="bold" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/iv_bookmarks"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:src="@drawable/ic_fixed_noti_bookmarks" />

        <TextView
            android:id="@+id/tv_bookmarks"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="Bookmarks"
            android:textColor="#999"
            android:textSize="11sp"
            android:textStyle="bold" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/iv_scan"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:src="@drawable/ic_fixed_noti_scan" />

        <TextView
            android:id="@+id/tv_scan"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="Scan"
            android:textColor="#999"
            android:textSize="11sp"
            android:textStyle="bold" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/iv_tools"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:src="@drawable/ic_fixed_noti_convert" />

        <TextView
            android:id="@+id/tv_tools"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="Convert"
            android:textColor="#999"
            android:textSize="11sp"
            android:textStyle="bold" />

    </LinearLayout>


</LinearLayout>