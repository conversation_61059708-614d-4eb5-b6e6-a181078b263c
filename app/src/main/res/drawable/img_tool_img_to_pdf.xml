<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="54dp"
    android:height="54dp"
    android:viewportWidth="54"
    android:viewportHeight="54">
  <path
      android:pathData="M8,0L46,0A8,8 0,0 1,54 8L54,46A8,8 0,0 1,46 54L8,54A8,8 0,0 1,0 46L0,8A8,8 0,0 1,8 0z"
      android:strokeWidth="1"
      android:fillColor="#F4F4F4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M15,11L39,11A4,4 0,0 1,43 15L43,39A4,4 0,0 1,39 43L15,43A4,4 0,0 1,11 39L11,15A4,4 0,0 1,15 11z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="11"
          android:startY="11"
          android:endX="43"
          android:endY="41.154"
          android:type="linear">
        <item android:offset="0" android:color="#FF6246FF"/>
        <item android:offset="1" android:color="#FFAC9BFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M50.415,11L26.415,11A4,4 0,0 0,22.415 15L22.415,39A4,4 0,0 0,26.415 43L50.415,43A4,4 0,0 0,54.415 39L54.415,15A4,4 0,0 0,50.415 11z"/>
    <path
        android:pathData="M45.707,29.813L36.949,22.671C36.221,22.077 35.177,22.071 34.442,22.654L25.081,30.082L25.081,30.082L19.707,34.346"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#FFFFFF"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
  </group>
  <group>
    <clip-path
        android:pathData="M17.881,11L-6.119,11A4,4 0,0 0,-10.119 15L-10.119,39A4,4 0,0 0,-6.119 43L17.881,43A4,4 0,0 0,21.881 39L21.881,15A4,4 0,0 0,17.881 11z"/>
    <path
        android:pathData="M24.173,30.307L19.393,26.781C18.695,26.266 17.744,26.259 17.039,26.765L8.707,32.747L8.707,32.747"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#FFFFFF"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
  </group>
  <group>
    <clip-path
        android:pathData="M15,11L39,11A4,4 0,0 1,43 15L43,39A4,4 0,0 1,39 43L15,43A4,4 0,0 1,11 39L11,15A4,4 0,0 1,15 11z"/>
    <path
        android:pathData="M32,29L40,29A3,3 0,0 1,43 32L43,40A3,3 0,0 1,40 43L32,43A3,3 0,0 1,29 40L29,32A3,3 0,0 1,32 29z"
        android:strokeWidth="1"
        android:fillColor="#551CF5"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M15,11L39,11A4,4 0,0 1,43 15L43,39A4,4 0,0 1,39 43L15,43A4,4 0,0 1,11 39L11,15A4,4 0,0 1,15 11z"/>
    <path
        android:pathData="M36.76,37.567C36.247,37.7 35.737,37.861 35.23,38.038L34.724,38.221C34.015,39.482 33.338,40.143 32.711,40.143C32.552,40.143 32.4,40.107 32.318,40.041C32.032,39.91 31.857,39.621 31.857,39.309C31.857,38.782 32.382,38.25 34.03,37.57C34.417,36.863 34.745,36.123 35.03,35.321L35.178,34.885L35.108,34.74C34.671,33.813 34.459,32.967 34.703,32.406L34.763,32.291C34.887,32.062 35.108,31.912 35.357,31.87L35.466,31.858L35.577,31.86C35.844,31.86 36.109,31.992 36.27,32.209C36.577,32.634 36.566,33.445 36.252,34.58L36.197,34.77L36.325,34.995C36.594,35.448 36.918,35.866 37.289,36.243L37.466,36.415L37.662,36.381C38.008,36.322 38.313,36.287 38.606,36.275L38.824,36.271C39.708,36.289 40.128,36.644 40.143,37.119L40.141,37.215C40.141,37.738 39.841,38.008 39.356,38.087C39.242,38.106 39.146,38.113 39.004,38.115L38.877,38.115C38.305,38.073 37.765,37.877 37.315,37.544L37.208,37.459L36.76,37.567ZM35.787,36.17L35.705,36.376C35.596,36.639 35.484,36.893 35.369,37.14L35.321,37.235L35.435,37.197C35.689,37.114 35.95,37.037 36.207,36.969L36.4,36.922L36.361,36.881C36.162,36.664 35.976,36.437 35.813,36.208L35.787,36.17Z"
        android:strokeWidth="1"
        android:fillType="nonZero"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="36"
            android:startY="32.162"
            android:endX="36"
            android:endY="40.143"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#CDFFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M15,11L39,11A4,4 0,0 1,43 15L43,39A4,4 0,0 1,39 43L15,43A4,4 0,0 1,11 39L11,15A4,4 0,0 1,15 11z"/>
    <path
        android:pathData="M22.259,18.704m-2.963,0a2.963,2.963 0,1 1,5.926 0a2.963,2.963 0,1 1,-5.926 0"
        android:strokeWidth="1"
        android:fillType="nonZero"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="22.259"
            android:startY="15.95"
            android:endX="22.259"
            android:endY="21.676"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#CDFFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
