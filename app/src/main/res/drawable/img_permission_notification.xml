<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="47dp"
    android:height="40dp"
    android:viewportWidth="47"
    android:viewportHeight="40">
  <group>
    <clip-path
        android:pathData="M17.992,7.669C17.876,7.013 22.283,4.93 31.21,1.423L47.851,27.109L26.803,36.194C21.045,17.834 18.108,8.326 17.992,7.669Z"/>
    <path
        android:pathData="M20.569,7.096C27.062,5.951 33.463,9.539 35.873,15.676L40.484,27.418C41.247,29.36 40.291,31.553 38.349,32.315C38.115,32.407 37.872,32.476 37.624,32.52L13.238,36.82C11.184,37.182 9.224,35.81 8.862,33.755C8.818,33.507 8.799,33.256 8.806,33.004L9.123,20.393C9.289,13.801 14.076,8.24 20.569,7.096Z"
        android:strokeWidth="1"
        android:fillColor="#FFFFFF"
        android:fillType="nonZero"
        android:strokeColor="#00000000"/>
    <path
        android:pathData="M20.612,7.341C23.8,6.779 26.966,7.379 29.63,8.873C32.294,10.367 34.457,12.754 35.64,15.767L40.252,27.51C40.608,28.417 40.563,29.382 40.202,30.209C39.841,31.037 39.164,31.727 38.258,32.083C38.039,32.169 37.812,32.233 37.581,32.273L13.195,36.573C12.235,36.743 11.298,36.507 10.559,35.989C9.819,35.471 9.277,34.671 9.108,33.712C9.067,33.48 9.05,33.245 9.055,33.011L9.373,20.399C9.454,17.163 10.67,14.18 12.663,11.865C14.655,9.55 17.424,7.903 20.612,7.341Z"
        android:strokeAlpha="0.20214161"
        android:strokeWidth="0.5"
        android:fillType="evenOdd"
        android:strokeColor="#F3502C">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="24.776"
            android:startY="36.628"
            android:endX="24.776"
            android:endY="7.177"
            android:type="linear">
          <item android:offset="0" android:color="#00F14D27"/>
          <item android:offset="1" android:color="#33F24C26"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M22.939,3.256L22.965,3.261C25.012,3.622 26.38,5.575 26.019,7.622L26.019,7.622L26.019,7.622L18.578,6.31C18.939,4.263 20.892,2.895 22.939,3.256Z"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M22.939,3.256L22.965,3.261C25.012,3.622 26.38,5.575 26.019,7.622L26.019,7.622L26.019,7.622L18.578,6.31C18.939,4.263 20.892,2.895 22.939,3.256Z"
      android:strokeWidth="1"
      android:fillColor="#F24C26"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M13.79,32.105L21.696,33.5L21.657,33.718C21.295,35.773 19.336,37.145 17.28,36.783L16.816,36.701C14.76,36.338 13.389,34.379 13.751,32.324L13.79,32.105L13.79,32.105Z"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M13.79,32.105L21.696,33.5L21.657,33.718C21.295,35.773 19.336,37.145 17.28,36.783L16.816,36.701C14.76,36.338 13.389,34.379 13.751,32.324L13.79,32.105L13.79,32.105Z"
      android:strokeWidth="1"
      android:fillColor="#F24C26"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M22.452,6.096C28.944,7.241 33.732,12.803 33.897,19.394L34.215,32.006C34.268,34.092 32.619,35.825 30.533,35.877C30.282,35.884 30.031,35.865 29.782,35.821L5.397,31.521C3.342,31.159 1.97,29.2 2.332,27.146C2.376,26.897 2.444,26.654 2.536,26.42L7.148,14.677C9.557,8.541 15.959,4.952 22.452,6.096Z"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M22.452,6.096C28.944,7.241 33.732,12.803 33.897,19.394L34.215,32.006C34.268,34.092 32.619,35.825 30.533,35.877C30.282,35.884 30.031,35.865 29.782,35.821L5.397,31.521C3.342,31.159 1.97,29.2 2.332,27.146C2.376,26.897 2.444,26.654 2.536,26.42L7.148,14.677C9.557,8.541 15.959,4.952 22.452,6.096Z"
      android:strokeWidth="1.5"
      android:strokeColor="#F3502C"
      android:fillType="nonZero">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="18.245"
          android:startY="35.878"
          android:endX="18.245"
          android:endY="5.93"
          android:type="linear">
        <item android:offset="0" android:color="#00F14D27"/>
        <item android:offset="1" android:color="#33F24C26"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M16.642,17.37L24.083,18.682"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#F24C26"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M13.534,23.482L24.804,25.469"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#F24C26"
      android:fillType="evenOdd"/>
</vector>
