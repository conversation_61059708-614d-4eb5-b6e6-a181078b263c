<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="54dp"
    android:height="54dp"
    android:viewportWidth="54"
    android:viewportHeight="54">
  <path
      android:pathData="M8,0L46,0A8,8 0,0 1,54 8L54,46A8,8 0,0 1,46 54L8,54A8,8 0,0 1,0 46L0,8A8,8 0,0 1,8 0z"
      android:strokeWidth="1"
      android:fillColor="#F4F4F4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M14,11L25.731,11C26.542,11 27.319,11.328 27.884,11.91L30,14.09C30.565,14.672 31.341,15 32.152,15L40,15C42.209,15 44,16.791 44,19L44,39C44,41.209 42.209,43 40,43L14,43C11.791,43 10,41.209 10,39L10,15C10,12.791 11.791,11 14,11Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="10"
          android:startY="12.827"
          android:endX="44"
          android:endY="39.538"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFC032"/>
        <item android:offset="1" android:color="#FFFFE073"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M14,11L25.731,11C26.542,11 27.319,11.328 27.884,11.91L30,14.09C30.565,14.672 31.341,15 32.152,15L40,15C42.209,15 44,16.791 44,19L44,39C44,41.209 42.209,43 40,43L14,43C11.791,43 10,41.209 10,39L10,15C10,12.791 11.791,11 14,11Z"/>
    <path
        android:pathData="M33,29L41,29A3,3 0,0 1,44 32L44,40A3,3 0,0 1,41 43L33,43A3,3 0,0 1,30 40L30,32A3,3 0,0 1,33 29z"
        android:strokeWidth="1"
        android:fillColor="#F69906"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
    <path
        android:pathData="M39.5,32.5C37.843,32.5 36.5,33.843 36.5,35.5"
        android:strokeLineJoin="round"
        android:strokeWidth="1.3"
        android:fillColor="#00000000"
        android:strokeColor="#FFFFFF"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M37.862,35.211l-1.658,1.118l-1.118,-1.658"
        android:strokeLineJoin="round"
        android:strokeWidth="1.3"
        android:fillColor="#00000000"
        android:strokeColor="#FFFFFF"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M34.5,39L38.5,39"
        android:strokeLineJoin="round"
        android:strokeWidth="1.3"
        android:fillColor="#00000000"
        android:strokeColor="#FFFFFF"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
  </group>
  <group>
    <clip-path
        android:pathData="M14,11L25.731,11C26.542,11 27.319,11.328 27.884,11.91L30,14.09C30.565,14.672 31.341,15 32.152,15L40,15C42.209,15 44,16.791 44,19L44,39C44,41.209 42.209,43 40,43L14,43C11.791,43 10,41.209 10,39L10,15C10,12.791 11.791,11 14,11Z"/>
    <path
        android:pathData="M16,23.5L38,23.5"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#FFFFFF"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
  </group>
</vector>
