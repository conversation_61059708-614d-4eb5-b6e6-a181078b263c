<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="54dp"
    android:height="54dp"
    android:viewportWidth="54"
    android:viewportHeight="54">
  <path
      android:pathData="M8,0L46,0A8,8 0,0 1,54 8L54,46A8,8 0,0 1,46 54L8,54A8,8 0,0 1,0 46L0,8A8,8 0,0 1,8 0z"
      android:strokeWidth="1"
      android:fillColor="#F4F4F4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M16,10L38,10A4,4 0,0 1,42 14L42,40A4,4 0,0 1,38 44L16,44A4,4 0,0 1,12 40L12,14A4,4 0,0 1,16 10z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="15.322"
          android:startY="10"
          android:endX="38.678"
          android:endY="42.038"
          android:type="linear">
        <item android:offset="0" android:color="#FF2F7CFF"/>
        <item android:offset="1" android:color="#FF9BC8FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M16,10L38,10A4,4 0,0 1,42 14L42,40A4,4 0,0 1,38 44L16,44A4,4 0,0 1,12 40L12,14A4,4 0,0 1,16 10z"/>
    <path
        android:pathData="M21.086,15L21.496,15C21.894,15 22.242,15.268 22.343,15.653L23.851,21.399C23.855,21.414 23.858,21.43 23.862,21.445L24.088,22.545C24.095,22.58 24.125,22.605 24.161,22.605C24.196,22.605 24.228,22.581 24.237,22.546L24.535,21.44C24.539,21.428 24.542,21.416 24.546,21.404L25.911,17.086C26.026,16.722 26.364,16.475 26.745,16.475L27.378,16.475C27.759,16.475 28.096,16.721 28.212,17.084L29.589,21.399C29.594,21.414 29.599,21.429 29.603,21.445L29.885,22.531C29.894,22.565 29.925,22.589 29.961,22.589C29.996,22.589 30.027,22.564 30.033,22.53L30.245,21.443C30.248,21.429 30.251,21.415 30.255,21.401L31.665,15.666C31.761,15.275 32.112,15 32.515,15L32.914,15C33.377,15 33.752,15.375 33.752,15.838C33.752,15.909 33.743,15.981 33.725,16.05L31.286,25.347C31.185,25.732 30.838,26 30.44,26L29.779,26C29.399,26 29.062,25.754 28.946,25.392L27.448,20.722C27.443,20.707 27.438,20.692 27.434,20.677L27.137,19.545C27.128,19.51 27.097,19.486 27.061,19.486C27.025,19.486 26.994,19.51 26.985,19.545L26.687,20.677C26.683,20.692 26.679,20.707 26.674,20.722L25.175,25.392C25.059,25.754 24.723,26 24.342,26L23.674,26C23.28,26 22.934,25.736 22.83,25.356L20.286,16.047C20.166,15.606 20.426,15.15 20.867,15.029C20.938,15.01 21.012,15 21.086,15Z"
        android:strokeWidth="1"
        android:fillColor="#FFFFFF"
        android:fillType="nonZero"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M16,10L38,10A4,4 0,0 1,42 14L42,40A4,4 0,0 1,38 44L16,44A4,4 0,0 1,12 40L12,14A4,4 0,0 1,16 10z"/>
    <path
        android:pathData="M17.5,31.5L24.885,31.5"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#FFFFFF"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
  </group>
  <group>
    <clip-path
        android:pathData="M16,10L38,10A4,4 0,0 1,42 14L42,40A4,4 0,0 1,38 44L16,44A4,4 0,0 1,12 40L12,14A4,4 0,0 1,16 10z"/>
    <path
        android:pathData="M17.5,36.5L29.5,36.5"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#FFFFFF"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
  </group>
  <group>
    <clip-path
        android:pathData="M16,10L38,10A4,4 0,0 1,42 14L42,40A4,4 0,0 1,38 44L16,44A4,4 0,0 1,12 40L12,14A4,4 0,0 1,16 10z"/>
    <path
        android:pathData="M31,30L39,30A3,3 0,0 1,42 33L42,41A3,3 0,0 1,39 44L31,44A3,3 0,0 1,28 41L28,33A3,3 0,0 1,31 30z"
        android:strokeWidth="1"
        android:fillColor="#1467E6"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
    <path
        android:pathData="M35.76,38.567C35.247,38.7 34.737,38.861 34.23,39.038L33.724,39.221C33.015,40.482 32.338,41.143 31.711,41.143C31.552,41.143 31.4,41.107 31.318,41.041C31.032,40.91 30.857,40.621 30.857,40.309C30.857,39.782 31.382,39.25 33.03,38.57C33.417,37.863 33.745,37.123 34.03,36.321L34.178,35.885L34.108,35.74C33.671,34.813 33.459,33.967 33.703,33.406L33.763,33.291C33.887,33.062 34.108,32.912 34.357,32.87L34.466,32.858L34.577,32.86C34.844,32.86 35.109,32.992 35.27,33.209C35.577,33.634 35.566,34.445 35.252,35.58L35.197,35.77L35.325,35.995C35.594,36.448 35.918,36.866 36.289,37.243L36.466,37.415L36.662,37.381C37.008,37.322 37.313,37.287 37.606,37.275L37.824,37.271C38.708,37.289 39.128,37.644 39.143,38.119L39.141,38.215C39.141,38.738 38.841,39.008 38.356,39.087C38.242,39.106 38.146,39.113 38.004,39.115L37.877,39.115C37.305,39.073 36.765,38.877 36.315,38.544L36.208,38.459L35.76,38.567ZM34.787,37.17L34.705,37.376C34.596,37.639 34.484,37.893 34.369,38.14L34.321,38.235L34.435,38.197C34.689,38.114 34.95,38.037 35.207,37.969L35.4,37.922L35.361,37.881C35.162,37.664 34.976,37.437 34.813,37.208L34.787,37.17Z"
        android:strokeWidth="1"
        android:fillType="nonZero"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="35"
            android:startY="33.162"
            android:endX="35"
            android:endY="41.143"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#CDFFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
