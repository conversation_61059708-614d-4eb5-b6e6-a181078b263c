<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="29dp"
    android:height="28dp"
    android:viewportWidth="29"
    android:viewportHeight="28">
  <path
      android:pathData="M14.429,14m-9.25,0a9.25,9.25 0,1 1,18.5 0a9.25,9.25 0,1 1,-18.5 0"
      android:strokeWidth="1.5"
      android:strokeColor="#F24C26"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="14.429"
          android:startY="23.25"
          android:endX="14.429"
          android:endY="4.749"
          android:type="linear">
        <item android:offset="0" android:color="#00F14D27"/>
        <item android:offset="1" android:color="#33F24C26"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M15.398,15.715l-1.969,-0.465l1,-5.25"
      android:strokeLineJoin="round"
      android:strokeWidth="1.75"
      android:fillColor="#00000000"
      android:strokeColor="#F24C26"
      android:fillType="evenOdd"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M18.036,15.363L18.036,15.363A0.857,0.857 0,0 1,18.893 16.22L18.893,16.22A0.857,0.857 0,0 1,18.036 17.077L18.036,17.077A0.857,0.857 0,0 1,17.179 16.22L17.179,16.22A0.857,0.857 0,0 1,18.036 15.363z"
      android:strokeWidth="1"
      android:fillColor="#F24C26"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
</vector>
