<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="40dp"
    android:height="40dp"
    android:viewportWidth="40"
    android:viewportHeight="40">
  <group>
    <clip-path
        android:pathData="M9,2.5L26.625,2.5C27.493,2.5 28.334,2.801 29.005,3.352L33.63,7.152C34.497,7.864 35,8.927 35,10.049L35,33.5C35,35.709 33.209,37.5 31,37.5L9,37.5C6.791,37.5 5,35.709 5,33.5L5,6.5C5,4.291 6.791,2.5 9,2.5Z"/>
    <path
        android:pathData="M30.25,-1.75L54.25,-1.75A2.5,2.5 0,0 1,56.75 0.75L56.75,7.25A2.5,2.5 0,0 1,54.25 9.75L30.25,9.75A2.5,2.5 0,0 1,27.75 7.25L27.75,0.75A2.5,2.5 0,0 1,30.25 -1.75z"
        android:strokeWidth="1"
        android:fillColor="#00000000"
        android:strokeColor="#24D171"
        android:fillType="evenOdd"/>
  </group>
  <path
      android:pathData="M9,2.5L26.625,2.5C27.493,2.5 28.334,2.801 29.005,3.352L33.63,7.152C34.497,7.864 35,8.927 35,10.049L35,33.5C35,35.709 33.209,37.5 31,37.5L9,37.5C6.791,37.5 5,35.709 5,33.5L5,6.5C5,4.291 6.791,2.5 9,2.5Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#24D171">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="20"
          android:startY="37.5"
          android:endX="20"
          android:endY="2.553"
          android:type="linear">
        <item android:offset="0" android:color="#0027F185"/>
        <item android:offset="1" android:color="#3326F271"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M15.864,9.901L18.856,14.252C18.882,14.29 18.882,14.34 18.856,14.378L15.618,19.098C15.438,19.361 15.505,19.719 15.768,19.899C15.863,19.965 15.977,20 16.093,20L16.591,20C16.963,20 17.31,19.814 17.515,19.504L19.908,15.888C19.941,15.836 20.01,15.823 20.061,15.856C20.074,15.864 20.084,15.875 20.092,15.888L22.485,19.504C22.69,19.814 23.037,20 23.409,20L23.907,20C24.223,20 24.48,19.743 24.48,19.427C24.48,19.31 24.445,19.196 24.378,19.101L21.114,14.378C21.088,14.34 21.088,14.29 21.114,14.252L24.132,9.898C24.313,9.639 24.248,9.282 23.988,9.102C23.892,9.036 23.779,9 23.662,9L23.164,9C22.792,9 22.445,9.186 22.24,9.496L20.092,12.743C20.059,12.794 19.99,12.808 19.939,12.774C19.926,12.766 19.916,12.755 19.908,12.743L17.76,9.496C17.555,9.186 17.208,9 16.836,9L16.338,9C16.02,9 15.763,9.257 15.763,9.575C15.763,9.691 15.798,9.805 15.864,9.901Z"
      android:strokeWidth="1"
      android:fillColor="#24D171"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M11,26L22,26"
      android:strokeAlpha="0.1999628"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#24D171"
      android:fillType="evenOdd"
      android:fillAlpha="0.1999628"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M11,30L29,30"
      android:strokeAlpha="0.1999628"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#24D171"
      android:fillType="evenOdd"
      android:fillAlpha="0.1999628"
      android:strokeLineCap="round"/>
</vector>
