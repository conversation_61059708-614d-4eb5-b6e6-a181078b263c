<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="28dp"
    android:height="28dp"
    android:viewportWidth="28"
    android:viewportHeight="28">
  <path
      android:pathData="M14,4.059C14.216,4.059 14.432,4.115 14.625,4.227L22.151,8.572C22.538,8.795 22.776,9.208 22.776,9.655L22.776,18.345C22.776,18.569 22.717,18.783 22.609,18.97C22.501,19.157 22.345,19.316 22.151,19.428L14.625,23.773C14.432,23.885 14.216,23.941 14,23.941C13.784,23.941 13.568,23.885 13.375,23.773L5.849,19.428C5.462,19.205 5.224,18.792 5.224,18.345L5.224,9.655C5.224,9.208 5.462,8.795 5.849,8.572L13.375,4.227C13.568,4.115 13.784,4.059 14,4.059Z"
      android:strokeWidth="1.5"
      android:strokeColor="#F24C26"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="14"
          android:startY="23.941"
          android:endX="14"
          android:endY="4.089"
          android:type="linear">
        <item android:offset="0" android:color="#00F14D27"/>
        <item android:offset="1" android:color="#33F24C26"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M16.864,12.846l-2.864,1.654l-5.196,-3"
      android:strokeLineJoin="round"
      android:strokeWidth="1.75"
      android:fillColor="#00000000"
      android:strokeColor="#F24C26"
      android:fillType="evenOdd"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M19.239,10.622L19.239,10.622A0.857,0.857 0,0 1,20.096 11.479L20.096,11.479A0.857,0.857 0,0 1,19.239 12.336L19.239,12.336A0.857,0.857 0,0 1,18.382 11.479L18.382,11.479A0.857,0.857 0,0 1,19.239 10.622z"
      android:strokeWidth="1"
      android:fillColor="#F24C26"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
</vector>
