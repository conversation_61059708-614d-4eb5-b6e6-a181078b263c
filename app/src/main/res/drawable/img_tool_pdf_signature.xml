<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="54dp"
    android:height="54dp"
    android:viewportWidth="54"
    android:viewportHeight="54">
  <path
      android:pathData="M8,0L46,0A8,8 0,0 1,54 8L54,46A8,8 0,0 1,46 54L8,54A8,8 0,0 1,0 46L0,8A8,8 0,0 1,8 0z"
      android:strokeWidth="1"
      android:fillColor="#F4F4F4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M16,10L38,10A4,4 0,0 1,42 14L42,40A4,4 0,0 1,38 44L16,44A4,4 0,0 1,12 40L12,14A4,4 0,0 1,16 10z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="15.322"
          android:startY="10"
          android:endX="38.678"
          android:endY="42.038"
          android:type="linear">
        <item android:offset="0" android:color="#FF2F7CFF"/>
        <item android:offset="1" android:color="#FF9BC8FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M15.054,35.704C21.235,31.991 29.578,21.196 25.176,18.181C20.775,15.166 17.216,40.076 21.901,37.89C26.585,35.704 24.805,37.768 26,38.286C27.195,38.803 27.766,37.291 29.046,37.078C29.899,36.936 30.568,37.413 31.054,38.507"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#FFFFFF"
      android:fillType="evenOdd"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M46.587,18.486C46.562,18.69 46.53,18.893 46.491,19.094C46.068,21.307 44.854,23.418 42.829,25.417L44.094,26.474C43.429,28.4 42.381,29.901 40.96,30.984C39.631,31.996 37.973,32.645 35.979,32.918C34.876,33.095 33.964,33.681 33.233,34.661C32.451,35.709 31.876,37.192 31.49,39.099L30.504,38.935C31.371,32.34 33.089,27.356 35.629,23.969C38.224,20.509 41.674,18.687 45.978,18.5C46.183,18.492 46.386,18.487 46.587,18.486Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1"
      android:fillColor="#1467E6"
      android:strokeColor="#1467E6"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M32.407,37.549C33.593,32.221 34.912,27 39.574,23.412C37.696,25.454 36.312,27.39 34.636,33.984C34.335,34.195 34.151,34.38 33.776,34.791C33.342,35.448 32.832,36.695 32.407,37.549Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="35.991"
          android:startY="23.93"
          android:endX="35.61"
          android:endY="35.306"
          android:type="linear">
        <item android:offset="0" android:color="#FF6EA8FF"/>
        <item android:offset="1" android:color="#FF80B5FF"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
