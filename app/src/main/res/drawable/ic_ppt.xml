<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="40dp"
    android:height="40dp"
    android:viewportWidth="40"
    android:viewportHeight="40">
  <path
      android:pathData="M9,2.5L26.625,2.5C27.493,2.5 28.334,2.801 29.005,3.352L33.63,7.152C34.497,7.864 35,8.927 35,10.049L35,33.5C35,35.709 33.209,37.5 31,37.5L9,37.5C6.791,37.5 5,35.709 5,33.5L5,6.5C5,4.291 6.791,2.5 9,2.5Z"
      android:strokeWidth="1.25"
      android:fillColor="#00000000"
      android:fillType="nonZero"
      android:strokeColor="#E6F0FF"/>
  <group>
    <clip-path
        android:pathData="M9,2.5L26.625,2.5C27.493,2.5 28.334,2.801 29.005,3.352L33.63,7.152C34.497,7.864 35,8.927 35,10.049L35,33.5C35,35.709 33.209,37.5 31,37.5L9,37.5C6.791,37.5 5,35.709 5,33.5L5,6.5C5,4.291 6.791,2.5 9,2.5Z"/>
    <path
        android:pathData="M30.25,-1.75L54.25,-1.75A2.5,2.5 0,0 1,56.75 0.75L56.75,7.25A2.5,2.5 0,0 1,54.25 9.75L30.25,9.75A2.5,2.5 0,0 1,27.75 7.25L27.75,0.75A2.5,2.5 0,0 1,30.25 -1.75z"
        android:strokeWidth="1"
        android:fillColor="#00000000"
        android:strokeColor="#FF7E00"
        android:fillType="evenOdd"/>
  </group>
  <path
      android:pathData="M9,2.5L26.625,2.5C27.493,2.5 28.334,2.801 29.005,3.352L33.63,7.152C34.497,7.864 35,8.927 35,10.049L35,33.5C35,35.709 33.209,37.5 31,37.5L9,37.5C6.791,37.5 5,35.709 5,33.5L5,6.5C5,4.291 6.791,2.5 9,2.5Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#FF7E00">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="20"
          android:startY="37.5"
          android:endX="20"
          android:endY="2.553"
          android:type="linear">
        <item android:offset="0" android:color="#00F17D27"/>
        <item android:offset="1" android:color="#33F28A26"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M16,10.129L16,19.051C16,19.575 16.425,20 16.949,20C17.473,20 17.897,19.575 17.897,19.051L17.897,15.892C17.897,15.829 17.948,15.779 18.01,15.779L20.768,15.779L20.768,15.779C23.589,15.779 25,14.639 25,12.374C25,10.125 23.589,9 20.8,9L17.129,9C16.505,9 16,9.505 16,10.129ZM17.897,10.541L20.654,10.541C21.481,10.541 22.097,10.679 22.486,10.972C22.876,11.249 23.086,11.711 23.086,12.374C23.086,13.036 22.892,13.514 22.503,13.807C22.114,14.084 21.497,14.238 20.654,14.238L17.897,14.238L17.897,10.541Z"
      android:strokeWidth="1"
      android:fillColor="#FF7E00"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M11,26L22,26"
      android:strokeAlpha="0.1999628"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#FF7E00"
      android:fillType="evenOdd"
      android:fillAlpha="0.1999628"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M11,30L29,30"
      android:strokeAlpha="0.1999628"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#FF7E00"
      android:fillType="evenOdd"
      android:fillAlpha="0.1999628"
      android:strokeLineCap="round"/>
</vector>
