<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="54dp"
    android:height="54dp"
    android:viewportWidth="54"
    android:viewportHeight="54">
  <path
      android:pathData="M8,0L46,0A8,8 0,0 1,54 8L54,46A8,8 0,0 1,46 54L8,54A8,8 0,0 1,0 46L0,8A8,8 0,0 1,8 0z"
      android:strokeWidth="1"
      android:fillColor="#F4F4F4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M16,10L38,10A4,4 0,0 1,42 14L42,40A4,4 0,0 1,38 44L16,44A4,4 0,0 1,12 40L12,14A4,4 0,0 1,16 10z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="15.322"
          android:startY="10"
          android:endX="38.678"
          android:endY="42.038"
          android:type="linear">
        <item android:offset="0" android:color="#FFF24C26"/>
        <item android:offset="1" android:color="#FFFFA38F"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M16,10L38,10A4,4 0,0 1,42 14L42,40A4,4 0,0 1,38 44L16,44A4,4 0,0 1,12 40L12,14A4,4 0,0 1,16 10z"/>
    <path
        android:pathData="M28.1,22.27C27.357,22.462 26.618,22.694 25.884,22.952L25.152,23.216C24.125,25.043 23.145,26 22.236,26C22.006,26 21.787,25.948 21.668,25.852C21.253,25.663 21,25.244 21,24.793C21,24.029 21.761,23.258 24.146,22.273C24.708,21.25 25.182,20.178 25.596,19.016L25.809,18.385L25.708,18.175C25.075,16.832 24.768,15.607 25.122,14.795L25.208,14.627C25.388,14.296 25.707,14.079 26.069,14.017L26.226,14L26.387,14.003C26.774,14.003 27.157,14.195 27.391,14.508C27.836,15.125 27.82,16.3 27.365,17.943L27.285,18.218L27.47,18.544C27.86,19.2 28.33,19.806 28.867,20.352L29.123,20.601L29.406,20.551C29.907,20.466 30.349,20.415 30.773,20.398L31.089,20.391C32.37,20.419 32.977,20.932 33,21.62L32.997,21.76C32.997,22.517 32.563,22.908 31.86,23.023C31.696,23.05 31.556,23.06 31.35,23.063L31.166,23.063C30.337,23.002 29.556,22.718 28.904,22.236L28.749,22.113L28.1,22.27ZM26.691,20.246L26.572,20.544C26.414,20.924 26.252,21.294 26.086,21.65L26.017,21.788L26.182,21.733C26.549,21.612 26.927,21.501 27.3,21.403L27.579,21.335L27.522,21.276C27.234,20.962 26.965,20.632 26.728,20.3L26.691,20.246Z"
        android:strokeWidth="1"
        android:fillColor="#FFFFFF"
        android:fillType="nonZero"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M16,10L38,10A4,4 0,0 1,42 14L42,40A4,4 0,0 1,38 44L16,44A4,4 0,0 1,12 40L12,14A4,4 0,0 1,16 10z"/>
    <path
        android:pathData="M17.5,31.5L24.885,31.5"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#FFFFFF"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
  </group>
  <group>
    <clip-path
        android:pathData="M16,10L38,10A4,4 0,0 1,42 14L42,40A4,4 0,0 1,38 44L16,44A4,4 0,0 1,12 40L12,14A4,4 0,0 1,16 10z"/>
    <path
        android:pathData="M17.5,36.5L29.5,36.5"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#FFFFFF"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
  </group>
  <path
      android:pathData="M31.8,34L40.8,34A2,2 0,0 1,42.8 36L42.8,42A2,2 0,0 1,40.8 44L31.8,44A2,2 0,0 1,29.8 42L29.8,36A2,2 0,0 1,31.8 34z"
      android:strokeWidth="1"
      android:fillColor="#CE2F0A"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M38.96,37.2C39.2,37.44 39.2,37.828 38.96,38.067L36.292,40.736C36.053,40.975 35.664,40.975 35.425,40.736L34.091,39.401C33.851,39.162 33.851,38.774 34.091,38.534C34.33,38.295 34.718,38.295 34.958,38.534L35.858,39.435L38.093,37.2C38.311,36.982 38.651,36.963 38.892,37.141L38.96,37.2Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="36.526"
          android:startY="37.163"
          android:endX="36.526"
          android:endY="40.915"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#CDFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M39,34.563L39,31.938C39,30.005 40.567,28.438 42.5,28.438C43.746,28.438 44.841,29.089 45.461,30.07C45.802,30.611 46,31.251 46,31.938"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#CE2F0A"
      android:fillType="evenOdd"
      android:strokeLineCap="round"/>
</vector>
