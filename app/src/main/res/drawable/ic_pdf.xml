<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="40dp"
    android:height="40dp"
    android:viewportWidth="40"
    android:viewportHeight="40">
  <group>
    <clip-path
        android:pathData="M9,2.5L26.625,2.5C27.493,2.5 28.334,2.801 29.005,3.352L33.63,7.152C34.497,7.864 35,8.927 35,10.049L35,33.5C35,35.709 33.209,37.5 31,37.5L9,37.5C6.791,37.5 5,35.709 5,33.5L5,6.5C5,4.291 6.791,2.5 9,2.5Z"/>
    <path
        android:pathData="M30.25,-1.75L54.25,-1.75A2.5,2.5 0,0 1,56.75 0.75L56.75,7.25A2.5,2.5 0,0 1,54.25 9.75L30.25,9.75A2.5,2.5 0,0 1,27.75 7.25L27.75,0.75A2.5,2.5 0,0 1,30.25 -1.75z"
        android:strokeWidth="1"
        android:fillColor="#00000000"
        android:strokeColor="#F3502C"
        android:fillType="evenOdd"/>
  </group>
  <path
      android:pathData="M9,2.5L26.625,2.5C27.493,2.5 28.334,2.801 29.005,3.352L33.63,7.152C34.497,7.864 35,8.927 35,10.049L35,33.5C35,35.709 33.209,37.5 31,37.5L9,37.5C6.791,37.5 5,35.709 5,33.5L5,6.5C5,4.291 6.791,2.5 9,2.5Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#F3502C">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="20"
          android:startY="37.5"
          android:endX="20"
          android:endY="2.553"
          android:type="linear">
        <item android:offset="0" android:color="#00F14D27"/>
        <item android:offset="1" android:color="#33F24C26"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M21.1,16.77C20.357,16.962 19.618,17.194 18.884,17.452L18.152,17.716C17.125,19.543 16.145,20.5 15.236,20.5C15.006,20.5 14.787,20.448 14.668,20.352C14.253,20.163 14,19.744 14,19.293C14,18.529 14.761,17.758 17.146,16.773C17.708,15.75 18.182,14.678 18.596,13.516L18.809,12.885L18.708,12.675C18.075,11.332 17.768,10.107 18.122,9.295L18.208,9.127C18.388,8.796 18.707,8.579 19.069,8.517L19.226,8.5L19.387,8.503C19.774,8.503 20.157,8.695 20.391,9.008C20.836,9.625 20.82,10.8 20.365,12.443L20.285,12.718L20.47,13.044C20.86,13.7 21.33,14.306 21.867,14.852L22.123,15.101L22.406,15.051C22.907,14.966 23.349,14.915 23.773,14.898L24.089,14.891C25.37,14.919 25.977,15.432 26,16.12L25.997,16.26C25.997,17.017 25.563,17.408 24.86,17.523C24.696,17.55 24.556,17.56 24.35,17.563L24.166,17.563C23.337,17.502 22.556,17.218 21.904,16.736L21.749,16.613L21.1,16.77ZM19.691,14.746L19.572,15.044C19.414,15.424 19.252,15.794 19.086,16.15L19.017,16.288L19.182,16.233C19.549,16.112 19.927,16.001 20.3,15.903L20.579,15.835L20.522,15.776C20.234,15.462 19.965,15.132 19.728,14.8L19.691,14.746Z"
      android:strokeWidth="1"
      android:fillColor="#F3502C"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M11,26L22,26"
      android:strokeAlpha="0.1999628"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#F24C26"
      android:fillType="evenOdd"
      android:fillAlpha="0.1999628"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M11,30L29,30"
      android:strokeAlpha="0.1999628"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#F24C26"
      android:fillType="evenOdd"
      android:fillAlpha="0.1999628"
      android:strokeLineCap="round"/>
</vector>
