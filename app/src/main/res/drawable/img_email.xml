<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="64dp"
    android:height="64dp"
    android:viewportWidth="64"
    android:viewportHeight="64">
  <path
      android:pathData="M9.6,22.4L54.4,22.4A3.2,3.2 0,0 1,57.6 25.6L57.6,43.2A3.2,3.2 0,0 1,54.4 46.4L9.6,46.4A3.2,3.2 0,0 1,6.4 43.2L6.4,25.6A3.2,3.2 0,0 1,9.6 22.4z"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="nonZero"
      android:strokeColor="#00000000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M9.6,22.4L54.4,22.4A3.2,3.2 0,0 1,57.6 25.6L57.6,43.2A3.2,3.2 0,0 1,54.4 46.4L9.6,46.4A3.2,3.2 0,0 1,6.4 43.2L6.4,25.6A3.2,3.2 0,0 1,9.6 22.4z"
      android:strokeWidth="2.4"
      android:strokeColor="#0CACFD"
      android:fillType="nonZero"
      android:strokeLineCap="round">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="32"
          android:startY="46.4"
          android:endX="32"
          android:endY="22.436"
          android:type="linear">
        <item android:offset="0" android:color="#0027ADF1"/>
        <item android:offset="1" android:color="#33269FF2"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M19.2,6.4L44.8,6.4A6.4,6.4 0,0 1,51.2 12.8L51.2,38.4A6.4,6.4 0,0 1,44.8 44.8L19.2,44.8A6.4,6.4 0,0 1,12.8 38.4L12.8,12.8A6.4,6.4 0,0 1,19.2 6.4z"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M19.2,6.4L44.8,6.4A6.4,6.4 0,0 1,51.2 12.8L51.2,38.4A6.4,6.4 0,0 1,44.8 44.8L19.2,44.8A6.4,6.4 0,0 1,12.8 38.4L12.8,12.8A6.4,6.4 0,0 1,19.2 6.4z"
      android:strokeWidth="2.4"
      android:strokeColor="#0CACFD"
      android:fillType="nonZero">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="32"
          android:startY="44.8"
          android:endX="32"
          android:endY="6.458"
          android:type="linear">
        <item android:offset="0" android:color="#0027ADF1"/>
        <item android:offset="1" android:color="#33269FF2"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24,14.4L34.353,14.4A1.6,1.6 0,0 1,35.953 16L35.953,16A1.6,1.6 0,0 1,34.353 17.6L24,17.6A1.6,1.6 0,0 1,22.4 16L22.4,16A1.6,1.6 0,0 1,24 14.4z"
      android:strokeWidth="1"
      android:fillColor="#0CACFD"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M24,22.4L40,22.4A1.6,1.6 0,0 1,41.6 24L41.6,24A1.6,1.6 0,0 1,40 25.6L24,25.6A1.6,1.6 0,0 1,22.4 24L22.4,24A1.6,1.6 0,0 1,24 22.4z"
      android:strokeWidth="1"
      android:fillColor="#0CACFD"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M57.6,27.2L57.6,51.2C57.6,54.735 54.735,57.6 51.2,57.6L12.8,57.6C9.265,57.6 6.4,54.735 6.4,51.2L6.4,27.2L7.521,27.2L30.446,39.937C31.412,40.474 32.588,40.474 33.554,39.937L56.48,27.2L57.6,27.2Z"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="nonZero"
      android:strokeColor="#00000000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M57.6,27.2L57.6,51.2C57.6,54.735 54.735,57.6 51.2,57.6L12.8,57.6C9.265,57.6 6.4,54.735 6.4,51.2L6.4,27.2L7.521,27.2L30.446,39.937C31.412,40.474 32.588,40.474 33.554,39.937L56.48,27.2L57.6,27.2Z"
      android:strokeWidth="2.4"
      android:strokeColor="#0CACFD"
      android:fillType="nonZero"
      android:strokeLineCap="round">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="32"
          android:startY="57.6"
          android:endX="32"
          android:endY="27.246"
          android:type="linear">
        <item android:offset="0" android:color="#0027ADF1"/>
        <item android:offset="1" android:color="#33269FF2"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M20,47.2L9.795,56.051"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="nonZero"
      android:strokeColor="#00000000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M20,47.2L9.795,56.051"
      android:strokeWidth="2.4"
      android:strokeColor="#0CACFD"
      android:fillType="nonZero"
      android:strokeLineCap="round">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="14.898"
          android:startY="56.051"
          android:endX="14.898"
          android:endY="47.213"
          android:type="linear">
        <item android:offset="0" android:color="#0027ADF1"/>
        <item android:offset="1" android:color="#33269FF2"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M45.6,47.2L55.12,56.051"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="nonZero"
      android:strokeColor="#00000000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M45.6,47.2L55.12,56.051"
      android:strokeWidth="2.4"
      android:strokeColor="#0CACFD"
      android:fillType="nonZero"
      android:strokeLineCap="round">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="50.36"
          android:startY="56.051"
          android:endX="50.36"
          android:endY="47.213"
          android:type="linear">
        <item android:offset="0" android:color="#0027ADF1"/>
        <item android:offset="1" android:color="#33269FF2"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
