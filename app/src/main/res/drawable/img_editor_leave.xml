<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="40dp"
    android:height="40dp"
    android:viewportWidth="40"
    android:viewportHeight="40">
  <path
      android:pathData="M8,6L19,6L30,6C32.209,6 34,7.791 34,10L34,21L34,32C34,34.209 32.209,36 30,36L8,36C5.791,36 4,34.209 4,32L4,10C4,7.791 5.791,6 8,6Z"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M8,6L19,6L30,6C32.209,6 34,7.791 34,10L34,21L34,32C34,34.209 32.209,36 30,36L8,36C5.791,36 4,34.209 4,32L4,10C4,7.791 5.791,6 8,6Z"
      android:strokeWidth="1.5"
      android:strokeColor="#F3502C"
      android:fillType="nonZero">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="19"
          android:startY="36"
          android:endX="19"
          android:endY="6.045"
          android:type="linear">
        <item android:offset="0" android:color="#00F14D27"/>
        <item android:offset="1" android:color="#33F24C26"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M11,6L27,6L27,13C27,14.105 26.105,15 25,15L13,15C11.895,15 11,14.105 11,13L11,6L11,6Z"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M11,6L27,6L27,13C27,14.105 26.105,15 25,15L13,15C11.895,15 11,14.105 11,13L11,6L11,6Z"
      android:strokeWidth="1.5"
      android:strokeColor="#F3502C"
      android:fillType="nonZero">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="19"
          android:startY="15"
          android:endX="19"
          android:endY="6.014"
          android:type="linear">
        <item android:offset="0" android:color="#00F14D27"/>
        <item android:offset="1" android:color="#33F24C26"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M11,24.5L27,24.5"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="nonZero"
      android:strokeColor="#00000000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M11,24.5L27,24.5"
      android:strokeWidth="1.5"
      android:strokeColor="#F3502C"
      android:fillType="nonZero"
      android:strokeLineCap="round">
  </path>
  <path
      android:pathData="M11,29.5L27,29.5"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="nonZero"
      android:strokeColor="#00000000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M11,29.5L27,29.5"
      android:strokeWidth="1.5"
      android:strokeColor="#F3502C"
      android:fillType="nonZero"
      android:strokeLineCap="round">
  </path>
  <path
      android:pathData="M31.5,8.5m-7.25,0a7.25,7.25 0,1 1,14.5 0a7.25,7.25 0,1 1,-14.5 0"
      android:strokeWidth="1.5"
      android:fillColor="#F24C26"
      android:fillType="nonZero"
      android:strokeColor="#FFFFFF"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M29,6L34,11"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#FFFFFF"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M34,6L29,11"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#FFFFFF"
      android:fillType="evenOdd"/>
</vector>
