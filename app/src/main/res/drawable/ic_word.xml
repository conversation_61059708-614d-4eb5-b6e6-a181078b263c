<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="40dp"
    android:height="40dp"
    android:viewportWidth="40"
    android:viewportHeight="40">
  <group>
    <clip-path
        android:pathData="M9,2.5L26.625,2.5C27.493,2.5 28.334,2.801 29.005,3.352L33.63,7.152C34.497,7.864 35,8.927 35,10.049L35,33.5C35,35.709 33.209,37.5 31,37.5L9,37.5C6.791,37.5 5,35.709 5,33.5L5,6.5C5,4.291 6.791,2.5 9,2.5Z"/>
    <path
        android:pathData="M30.25,-1.75L54.25,-1.75A2.5,2.5 0,0 1,56.75 0.75L56.75,7.25A2.5,2.5 0,0 1,54.25 9.75L30.25,9.75A2.5,2.5 0,0 1,27.75 7.25L27.75,0.75A2.5,2.5 0,0 1,30.25 -1.75z"
        android:strokeWidth="1"
        android:fillColor="#00000000"
        android:strokeColor="#0CACFD"
        android:fillType="evenOdd"/>
  </group>
  <path
      android:pathData="M9,2.5L26.625,2.5C27.493,2.5 28.334,2.801 29.005,3.352L33.63,7.152C34.497,7.864 35,8.927 35,10.049L35,33.5C35,35.709 33.209,37.5 31,37.5L9,37.5C6.791,37.5 5,35.709 5,33.5L5,6.5C5,4.291 6.791,2.5 9,2.5Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#0CACFD">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="20"
          android:startY="37.5"
          android:endX="20"
          android:endY="2.553"
          android:type="linear">
        <item android:offset="0" android:color="#0027ADF1"/>
        <item android:offset="1" android:color="#33269FF2"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M14.508,9.5L14.893,9.5C15.261,9.5 15.582,9.747 15.678,10.102L17.076,15.316C17.079,15.331 17.083,15.345 17.086,15.36L17.295,16.358C17.302,16.39 17.33,16.413 17.363,16.413C17.397,16.413 17.426,16.391 17.435,16.359L17.711,15.355C17.714,15.344 17.718,15.332 17.721,15.321L18.986,11.404C19.095,11.068 19.407,10.841 19.76,10.841L20.355,10.841C20.707,10.841 21.018,11.067 21.127,11.401L22.404,15.317C22.409,15.331 22.413,15.345 22.417,15.36L22.679,16.345C22.687,16.377 22.716,16.399 22.749,16.399C22.782,16.399 22.81,16.376 22.817,16.344L23.013,15.358C23.016,15.345 23.019,15.331 23.022,15.319L24.329,10.115C24.42,9.753 24.745,9.5 25.117,9.5L25.492,9.5C25.919,9.5 26.265,9.846 26.265,10.274C26.265,10.341 26.257,10.409 26.239,10.474L23.982,18.898C23.887,19.253 23.565,19.5 23.198,19.5L22.577,19.5C22.226,19.5 21.914,19.274 21.805,18.941L20.416,14.703C20.411,14.689 20.407,14.674 20.403,14.66L20.127,13.633C20.119,13.601 20.09,13.578 20.056,13.578C20.023,13.578 19.994,13.601 19.985,13.633L19.71,14.66C19.706,14.674 19.702,14.689 19.697,14.703L18.308,18.941C18.199,19.274 17.887,19.5 17.536,19.5L16.908,19.5C16.544,19.5 16.224,19.257 16.126,18.906L13.771,10.471C13.657,10.064 13.895,9.642 14.302,9.528C14.369,9.509 14.439,9.5 14.508,9.5Z"
      android:strokeWidth="1"
      android:fillColor="#0CACFD"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M11,26L22,26"
      android:strokeAlpha="0.1999628"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#0CACFD"
      android:fillType="evenOdd"
      android:fillAlpha="0.1999628"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M11,30L29,30"
      android:strokeAlpha="0.1999628"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#0CACFD"
      android:fillType="evenOdd"
      android:fillAlpha="0.1999628"
      android:strokeLineCap="round"/>
</vector>
