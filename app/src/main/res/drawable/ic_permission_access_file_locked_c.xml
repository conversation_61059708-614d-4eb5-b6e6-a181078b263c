<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="40dp"
    android:height="40dp"
    android:viewportWidth="40"
    android:viewportHeight="40">
  <path
      android:pathData="M11.668,9.948L30.986,4.771A3,3 128.665,0 1,34.661 6.892L36.213,12.688A3,3 53.779,0 1,34.092 16.362L14.774,21.539A3,3 120.95,0 1,11.099 19.417L9.546,13.622A3,3 132.739,0 1,11.668 9.948z"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M11.974,10.125L30.809,5.078A3,3 126.35,0 1,34.483 7.199L35.907,12.512A3,3 46.909,0 1,33.786 16.186L14.95,21.233A3,3 115.346,0 1,11.276 19.111L9.852,13.799A3,3 105.524,0 1,11.974 10.125z"
      android:strokeAlpha="0.2032889"
      android:strokeWidth="0.5"
      android:fillType="evenOdd"
      android:strokeColor="#FF7E00">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="22.88"
          android:startY="21.335"
          android:endX="22.88"
          android:endY="5"
          android:type="linear">
        <item android:offset="0" android:color="#00F17D27"/>
        <item android:offset="1" android:color="#33F28A26"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M7,5L11.974,5C12.639,5 13.285,5.221 13.811,5.628L17.354,8.372C17.88,8.779 18.526,9 19.191,9L33,9C35.209,9 37,10.791 37,13L37,32C37,34.209 35.209,36 33,36L7,36C4.791,36 3,34.209 3,32L3,9C3,6.791 4.791,5 7,5Z"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M7,5L11.974,5C12.639,5 13.285,5.221 13.811,5.628L17.354,8.372C17.88,8.779 18.526,9 19.191,9L33,9C35.209,9 37,10.791 37,13L37,32C37,34.209 35.209,36 33,36L7,36C4.791,36 3,34.209 3,32L3,9C3,6.791 4.791,5 7,5Z"
      android:strokeWidth="1.5"
      android:strokeColor="#FF7E00"
      android:fillType="nonZero">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="20"
          android:startY="36"
          android:endX="20"
          android:endY="5.047"
          android:type="linear">
        <item android:offset="0" android:color="#00F17D27"/>
        <item android:offset="1" android:color="#33F28A26"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M17.5,22L17.5,19.5C17.5,18.119 18.619,17 20,17C21.381,17 22.5,18.119 22.5,19.5L22.5,22L22.5,22"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#FF7E00"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M16.6,20L23.4,20A1.5,1.5 0,0 1,24.9 21.5L24.9,25.5A1.5,1.5 0,0 1,23.4 27L16.6,27A1.5,1.5 0,0 1,15.1 25.5L15.1,21.5A1.5,1.5 0,0 1,16.6 20z"
      android:strokeWidth="1"
      android:fillColor="#FF7E00"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
</vector>
