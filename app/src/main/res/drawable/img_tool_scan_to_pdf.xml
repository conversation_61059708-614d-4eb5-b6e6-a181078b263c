<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="54dp"
    android:height="54dp"
    android:viewportWidth="54"
    android:viewportHeight="54">
  <path
      android:pathData="M8,0L46,0A8,8 0,0 1,54 8L54,46A8,8 0,0 1,46 54L8,54A8,8 0,0 1,0 46L0,8A8,8 0,0 1,8 0z"
      android:strokeWidth="1"
      android:fillColor="#F4F4F4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M15,11L39,11A4,4 0,0 1,43 15L43,39A4,4 0,0 1,39 43L15,43A4,4 0,0 1,11 39L11,15A4,4 0,0 1,15 11z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="13.147"
          android:startY="12.466"
          android:endX="43"
          android:endY="41.154"
          android:type="linear">
        <item android:offset="0" android:color="#FF1FDD72"/>
        <item android:offset="1" android:color="#FF73E9AB"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M15,11L39,11A4,4 0,0 1,43 15L43,39A4,4 0,0 1,39 43L15,43A4,4 0,0 1,11 39L11,15A4,4 0,0 1,15 11z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="27"
          android:startY="43"
          android:endX="27"
          android:endY="11.134"
          android:type="linear">
        <item android:offset="0" android:color="#00FFFFFF"/>
        <item android:offset="0.985" android:color="#33666666"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M15,11L39,11A4,4 0,0 1,43 15L43,39A4,4 0,0 1,39 43L15,43A4,4 0,0 1,11 39L11,15A4,4 0,0 1,15 11z"/>
    <path
        android:pathData="M15.986,19.667L15.986,17C15.986,16.448 16.434,16 16.986,16L20.264,16L20.264,16"
        android:strokeLineJoin="round"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#FFFFFF"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
  </group>
  <group>
    <clip-path
        android:pathData="M56.083,11L32.083,11A4,4 0,0 0,28.083 15L28.083,39A4,4 0,0 0,32.083 43L56.083,43A4,4 0,0 0,60.083 39L60.083,15A4,4 0,0 0,56.083 11z"/>
    <path
        android:pathData="M37.681,19.667L37.681,17C37.681,16.448 37.233,16 36.681,16L33.403,16L33.403,16"
        android:strokeLineJoin="round"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#FFFFFF"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
  </group>
  <group>
    <clip-path
        android:pathData="M15,59.333L39,59.333A4,4 0,0 0,43 55.333L43,31.333A4,4 0,0 0,39 27.333L15,27.333A4,4 0,0 0,11 31.333L11,55.333A4,4 0,0 0,15 59.333z"/>
    <path
        android:pathData="M15.986,33.333L15.986,36C15.986,36.552 16.434,37 16.986,37L19.653,37L19.653,37"
        android:strokeLineJoin="round"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#FFFFFF"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
  </group>
  <group>
    <clip-path
        android:pathData="M55.472,59.333L31.472,59.333A4,4 0,0 1,27.472 55.333L27.472,31.333A4,4 0,0 1,31.472 27.333L55.472,27.333A4,4 0,0 1,59.472 31.333L59.472,55.333A4,4 0,0 1,55.472 59.333z"/>
    <path
        android:pathData="M37.069,33.333L37.069,36C37.069,36.552 36.622,37 36.069,37L33.403,37L33.403,37"
        android:strokeLineJoin="round"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#FFFFFF"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
  </group>
  <group>
    <clip-path
        android:pathData="M15,11L39,11A4,4 0,0 1,43 15L43,39A4,4 0,0 1,39 43L15,43A4,4 0,0 1,11 39L11,15A4,4 0,0 1,15 11z"/>
    <path
        android:pathData="M18,26.5L36,26.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#FFFFFF"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
  </group>
  <group>
    <clip-path
        android:pathData="M15,11L39,11A4,4 0,0 1,43 15L43,39A4,4 0,0 1,39 43L15,43A4,4 0,0 1,11 39L11,15A4,4 0,0 1,15 11z"/>
    <path
        android:pathData="M32,29L40,29A3,3 0,0 1,43 32L43,40A3,3 0,0 1,40 43L32,43A3,3 0,0 1,29 40L29,32A3,3 0,0 1,32 29z"
        android:strokeWidth="1"
        android:fillColor="#02AC4C"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
    <path
        android:pathData="M36.76,37.567C36.247,37.7 35.737,37.861 35.23,38.038L34.724,38.221C34.015,39.482 33.338,40.143 32.711,40.143C32.552,40.143 32.4,40.107 32.318,40.041C32.032,39.91 31.857,39.621 31.857,39.309C31.857,38.782 32.382,38.25 34.03,37.57C34.417,36.863 34.745,36.123 35.03,35.321L35.178,34.885L35.108,34.74C34.671,33.813 34.459,32.967 34.703,32.406L34.763,32.291C34.887,32.062 35.108,31.912 35.357,31.87L35.466,31.858L35.577,31.86C35.844,31.86 36.109,31.992 36.27,32.209C36.577,32.634 36.566,33.445 36.252,34.58L36.197,34.77L36.325,34.995C36.594,35.448 36.918,35.866 37.289,36.243L37.466,36.415L37.662,36.381C38.008,36.322 38.313,36.287 38.606,36.275L38.824,36.271C39.708,36.289 40.128,36.644 40.143,37.119L40.141,37.215C40.141,37.738 39.841,38.008 39.356,38.087C39.242,38.106 39.146,38.113 39.004,38.115L38.877,38.115C38.305,38.073 37.765,37.877 37.315,37.544L37.208,37.459L36.76,37.567ZM35.787,36.17L35.705,36.376C35.596,36.639 35.484,36.893 35.369,37.14L35.321,37.235L35.435,37.197C35.689,37.114 35.95,37.037 36.207,36.969L36.4,36.922L36.361,36.881C36.162,36.664 35.976,36.437 35.813,36.208L35.787,36.17Z"
        android:strokeWidth="1"
        android:fillType="nonZero"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="36"
            android:startY="32.162"
            android:endX="36"
            android:endY="40.143"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#CDFFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
