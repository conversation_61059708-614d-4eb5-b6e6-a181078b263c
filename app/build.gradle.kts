import com.google.firebase.crashlytics.buildtools.gradle.CrashlyticsExtension
import java.util.Properties

plugins {
  alias(libs.plugins.lumo)
  alias(libs.plugins.compose.compiler)
  alias(libs.plugins.androidApplication)
  alias(libs.plugins.jetbrainsKotlinAndroid)
  alias(libs.plugins.kotlin.parcelize)
  alias(libs.plugins.kotlin.serialization)
  alias(libs.plugins.ktorfit)
  alias(libs.plugins.ksp)
  alias(libs.plugins.google.services)
  alias(libs.plugins.firebase.crashlytics)
}

apply(from = "${rootProject.projectDir}/conf4build/copyProject.gradle")
apply(from = "${rootProject.projectDir}/conf4build/copyAabWhenBundleFinish.gradle")

val confProp = Properties()
file("${rootProject.projectDir}/conf4build/buildConf.properties").inputStream().use {
  confProp.load(it)
}

android {
  namespace = "com.example.pdf"
  compileSdk = 36

  defaultConfig {
    applicationId = "com.example.pdf"
    minSdk = 23
    targetSdk = 35
    versionCode = 1
    versionName = "1.0"

    testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

    ndk {
      abiFilters.addAll(setOf("arm64-v8a"))
    }
  }

  signingConfigs {
    create("internal") {
      storeFile = rootProject.file("test_sign")
      storePassword = "test_sign"
      keyAlias = "test_sign"
      keyPassword = "test_sign"
    }
  }

  buildTypes {
    create("internal") {
      isMinifyEnabled = true
      isShrinkResources = true
      signingConfig = signingConfigs.getByName("internal")
      proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
      configure<CrashlyticsExtension> {
        mappingFileUploadEnabled = false
      }
    }
  }
  compileOptions {
    isCoreLibraryDesugaringEnabled = true
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
  }
  kotlin {
    jvmToolchain(17)
    sourceSets {
      all {
        languageSettings {
          optIn("androidx.compose.material3.ExperimentalMaterial3Api")
          optIn("androidx.compose.foundation.ExperimentalFoundationApi")
          optIn("androidx.compose.foundation.layout.ExperimentalLayoutApi")
          optIn("androidx.compose.ui.text.ExperimentalTextApi")
          optIn("com.google.accompanist.permissions.ExperimentalPermissionsApi")
          optIn("kotlinx.coroutines.DelicateCoroutinesApi")
          optIn("org.orbitmvi.orbit.annotation.OrbitExperimental")
          optIn("kotlinx.serialization.ExperimentalSerializationApi")
        }
      }
    }
  }
  buildFeatures {
    viewBinding = true
    compose = true
    buildConfig = true
  }
}

dependencies {
  implementation(libs.androidx.cardview)
  implementation(libs.androidx.constraintlayout)
  coreLibraryDesugaring(libs.desugar.jdk.libs)

  implementation(libs.androidx.core.ktx)
  implementation(libs.androidx.startup.runtime)
//  implementation(libs.androidx.work.runtime)
  implementation(libs.androidx.lifecycle.runtime.ktx)
  implementation(libs.androidx.lifecycle.process)
  implementation(libs.androidx.lifecycle.runtime.compose)
  implementation(libs.androidx.lifecycle.viewmodel.ktx)
  implementation(libs.androidx.lifecycle.viewmodel.compose)
  implementation(libs.androidx.activity.compose)
  implementation(platform(libs.androidx.compose.bom))
  implementation(libs.androidx.ui)
  implementation(libs.androidx.ui.viewbinding)
  implementation(libs.androidx.ui.graphics)
  implementation(libs.androidx.ui.tooling.preview)
  implementation(libs.androidx.material.icons.extended)
  implementation(libs.androidx.material3)

  implementation(libs.accompanist.permissions)
  implementation(libs.accompanist.systemuicontroller)

  implementation(libs.bottomsheetdialog.compose)

  implementation(libs.resaca)
  implementation(libs.resacakoin)

  implementation(platform(libs.koin.bom))
  implementation(libs.koin.core)
  implementation(libs.koin.android)
  implementation(libs.koin.androidx.compose)

  implementation(libs.koin.annotations)
  ksp(libs.insert.koin.koin.ksp.compiler)

  implementation(libs.kotlinx.coroutines.core)
  implementation(libs.kotlinx.coroutines.android)
  implementation(libs.kotlinx.datetime)
  implementation(libs.kotlinx.serialization.json)

  implementation(libs.guia)

  implementation(libs.mmkv)

  implementation(libs.kotlinx.coroutines.core)
  implementation(libs.kotlinx.coroutines.android)
  implementation(libs.kotlinx.datetime)
  implementation(libs.kotlinx.serialization.json)

  implementation(libs.lottie.compose)

  implementation(libs.lyricist)
  ksp(libs.lyricist.processor)

  implementation(libs.orbit.viewmodel)
  implementation(libs.orbit.compose)

  implementation(libs.kermit)

  implementation(libs.coil)
  implementation(libs.coil.compose)
  implementation(libs.coil.network.okhttp)
  implementation(libs.coil.gif)

  ksp(libs.ktorfit.ksp)
  implementation(libs.ktorfit.lib.light)
  implementation(libs.ktorfit.converters.response)
  implementation(libs.ktor.client.okhttp)
  implementation(libs.ktor.client.content.negotiation)
  implementation(libs.ktor.serialization.kotlinx.json)

  implementation(libs.commons.codec)

  implementation(platform(libs.firebase.bom))
  implementation(libs.firebase.crashlytics)
  implementation(libs.firebase.analytics)
  implementation(libs.firebase.config)
  implementation(libs.firebase.messaging)

  implementation(libs.review.ktx)
  implementation(libs.installreferrer)
  implementation(libs.play.services.base)
  implementation(libs.play.services.ads.identifier)
  implementation(libs.play.services.mlkit.document.scanner)

  implementation(libs.reorderable)


  implementation(libs.compose.webview.multiplatform)
  implementation(libs.pdfviewer.compose)
//  implementation(libs.pdfviewer.compose.ui)
  implementation(libs.krop.ui)
  implementation("io.ak1:drawbox:1.0.3")

//  implementation("com.artifex.mupdf:viewer:1.25.+")
//  implementation("com.artifex.mupdf:mini:1.25.+")
//  implementation("com.artifex.mupdf:fitz:1.25.+")

  implementation("com.aspose:aspose-words:23.3:android.via.java")
//  implementation("org.bouncycastle:bc-fips:2.1.0")
//  implementation("org.bouncycastle:bctls-fips:2.1.20")

//  implementation("com.github.Victor2018:DocViewer:3.0.4")
//  implementation(files("libs/new.aar"))
  implementation(files("libs/new2.aar"))

  // PdfBox for PDF password operations
  implementation("com.tom-roush:pdfbox-android:********")
  implementation("org.bouncycastle:bcprov-jdk15to18:1.81")
  implementation("org.bouncycastle:bcpkix-jdk15to18:1.81")
  implementation("org.bouncycastle:bcutil-jdk15to18:1.81")



  testImplementation(libs.junit)
  androidTestImplementation(libs.androidx.junit)
  androidTestImplementation(libs.androidx.espresso.core)
  androidTestImplementation(platform(libs.androidx.compose.bom))
  androidTestImplementation(libs.androidx.ui.test.junit4)
  debugImplementation(libs.androidx.ui.tooling)
  debugImplementation(libs.androidx.ui.test.manifest)


  implementation(libs.tenjin)

  // Google Ads (Admob)
  implementation("com.google.android.gms:play-services-ads:24.5.0")
  implementation("com.google.ads.mediation:facebook:********")
  implementation("com.google.ads.mediation:mintegral:16.9.71.0")
  implementation("com.google.ads.mediation:pangle:7.3.0.3.0")
  implementation("com.google.ads.mediation:vungle:7.5.0.1")
  implementation("com.bigossp:admob-mediation:5.3.0.1")

  // TradPlus
  implementation("com.tradplusad:tradplus:14.4.0.1")
//noinspection GradleCompatible
  implementation("androidx.legacy:legacy-support-v4:1.0.0")
  // Admob (TP)
  implementation("com.tradplusad:tradplus-googlex:2.14.4.0.1")
  // Meta
  implementation("com.facebook.android:audience-network-sdk:6.20.0")
  implementation("com.tradplusad:tradplus-facebook:1.14.4.0.1")
  // Pangle
  implementation("com.tradplusad:tradplus-pangle:19.14.4.0.1")
  implementation("com.pangle.global:pag-sdk:7.2.0.6")
  // Fyber
  implementation("com.fyber:marketplace-sdk:8.3.7")
  implementation("com.tradplusad:tradplus-fyber:24.14.4.0.1")
  // Mintegral
  implementation("com.tradplusad:tradplus-mintegralx_overseas:18.14.4.0.1")
  implementation("com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71")
  // Liftoff
  implementation("com.tradplusad:tradplus-vunglex:7.14.4.0.1")
  implementation("com.vungle:vungle-ads:7.5.0")
  // Bigo
  implementation("com.bigossp:bigo-ads:5.3.0")
  implementation("com.tradplusad:tradplus-bigo:57.14.4.0.1")
  // Cross Promotion
  implementation("com.tradplusad:tradplus-crosspromotion:27.14.4.0.1")
  // TP Exchange
  implementation("com.tradplusad:tp_exchange:40.14.4.0.1")

  // For TradPlus SDK
  implementation("com.google.code.gson:gson:2.13.1")
  implementation("androidx.appcompat:appcompat:1.7.1")
  implementation("androidx.recyclerview:recyclerview:1.4.0")

}