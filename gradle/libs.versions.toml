[versions]
accompanist = "0.37.3"
accompanistSys<PERSON>uicontroller = "0.36.0"
agp = "8.12.0"
bottomsheetdialogCompose = "1.6.0"
cardview = "1.0.0"
coil = "3.3.0"
composeWebviewMultiplatform = "2.0.2"
constraintlayout = "2.2.1"
crashlytics = "3.0.6"
desugarJdkLibs = "2.1.5"
firebaseBom = "34.1.0"
guia = "1.0.0-beta05"
googleServices = "4.4.3"
kache = "2.1.1"
kermit = "2.0.6"
koinAnnotations = "2.1.0"
kotlin = "2.2.0"
ksp = "2.2.0-2.0.2"
coreKtx = "1.16.0"
junit = "4.13.2"
junitVersion = "1.3.0"
espressoCore = "3.7.0"
kotlinxCoroutinesCore = "1.10.2"
kotlinxDatetime = "0.6.2"
kotlinxSerialization = "1.9.0"
ktor = "3.2.1"
ktorfit-ksp = "2.6.4"
ktorfit = "2.6.4"
lottie = "6.6.7"
lyricist = "1.7.0"
mmkv = "2.2.2"
orbit = "10.0.0"
lifecycle = "2.9.2"
lumo = "1.2.5"
activityCompose = "1.10.1"
composeBom = "2025.07.00"
pdfviewer = "1.1.0-beta08"
playServicesAdsIdentifier = "18.2.0"
playServicesBase = "18.7.2"
playServicesMlkitDocumentScanner = "16.0.0-beta1"
reorderable = "2.5.1"
resaca = "4.5.0"
koinBom = "4.1.0"
reviewKtx = "2.0.2"
startupRuntime = "1.2.0"
krop = "0.2.0"
uiTextGoogleFonts = "1.8.3"
installreferrer = "2.2"
tenjin = "1.16.7"
workRuntime = "2.10.3"
storage = "1.6.0"
appcompat = "1.7.1"
material = "1.12.0"

[libraries]
accompanist-permissions = { module = "com.google.accompanist:accompanist-permissions", version.ref = "accompanist" }
accompanist-systemuicontroller = { module = "com.google.accompanist:accompanist-systemuicontroller", version.ref = "accompanistSystemuicontroller" }
androidx-cardview = { module = "androidx.cardview:cardview", version.ref = "cardview" }
androidx-constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version.ref = "constraintlayout" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-startup-runtime = { module = "androidx.startup:startup-runtime", version.ref = "startupRuntime" }
androidx-ui-text-google-fonts = { module = "androidx.compose.ui:ui-text-google-fonts", version.ref = "uiTextGoogleFonts" }
androidx-ui-viewbinding = { module = "androidx.compose.ui:ui-viewbinding" }
androidx-work-runtime = { module = "androidx.work:work-runtime", version.ref = "workRuntime" }
bottomsheetdialog-compose = { module = "com.holix.android:bottomsheetdialog-compose", version.ref = "bottomsheetdialogCompose" }
coil = { module = "io.coil-kt.coil3:coil", version.ref = "coil" }
coil-compose = { module = "io.coil-kt.coil3:coil-compose", version.ref = "coil" }
coil-gif = { module = "io.coil-kt.coil3:coil-gif", version.ref = "coil" }
coil-network-okhttp = { module = "io.coil-kt.coil3:coil-network-okhttp", version.ref = "coil" }
commons-codec = { module = "commons-codec:commons-codec", version = "1.19.0" }
compose-webview-multiplatform = { module = "io.github.kevinnzou:compose-webview-multiplatform", version.ref = "composeWebviewMultiplatform" }
desugar_jdk_libs = { module = "com.android.tools:desugar_jdk_libs", version.ref = "desugarJdkLibs" }
firebase-bom = { module = "com.google.firebase:firebase-bom", version.ref = "firebaseBom" }
firebase-crashlytics = { group = "com.google.firebase", name = "firebase-crashlytics" }
firebase-analytics = { group = "com.google.firebase", name = "firebase-analytics" }
firebase-config = { group = "com.google.firebase", name = "firebase-config" }
firebase-messaging = { group = "com.google.firebase", name = "firebase-messaging" }
guia = { module = "com.roudikk.guia:guia", version.ref = "guia" }
insert-koin-koin-ksp-compiler = { module = "io.insert-koin:koin-ksp-compiler", version.ref = "koinAnnotations" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycle" }
androidx-lifecycle-process = { group = "androidx.lifecycle", name = "lifecycle-process", version.ref = "lifecycle" }
androidx-lifecycle-runtime-compose = { group = "androidx.lifecycle", name = "lifecycle-runtime-compose", version.ref = "lifecycle" }
androidx-lifecycle-viewmodel-ktx = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "lifecycle" }
androidx-lifecycle-viewmodel-compose = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-compose", version.ref = "lifecycle" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-material-icons-extended = { group = "androidx.compose.material", name = "material-icons-extended" }
androidx-material3 = { group = "androidx.compose.material3", name = "material3" }
kache = { module = "com.mayakapps.kache:kache", version.ref = "kache" }
kermit = { module = "co.touchlab:kermit", version.ref = "kermit" }
koin-annotations = { module = "io.insert-koin:koin-annotations", version.ref = "koinAnnotations" }
koin-bom = { group = "io.insert-koin", name = "koin-bom", version.ref = "koinBom" }
koin-core = { group = "io.insert-koin", name = "koin-core" }
koin-android = { group = "io.insert-koin", name = "koin-android" }
koin-androidx-compose = { group = "io.insert-koin", name = "koin-androidx-compose" }
koin-ksp-compiler = { module = "io.insert-koin:koin-ksp-compiler" }
kotlinx-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "kotlinxCoroutinesCore" }
kotlinx-coroutines-android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "kotlinxCoroutinesCore" }
kotlinx-datetime = { module = "org.jetbrains.kotlinx:kotlinx-datetime", version.ref = "kotlinxDatetime" }
kotlinx-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinxSerialization" }
ktor-client-content-negotiation = { module = "io.ktor:ktor-client-content-negotiation", version.ref = "ktor" }
ktor-client-okhttp = { module = "io.ktor:ktor-client-okhttp", version.ref = "ktor" }
ktor-serialization-kotlinx-json = { module = "io.ktor:ktor-serialization-kotlinx-json", version.ref = "ktor" }
ktorfit-ksp = { module = "de.jensklingenberg.ktorfit:ktorfit-ksp", version.ref = "ktorfit-ksp" }
ktorfit-lib-light = { module = "de.jensklingenberg.ktorfit:ktorfit-lib-light", version.ref = "ktorfit" }
ktorfit-converters-response	 = { module = "de.jensklingenberg.ktorfit:ktorfit-converters-response", version.ref = "ktorfit" }
lottie-compose = { module = "com.airbnb.android:lottie-compose", version.ref = "lottie" }
lyricist = { module = "cafe.adriel.lyricist:lyricist", version.ref = "lyricist" }
lyricist-processor = { module = "cafe.adriel.lyricist:lyricist-processor", version.ref = "lyricist" }
mmkv = { module = "com.tencent:mmkv", version.ref = "mmkv" }
orbit-compose = { module = "org.orbit-mvi:orbit-compose", version.ref = "orbit" }
orbit-viewmodel = { module = "org.orbit-mvi:orbit-viewmodel", version.ref = "orbit" }
pdfviewer-compose-ui = { module = "com.github.bhuvaneshw.pdfviewer:compose-ui", version.ref = "pdfviewer" }
pdfviewer-compose = { module = "com.github.bhuvaneshw.pdfviewer:compose", version.ref = "pdfviewer" }
play-services-ads-identifier = { module = "com.google.android.gms:play-services-ads-identifier", version.ref = "playServicesAdsIdentifier" }
play-services-base = { module = "com.google.android.gms:play-services-base", version.ref = "playServicesBase" }
play-services-mlkit-document-scanner = { module = "com.google.android.gms:play-services-mlkit-document-scanner", version.ref = "playServicesMlkitDocumentScanner" }
reorderable = { module = "sh.calvin.reorderable:reorderable", version.ref = "reorderable" }
resaca = { module = "io.github.sebaslogen:resaca", version.ref = "resaca" }
resacakoin = { module = "io.github.sebaslogen:resacakoin", version.ref = "resaca" }
review-ktx = { module = "com.google.android.play:review-ktx", version.ref = "reviewKtx" }
tenjin = { module = "com.tenjin:android-sdk", version.ref = "tenjin" }
installreferrer = { group = "com.android.installreferrer", name = "installreferrer", version.ref = "installreferrer" }
androidx-storage = { group = "androidx.test.services", name = "storage", version.ref = "storage" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
krop-ui = { module = "com.attafitamim.krop:ui", version.ref = "krop" }


[plugins]
androidApplication = { id = "com.android.application", version.ref = "agp" }
androidLibrary = { id = "com.android.library", version.ref = "agp" }
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
jetbrainsKotlinAndroid = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-parcelize = { id = "org.jetbrains.kotlin.plugin.parcelize", version.ref = "kotlin" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
ktorfit = { id = "de.jensklingenberg.ktorfit", version.ref = "ktorfit" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
google-services = { id = "com.google.gms.google-services", version.ref = "googleServices" }
firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "crashlytics" }
lumo = { id = "com.nomanr.plugin.lumo", version.ref = "lumo" }

