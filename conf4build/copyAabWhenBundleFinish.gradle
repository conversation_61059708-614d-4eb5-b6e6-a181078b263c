def confProp = new Properties()
confProp.load((new FileInputStream(file("${rootProject.projectDir}/conf4build/buildConf.properties"))))

def versionCode = confProp.getProperty("versionCode")
def versionName = confProp.getProperty("versionName")
def buildFlavor = confProp.getProperty("buildFlavor")

tasks.register('copyAabWhenBundleFinish', Copy) {
    def commitId = gitCurrentBranchCurrentCommitId("main").substring(0, 6)

    def outAabName = "hi_scan_vc_${versionCode}_${versionName}_${commitId}.aab"

    from "$rootDir/app/build/outputs/bundle/$buildFlavor/app-${buildFlavor}.aab"
    into "$rootDir/buildOut/$buildFlavor"

    rename { String fileName -> outAabName }

    println "copy aab: $outAabName"
}

tasks.register('copyMappingFileWhenBundleFinish', Co<PERSON>) {
    from "$rootDir/app/build/outputs/mapping/$buildFlavor/mapping.txt"
    into "$rootDir/buildOut/$buildFlavor/mapping"

    rename { String fileName -> "mapping_${versionCode}.txt" }
}

tasks.configureEach { Task task ->
    if (task.name == "bundle${initialUpperCase(buildFlavor)}") {
        task.finalizedBy(copyAabWhenBundleFinish)
        task.finalizedBy(copyMappingFileWhenBundleFinish)
    }
}

static def initialUpperCase(String string) {
    def initialChar = string.toUpperCase().substring(0, 1).toCharArray()[0]
    def charArray = string.toCharArray()
    charArray[0] = initialChar

    return charArray.toString()
}

static def gitCurrentBranchCurrentCommitId(String branch) {
    def cmdLine = "git rev-parse $branch"
    def commitId = cmdLine.execute().text.trim()
    return commitId
}