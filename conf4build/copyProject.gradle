def confProp = new Properties()
confProp.load((new FileInputStream(file("${rootProject.projectDir}/conf4build/copyProjectConf.properties"))))

def projectPackageName = confProp.getProperty("projectPackageName")
def targetPackageName = confProp.getProperty("targetPackageName")

tasks.register('replaceProjectPackageName') {
    doFirst {
        FileTree tree = fileTree(dir: 'src/main')
        tree.include '**/*.java'
        tree.include '**/*.kt'
        tree.include '**/*.xml'
        tree.each { File mFile ->
            fileReader(mFile.path, projectPackageName, targetPackageName)
        }
        fileReader("proguard-rules.pro", projectPackageName, targetPackageName)
        fileReader("build.gradle.kts", projectPackageName, targetPackageName)
    }
}

def fileReader(path, projectPackageName, targetPackageName) {
    def readerString = "";
    def hasReplace = false
    //当前项目中的包名
    def currentPackageName = projectPackageName

    file(path).withReader('UTF-8') { reader ->
        reader.eachLine {
            if (it.find(currentPackageName)) {
                it = it.replace(currentPackageName, targetPackageName)
                hasReplace = true
            }
            readerString <<= it
            readerString << '\n'
        }

        if (hasReplace) {
            println(path + " Replacement completed. ")
            file(path).withWriter('UTF-8') {
                within ->
                    within.append(readerString)
            }
        }
        return readerString
    }
}

tasks.register('replaceProjectPackageAndDirName', Copy) {
//    dependsOn 'clean'
//    dependsOn replaceProjectPackageName

    from "$projectDir/src/main/java/${projectPackageName.replace(".", "/")}"
    into "$projectDir/src/main/java/${targetPackageName.replace(".", "/")}"
//    from rootDir
//    into "${rootDir.parent}/${rootProject.name}_RM".replace(" ","_")

    filter { String line ->
        if (line.find(projectPackageName)) {
            line = line.replace(projectPackageName, targetPackageName)
        }
        "$line"
    }

    doLast {
        new File("app/src/main/java/com/example").deleteDir()
    }
}

tasks.register('mirrorToRemoteDir', Copy) {
    def remoteDir = "${rootDir.parent}/pdf_RM".replace(" ", "_")

    from rootDir
    into remoteDir

    doLast {
        new File("$remoteDir/app/src/main/java/com/example").deleteDir()
    }
}
