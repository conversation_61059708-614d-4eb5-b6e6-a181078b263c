# Remote Config
## use_legacy_ad_config
```text
true: use ad_config_1 (applovin max ad)
false: use ad_config_2 (admob ad)
```


## ad_config_1 (for applovin max ad)
```json
{
  "sdk_key": "",
  "app_open_ad_key": "",
  "banner_ad_key": "",
  "native_ad_key": "",
  "inter_ad_key": "",
  "inter_ad_key2": "",
  "rewarded_ad_key": "",
  "app_open_ad_loading_timeout_seconds": 8,
  "inter_ad_loading_timeout_seconds": 5,
  "fullscreen_ad_show_interval_seconds": 60,
  "next_inter_ad_key_active_interval_minutes": -1
}
```

## ad_config_2 (for admob ad)
```json
{
  "app_open_ad_key": "",
  "banner_ad_key": "",
  "native_ad_key": "",
  "inter_ad_key": "",
  "inter_ad_key2": "",
  "rewarded_ad_key": "",
  "app_open_ad_loading_timeout_seconds": 8,
  "inter_ad_loading_timeout_seconds": 5,
  "fullscreen_ad_show_interval_seconds": 60,
  "next_inter_ad_key_active_interval_minutes": -1
}
```
